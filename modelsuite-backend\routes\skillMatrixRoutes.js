import express from "express";
import { verifyToken, verifyRole } from "../middlewares/authMiddleware.js";
import {
  // Skill Categories
  createSkillCategory,
  getSkillCategories,
  updateSkillCategory,
  deleteSkillCategory,
  
  // Skills
  createSkill,
  getSkills,
  updateSkill,
  deleteSkill,
  
  // User Skills
  updateUserSkill,
  getUserSkills,
  getSkillMatrix,
  endorseUserSkill,
  getSkillGaps,
} from "../controllers/skillMatrixController.js";

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Skill Category Routes
router.post("/categories", verifyRole("agency"), createSkillCategory);
router.get("/categories", getSkillCategories);
router.put("/categories/:id", verifyRole("agency"), updateSkillCategory);
router.delete("/categories/:id", verifyRole("agency"), deleteSkillCategory);

// Skill Routes
router.post("/skills", verifyRole("agency"), createSkill);
router.get("/skills", getSkills);
router.put("/skills/:id", verifyRole("agency"), updateSkill);
router.delete("/skills/:id", verifyRole("agency"), deleteSkill);

// User Skill Routes
router.put("/user-skills/:user_id/:skill_id", updateUserSkill);
router.get("/user-skills/:user_id", getUserSkills);
router.post("/user-skills/:user_id/:skill_id/endorse", endorseUserSkill);

// Skill Matrix Routes
router.get("/matrix/:agency_id", verifyRole("agency"), getSkillMatrix);
router.get("/gaps/:user_id", getSkillGaps);

export default router;
