import twilio from "twilio";

const client = twilio(process.env.TWILIO_SID, process.env.TWILIO_AUTH_TOKEN);

export const sendOtpToPhone = async (phone, otp) => {
  try {
    const message = await client.messages.create({
      body: `Your OTP for ModelSuite.ai is: ${otp}. It will expire in 10 minutes.`,
      from: process.env.TWILIO_PHONE,
      to: phone,
    });
    console.log("OTP sent via Twilio:", message.sid);
  } catch (error) {
    console.error("Failed to send OTP via Twilio:", error.message);
    throw new Error("SMS failed");
  }
};
