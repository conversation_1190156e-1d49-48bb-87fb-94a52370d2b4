import mongoose from "mongoose";

const dmMessageSchema = new mongoose.Schema({
  convoId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: "DmConversation",
  },
  senderId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: "User",
  },
  receiverId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: "User",
  },

  // Added fields
  text: { type: String, default: "" },
  attachments: [
    {
      fileId: { type: String },
      type: { type: String },
      name: { type: String },
      size: { type: Number },
      width: { type: Number },
      height: { type: Number },
      mimeType: { type: String },
      duration: { type: String, default: null },
      status: { type: String },
      progress: { type: Number },
      cloudinaryUrl: { type: String },
      publicId: { type: String },
    },
  ],
  type: {
    type: String,
    enum: ["textonly", "attachmentonly", "mixed", "system"],
    default: "textonly",
  },

  replyTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "DmMessage",
    default: null,
  },

  status: {
    type: String,
    enum: ["sent", "delivered", "seen", "error"],
    default: "sent",
  },
  deletedFor: [{ type: String, default: [] }],

  reactions: [
    {
      userId: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
      fullName: { type: String },
      avatar: { type: String },
      emoji: String,
    },
  ],
  edited: { type: Boolean, default: false },
  pinned: { type: Boolean, default: false },
  createdAt: { type: Date, default: Date.now },
});

export default mongoose.model("DmMessage", dmMessageSchema);
