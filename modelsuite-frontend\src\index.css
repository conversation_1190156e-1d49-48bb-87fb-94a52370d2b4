html,
body {
  padding: 0;
  margin: 0;
  width: 100vw;
  box-sizing: border-box;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-y: auto;
  overflow-x: hidden;
}

body {
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu,
    Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.3s ease;
}

/* Theme-aware body background */
body {
  background-color: #f9fafb; /* light mode - bg-gray-50 */
}

.dark body {
  background-color: #030712; /* dark mode - bg-gray-950 */
}

* {
  box-sizing: border-box;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.scrollbar-none::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollbar-none {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

/* Custom scrollbar for other elements */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1f2937; /* bg-gray-800 */
}

::-webkit-scrollbar-thumb {
  background: #374151; /* bg-gray-700 */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4b5563; /* bg-gray-600 */
}

@layer base {
  html,
  body,
  #root {
    @apply h-full m-0 p-0 overflow-hidden;
  }

  body {
    @apply bg-gray-900 text-gray-100;
  }
}

.input {
  @apply w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400;
}
.scrollBar {
  @apply scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-gray-900;
}
@layer utilities {
  .custom-scrollbar {
    scrollbar-width: thin; /* Use thin, looks more modern */
    scrollbar-color: #555 #1e1e1e; /* Dark gray thumb, darker track */
  }
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px; /* Thinner than 15px, less chunky */
    background: #1e1e1e; /* Track color */
  }
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #555; /* Thumb color */
    border-radius: 6px; /* Rounded corners look smoother */
    border: 2px solid #1e1e1e; /* Creates padding effect so thumb isn't stuck to edge */
  }
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #777; /* Lighter on hover for feedback */
  }
  /* Custom SortableJS utility classes */
  .chosen-blue-ring {
    /* mimics ring-2 and ring-blue-500 */
    box-shadow: 0 0 0 2px #3b82f6;
  }
  .drag-fit {
    /* mimics rotate-2, scale-105, and shadow-2xl */
    transform: rotate(2deg) scale(1.05);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.3);
  }
  .chosen-purple-ring {
    /* mimics ring-2 and ring-purple-500 */
    box-shadow: 0 0 0 2px #c084fc;
  }
  .drag-question {
    /* mimics rotate-1, scale-105, and shadow-xl */
    transform: rotate(1deg) scale(1.05);
    box-shadow: 0 8px 10px rgba(0, 0, 0, 0.2);
  }

  @keyframes shake-x {
    0%,
    100% {
      transform: translateX(0);
    }
    10%,
    50%,
    90% {
      transform: translateX(-4px);
    }
    30%,
    70%,
    100% {
      transform: translateX(4px);
    }
  }

  .shake-x {
    animation: shake-x 0.5s ease-in-out;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes avatar-in-out {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  60% {
    opacity: 1;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
.avatar-modal-in {
  animation: avatar-in-out 0.4s cubic-bezier(0.4, 0, 0.2, 1) both;
}
.avatar-modal-out {
  animation: avatar-in-out 0.3s cubic-bezier(0.4, 0, 0.2, 1) reverse both;
}
