import express from "express";
import {
  checkGoogleConnectionStatus,
  disconnectGoogleCalendar,
  generateGoogleAuthURL,
  handleGoogleCallback,
} from "../../controllers/google/googleController.js";
import { verifyToken } from "../../middlewares/authMiddleware.js";

const router = express.Router();

router.get("/auth/google", generateGoogleAuthURL);
router.get("/callback", handleGoogleCallback);
router.get("/status/:modelId", checkGoogleConnectionStatus);
router.delete("/disconnect", verifyToken, disconnectGoogleCalendar);

export default router;
