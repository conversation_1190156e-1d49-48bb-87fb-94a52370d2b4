import VoiceAssignment from "../../models/voice/VoiceAssignment.js";
// Removed unused imports for sections and templates
import { asyncHandler } from "../../utils/asyncHandler.js";
import { ApiError } from "../../utils/ApiError.js";
import { ApiResponse } from "../../utils/ApiResponse.js";

/**
 * Get dashboard data for model or agency
 * @route GET /api/v1/voice/dashboard
 * @access Model and Agency
 */
export const getDashboard = asyncHandler(async (req, res) => {
  if (req.user.role === "model") {
    // Recent assignments for model
    const recentAssignments = await VoiceAssignment.find({
      modelId: req.user._id,
      isDeleted: false,
    })
      .populate({
        path: 'questionIds',
        select: 'text sectionId',
        populate: {
          path: 'sectionId',
          select: 'title',
        },
      })
      .sort({ assignedAt: -1 })
      .limit(5)
      .lean();

    // Assignment statistics for model
    const assignmentStats = await VoiceAssignment.aggregate([
      { $match: { modelId: req.user._id, isDeleted: false } },
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
        },
      },
    ]);

    return res.status(200).json(
      new ApiResponse(200, { recentAssignments, assignmentStats }, "Model dashboard data retrieved successfully")
    );
  } else if (req.user.role === "agency") {
    // Recent assignments for agency
    const recentAssignments = await VoiceAssignment.find({
      agencyId: req.user._id,
      isDeleted: false,
    })
      .populate('modelId', 'fullName username')
      .sort({ assignedAt: -1 })
      .limit(5)
      .lean();

    // Assignment statistics for agency
    const assignmentStats = await VoiceAssignment.aggregate([
      { $match: { agencyId: req.user._id, isDeleted: false } },
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
        },
      },
    ]);

    // Pending reviews count for agency
    const pendingReviewsCount = await VoiceAssignment.countDocuments({
      agencyId: req.user._id,
      status: "submitted",
      isDeleted: false,
    });

    return res.status(200).json(
      new ApiResponse(200, { recentAssignments, assignmentStats, pendingReviewsCount }, "Agency dashboard data retrieved successfully")
    );
  }

  throw new ApiError(403, "Access denied");
});
