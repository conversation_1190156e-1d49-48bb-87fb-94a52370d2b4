import mongoose from "mongoose";

const groupSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, "Title is required!"],
  },
  agencyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Agency",
    required: [true, "Agency Id is required!"],
  },
  modelId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "ModelUser",
    required: [true, "Model User is required!"],
  },
  // employeeId: {
  //   type: mongoose.Schema.Types.ObjectId,
  //   ref: 'Employee',
  // },
  hasTopics: {
    type: Boolean,
    default: false,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  topics: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Topic",
    },
  ],
  groupTopicMessage: [
    { type: mongoose.Schema.Types.ObjectId, ref: "GroupTopicMessage" },
  ],
});

groupSchema.index({ modelId: 1 });
groupSchema.index({ agencyId: 1 });

export default mongoose.model("Group", groupSchema);
