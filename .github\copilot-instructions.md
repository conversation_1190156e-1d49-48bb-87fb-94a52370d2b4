# ModelSuite AI Agent Instructions

## Architecture Overview

- **Backend**: Node.js/Express API (`server.js`), feature controllers in `config/controllers/{feature}/`, Mongoose models in `models/{feature}/`, REST routes in `routes/{feature}Routes.js` (mounted at `/api/v1/{feature}`), real-time messaging via Socket.io (`sockets/`).
- **Frontend**: React 19 + Vite (`modelsuite-frontend/`), pages in `src/pages/{Model,Agency,Voice}`, layouts in `src/layouts`, shared UI in `src/components/ui`, feature components in `src/components/{feature}`. Tailwind CSS and PostCSS for styling.
- **Database**: MongoDB (Mongoose), connection in `db.js`.
- **Auth**: JWT-based, middleware in `middlewares/authMiddleware.js`, role checks in `middlewares/roleCheck.js` and `<ProtectedRoute allowedRole="...">` in frontend.
- **Real-time Messaging**: Socket.io client in `utils/socket.js`, server handlers in `sockets/index.js` and `sockets/messanger/`.

## Developer Workflows

- **Backend**: `cd modelsuite-backend; npm install; npm run dev` (port 3000)
- **Frontend**: `cd modelsuite-frontend; npm install; npm run dev` (port 4000)
- **Linting**: `npm run lint` in frontend
- **Testing**: `npm test` in backend

## File Structure & Patterns

**Backend:**

- Controllers: `config/controllers/{feature}/`
- Models: `models/{feature}/`
- Routes: `routes/{feature}Routes.js` (mounted at `/api/v1/{feature}`)
- Utilities: `utils/` (e.g., `asyncHandler.js`, `ApiError.js`, `tikapiClient.js`)
- Middlewares: `middlewares/` (auth, roles, file uploads)

**Frontend:**

- Pages: `src/pages/{Model,Agency,Voice}`
- Layouts: `src/layouts/`
- Shared UI: `src/components/ui/`
- Feature components: `src/components/{feature}/`
- State: Redux slices in `src/globalstate/`, context in `src/context/`
- API client: `src/utils/questionnaireApi.js` (JWT interceptor)

## Project-Specific Conventions

- **Async controllers**: Always wrap with `asyncHandler`, throw `new ApiError(status, message)` for errors
- **Role-based access**: Backend checks `req.user.role`, frontend uses `<ProtectedRoute allowedRole="...">`
- **Socket registration**: `socket.emit('register', {userId})` on client, server emits via `req.app.get('io').emit()`
- **File uploads**: Use Multer + Cloudinary (`middlewares/cloudinaryUpload.js`)
- **Default templates**: Seeded at startup (`utils/seedDefaultTemplate.js`)
- **Error responses**: Always return `{ error: 'message' }` JSON
- **API endpoints**: Prefix new endpoints with `/api/v1/`

## Integration Points

- **Social Media**: TikTok (`utils/tikapiClient.js`), Instagram/Facebook (`config/controllers/socialMedia/`)
- **Contracts**: PandaDoc (`config/controllers/pandadoccontroller.js`)
- **Google Calendar**: Client (`utils/googleCalendar.js`), server (`config/controllers/google/eventController.js`)
- **Billing**: Handlers (`config/controllers/billing/`), schemas (`models/billing/`)

## Cross-Component Communication

- **Voice features**: Shared via `VoiceRoutes` in frontend, backend routes for assignments and review
- **Real-time flows**: Confirm socket event names and payloads before implementing changes

## Examples

- **Backend route pattern**: `routes/voiceRoutes.js` → `/api/v1/voice` endpoints
- **Frontend protected route**: `<Route path="/agency/voice/*" element={<VoiceRoutes />} />`
- **Async error handling**: `throw new ApiError(404, "Not found")`

_If any section is unclear or missing, please provide feedback for further updates._
