import Note from "../models/Note.js";
import { ApiError } from "../utils/ApiError.js";
import { ApiResponse } from "../utils/ApiResponse.js";
import { asyncHandler } from "../utils/asyncHandler.js";

// Upload a file and attach it to a note
export const uploadAttachment = asyncHandler(async (req, res) => {
  const noteId = req.params.noteId;
  const file = req.file;
  if (!file) {
    throw new ApiError(400, "File is required");
  }

  // Build attachment object
  const attachment = {
    url: file.path,
    originalName: file.originalname,
    size: file.size,
    type: file.mimetype,
    uploadedAt: new Date(),
  };

  // Push into note
  const note = await Note.findById(noteId);
  if (!note) {
    throw new ApiError(404, "Note not found");
  }
  note.attachments.push(attachment);
  await note.save();

  res
    .status(200)
    .json(new ApiResponse(200, attachment, "Attachment uploaded and saved"));
});

// Get attachments for a note
export const getAttachments = asyncHandler(async (req, res) => {
  const noteId = req.params.noteId;
  const note = await Note.findById(noteId, "attachments");
  if (!note) {
    throw new ApiError(404, "Note not found");
  }

  res
    .status(200)
    .json(
      new ApiResponse(
        200,
        note.attachments,
        "Attachments retrieved successfully",
      ),
    );
});

// Delete an attachment by its id
export const deleteAttachment = asyncHandler(async (req, res) => {
  const { noteId, attachmentId } = req.params;
  const note = await Note.findById(noteId);
  if (!note) {
    throw new ApiError(404, "Note not found");
  }

  const index = note.attachments.findIndex(
    (att) => att._id.toString() === attachmentId,
  );
  if (index === -1) {
    throw new ApiError(404, "Attachment not found");
  }

  note.attachments.splice(index, 1);
  await note.save();

  res
    .status(200)
    .json(new ApiResponse(200, null, "Attachment deleted successfully"));
});
