import mongoose from "mongoose";

const eventSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      default: "",
    },
    start: {
      type: Date,
      required: true,
    },
    end: {
      type: Date,
      required: true,
    },
    allDay: {
      type: Boolean,
      default: false,
    },
    // Timezone support
    timezone: {
      type: String,
      default: "UTC",
    },
    // Recurrence support
    recurrence: {
      type: String,
      enum: ["none", "daily", "weekly", "monthly"],
      default: "none",
    },
    recurrenceEndDate: {
      type: Date,
      default: null,
    },
    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: true,
    },
    createdBy: {
      type: String,
      enum: ["ModelUser", "Agency"],
      required: true,
    },
    createdById: {
      type: mongoose.Schema.Types.ObjectId,
      refPath: "createdBy", // Will refer to 'Model' or 'Agency' collection dynamically
      required: true,
    },
  },
  {
    timestamps: true,
  },
);

const Event = mongoose.model("Event", eventSchema);
export default Event;
