import mongoose from "mongoose";

const skillSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    category_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "SkillCategory",
      required: true,
    },
    type: {
      type: String,
      enum: ["hard", "soft", "tool"],
      required: true,
    },
    proficiency_level: {
      type: String,
      enum: ["beginner", "intermediate", "advanced", "expert"],
      required: true,
      default: "beginner"
    },
    description: {
      type: String,
    },
    weight: {
      type: Number,
      default: 1,
      min: 0,
      max: 5,
    },
    required_levels_by_role: {
      type: Map,
      of: Number, // JSON object mapping role names to required levels
      default: {},
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

export default mongoose.model("Skill", skillSchema);
