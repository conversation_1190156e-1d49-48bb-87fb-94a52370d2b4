import mongoose from "mongoose";

const topicSchema = new mongoose.Schema({
  groupId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Group",
    required: [true, "Group ID is required"],
  },
  title: {
    type: String,
    required: [true, "Title is required"],
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    required: [true, "Created By is required"],
    refPath: "creatorModel",
  },
  creatorModel: {
    type: String,
    required: true,
    enum: ["Agency", "Employee"], // For now, only 'Agency'
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

topicSchema.index({ groupId: 1 });

export default mongoose.model("Topic", topicSchema);
