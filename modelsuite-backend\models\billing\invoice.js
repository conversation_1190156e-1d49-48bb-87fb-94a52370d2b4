import mongoose from "mongoose";

const invoiceSchema = new mongoose.Schema(
  {
    client: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: true,
    },
    campaign: { type: String },
    amount: { type: Number, required: true },
    currency: { type: String, required: true },
    dueDate: { type: Date, required: true },
    status: {
      type: String,
      enum: ["Paid", "Unpaid", "Failed"],
      required: true,
    },
    note: { type: String },
    fileUrl: { type: String, required: true },
    uploadedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: true,
    },
    transactionMethod: {
      type: String,
      enum: [
        "UPI",
        "Bank Transfer",
        "Cash",
        "SEPA",
        "Sofort",
        "PayPal",
        "Giropay",
        "Apple Pay",
        "Google Pay",
        "Credit Card",
      ],
    },
    screenshotUrl: { type: String },

    // ✅ Add this: List of payment attempts
    paymentAttempts: [
      {
        attemptAt: { type: Date, default: Date.now }, // timestamp
        success: { type: Boolean, required: true },
        reason: { type: String }, // Failure reason if not successful
      },
    ],
  },
  { timestamps: true },
);

// ✅ Existing virtual
invoiceSchema.virtual("dueStatus").get(function () {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const dueDate = new Date(this.dueDate);
  dueDate.setHours(0, 0, 0, 0);

  if (this.status === "Paid") {
    return dueDate >= today ? "Paid In Advance" : "Paid Late";
  }

  if (this.status === "Unpaid") {
    return dueDate < today ? "Overdue" : "Upcoming";
  }

  if (this.status === "Failed") {
    return "Payment Failed";
  }

  return "Unknown";
});

invoiceSchema.set("toJSON", { virtuals: true });

const Invoice = mongoose.model("Invoice", invoiceSchema);
export default Invoice;

// (async () => {
//   try {
//     const deleted = await Invoice.deleteMany({});
//     console.log(`[Invoice Cleanup] Deleted ${deleted.deletedCount} invoices.`);
//   } catch (error) {
//     console.error("[Invoice Cleanup Error]", error);
//   }
// })();

// for deletion
