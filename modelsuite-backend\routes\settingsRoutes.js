import express from "express";
import { verifyToken, verifyRole } from "../middlewares/authMiddleware.js";
import upload from "../middlewares/multer.js";
import {
  updateAgencyProfile,
  changeAgencyPassword,
  uploadAgencyAvatar,
} from "../controllers/settingsController.js";

const router = express.Router();

// POST /settings/update
router.post("/update", verifyToken, verifyRole("agency"), updateAgencyProfile);

// POST /settings/change-password
router.post(
  "/change-password",
  verifyToken,
  verifyRole("agency"),
  changeAgencyPassword
);

// POST /settings/upload-avatar
router.post(
  "/upload-avatar",
  verifyToken,
  verifyRole("agency"),
  upload.single("avatar"),
  uploadAgencyAvatar
);

export default router;
