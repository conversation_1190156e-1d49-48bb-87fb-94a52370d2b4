import express from "express";
import multer from "multer";
import path from "path";
import fs from "fs";
import { verifyToken, verifyRole } from "../../middlewares/authMiddleware.js";
import { verifyDownloadToken } from "../../middlewares/downloadTokenMiddleware.js";
import ffmpegConfig from "../../config/ffmpegConfig.js";
import { asyncHandler } from "../../utils/asyncHandler.js";
// ...existing imports...

// Question controllers
import {
  getSections,
  getQuestionsBySection,
  getAllQuestions,
  createSection,
  addQuestionToSection,
  getQuestionsStats,
  updateSection,
  deleteSection,
  updateQuestion,
  deleteQuestion,
} from "../../controllers/voice/questionController.js";

// Assignment controllers  
import {
  assignQuestions,
  getModelAssignments,
  getAgencyAssignments,
  getAssignmentById,
  startAssignment,
  submitAssignment,
  saveAssignmentProgress,
  cancelAssignment,
  getAssignmentStats,
  completeAssignmentReview,
} from "../../controllers/voice/voiceAssignmentController.js";

// Agency controllers
import { getAgencyModels } from "../../controllers/agencyController.js";

// Recording controllers
import {
  uploadRecording,
  getAssignmentRecordings,
  getRecordingById,
  getModelRecordings,
  getAgencyRecordings,
  getAllRecordingsDebug,
  reviewRecording,
  generateDownloadToken,
  downloadWithToken
} from "../../controllers/voice/voiceRecordingController.js";

// Script controllers
import {
  createScript,
  getAgencyScripts,
  getScriptById,
  updateScript,
  deleteScript,
} from "../../controllers/voice/voiceScriptController.js";

// Review controller imports
import {
  createReview,
  getAgencyReviews,
} from "../../controllers/voice/voiceReviewController.js";
import { getDashboard } from "../../controllers/voice/voiceDashboardController.js";

// Sentence-level controller imports
import {
  uploadSentenceRecording,
  getScriptSentenceRecordings,
  autoMatchSentenceRecording,
  getSentenceProgress,
} from "../../controllers/voice/voiceSentenceController.js";

// Model imports for statistics
import VoiceAssignment from "../../models/voice/VoiceAssignment.js";
import VoiceRecording from "../../models/voice/VoiceRecording.js";
import QuestionSection from "../../models/voice/QuestionSection.js";
import QuestionTemplate from "../../models/voice/QuestionTemplate.js";

const router = express.Router();

// Ensure uploads directory exists
const uploadsDir = 'uploads/';
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Apply authentication to all routes except secure download
router.use((req, res, next) => {
  // Skip auth for secure download route - it has its own token validation
  if (req.path.startsWith('/recordings/download/') && req.method === 'GET') {
    return next();
  }
  // Apply normal auth for all other routes
  return verifyToken(req, res, next);
});

// Storage configuration for audio uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/'); // Temporary storage before Cloudinary upload
  },
  filename: (req, file, cb) => {
    // Generate unique filename with timestamp
    const timestamp = Date.now();
    const extension = file.originalname.split('.').pop();
    cb(null, `temp_${timestamp}_${file.originalname}`);
  }
});

// File filter for audio files only
const fileFilter = (req, file, cb) => {
  if (file.mimetype.startsWith("audio/")) {
    cb(null, true);
  } else {
    cb(new Error("Only audio files are allowed!"), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB limit
    files: 1,
  },
});

// ============================================================================
// QUESTION SECTIONS ROUTES
// ============================================================================

// Get all sections (default + agency custom)
router.get("/sections", getSections);

// Get questions by section
router.get("/sections/:sectionId/questions", getQuestionsBySection);

// Get all questions for agency selection
router.get("/questions", verifyRole("agency"), getAllQuestions);

// Create custom section (agency only)
router.post("/sections", verifyRole("agency"), createSection);

// Create custom question (agency only)
router.post("/questions", verifyRole("agency"), addQuestionToSection);

// Get section statistics
router.get("/sections/stats", getQuestionsStats);

// Update custom section (agency only)
router.put(
  "/sections/:id",
  verifyRole("agency"),
  updateSection
);

// Delete custom section (agency only)
router.delete(
  "/sections/:id",
  verifyRole("agency"),
  deleteSection
);

// Update custom question (agency only)
router.put(
  "/questions/:questionId",
  verifyRole("agency"),
  updateQuestion
);

// Delete custom question (agency only)
router.delete(
  "/questions/:questionId",
  verifyRole("agency"),
  deleteQuestion
);

// ============================================================================
// SCRIPT ROUTES
// ============================================================================

// Create script (agency only)
router.post("/scripts", verifyRole("agency"), createScript);

// Get agency scripts (agency only)
router.get("/scripts", verifyRole("agency"), getAgencyScripts);

// Get script by ID (agency and assigned models)
router.get("/scripts/:id", getScriptById);

// Update script (agency only)
router.put("/scripts/:id", verifyRole("agency"), updateScript);

// Delete script (agency only)
router.delete("/scripts/:id", verifyRole("agency"), deleteScript);

// ============================================================================
// MODELS ROUTES
// ============================================================================

// Get agency models (agency only)
router.get("/models", verifyRole("agency"), getAgencyModels);

// ============================================================================
// ASSIGNMENT ROUTES
// ============================================================================

// Create assignment (agency only)
router.post("/assignments", verifyRole("agency"), assignQuestions);

// Get model assignments (model only)
router.get("/my-assignments", verifyRole("model"), getModelAssignments);
router.get("/assignments/model", verifyRole("model"), getModelAssignments);

// Get agency assignments (agency only)
router.get("/assignments", verifyRole("agency"), getAgencyAssignments);

// Get assignment details
router.get("/assignments/:assignmentId", getAssignmentById);

// Get assignment statistics
router.get("/assignments/stats", getAssignmentStats);

// Submit assignment for review (model only)
router.post("/assignments/:id/submit", verifyRole("model"), submitAssignment);

// Save assignment progress (model only)
router.post("/assignments/:id/save-progress", verifyRole("model"), saveAssignmentProgress);

// Complete assignment review (agency only)
router.post("/assignments/:id/complete-review", verifyRole("agency"), completeAssignmentReview);

// ============================================================================
// RECORDING ROUTES
// ============================================================================

// Submit recording (model only)
router.post(
  "/assignments/:assignmentId/questions/:questionId/record",
  verifyRole("model"),
  upload.single("audio"),
  uploadRecording
);

// Get recordings for assignment
router.get("/recordings", getAssignmentRecordings);

// Review a recording (agency only)
router.put(
  "/recordings/:id/review",
  verifyRole("agency"),
  reviewRecording
);

// Get recordings for current model
router.get(
  "/recordings/model",
  verifyRole("model"),
  getModelRecordings
);

// Get recordings for current agency (agency only)
router.get(
  "/recordings/agency",
  verifyRole("agency"),
  getAgencyRecordings
);

// NEW SECURE DOWNLOAD ROUTES (must come before legacy route to avoid conflicts)
// Generate temporary download token for a recording (model and agency)
router.post(
  "/recordings/:id/generate-download-token",
  verifyToken,
  generateDownloadToken
);

// Download recording using temporary token (no auth required - token validates access)
router.get(
  "/recordings/download/:token",
  verifyDownloadToken,
  downloadWithToken
);



// Debug endpoint - Get all recordings (authenticated)
router.get(
  "/recordings/debug/all",
  getAllRecordingsDebug
);

// ============================================================================
// DASHBOARD AND ANALYTICS ROUTES
// ============================================================================

// Dashboard data (model and agency)
router.get(
  "/dashboard",
  getDashboard
);

// ============================================================================
// HEALTH CHECK AND STATUS ROUTES
// ============================================================================

// Health check for voice module
router.get("/health", (req, res) => {
  res.json({
    success: true,
    message: "Voice question system is healthy",
    timestamp: new Date(),
    user: {
      id: req.user._id,
      role: req.user.role,
    },
  });
});

// FFmpeg configuration test
router.get("/test-ffmpeg", asyncHandler(async (req, res) => {
  try {
    res.json({
      success: true,
      message: "FFmpeg configuration loaded successfully",
      config: ffmpegConfig.getConfig()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "FFmpeg test failed",
      error: error.message,
      config: ffmpegConfig.getConfig()
    });
  }
}));

// Get voice module statistics
router.get("/stats/overview", async (req, res) => {
  try {
    let matchStage = {};

    if (req.user.role === "model") {
      matchStage = { modelId: req.user._id };
    } else if (req.user.role === "agency") {
      matchStage = { agencyId: req.user._id };
    } else {
      return res.status(403).json({ message: "Access denied" });
    }

    // Get counts for different entities
    const [assignmentCount, sectionCount, questionCount, recordingStats] =
      await Promise.all([
        VoiceAssignment.countDocuments({ ...matchStage, isDeleted: false }),
        req.user.role === "agency" ? QuestionSection.countDocuments({
          $or: [{ isDefault: true }, { createdBy: req.user._id }],
          isDeleted: false
        }) : QuestionSection.countDocuments({ isDefault: true, isDeleted: false }),
        req.user.role === "agency" ? QuestionTemplate.countDocuments({
          $or: [{ createdBy: null }, { createdBy: req.user._id }],
          isDeleted: false
        }) : QuestionTemplate.countDocuments({ createdBy: null, isDeleted: false }),
        // Get recording statistics
        VoiceRecording.aggregate([
          { $match: { ...matchStage, isDeleted: false } },
          {
            $group: {
              _id: "$status",
              count: { $sum: 1 }
            }
          }
        ])
      ]);

    // Process recording stats
    const recordingCounts = {
      total: 0,
      approved: 0,
      rejected: 0,
      pending: 0,
      requires_revision: 0
    };

    recordingStats.forEach(stat => {
      recordingCounts.total += stat.count;
      if (stat._id === 'approved') {
        recordingCounts.approved = stat.count;
      } else if (stat._id === 'rejected') {
        recordingCounts.rejected = stat.count;
      } else if (stat._id === 'requires_revision') {
        recordingCounts.requires_revision = stat.count;
      } else {
        recordingCounts.pending += stat.count;
      }
    });

    // Get assignment status breakdown
    const assignmentStats = await VoiceAssignment.aggregate([
      { $match: { ...matchStage, isDeleted: false } },
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 }
        }
      }
    ]);

    const assignmentCounts = {
      total: assignmentCount,
      pending: 0,
      in_progress: 0,
      submitted: 0,
      approved: 0,
      rejected: 0
    };

    assignmentStats.forEach(stat => {
      if (stat._id === 'pending') assignmentCounts.pending = stat.count;
      else if (stat._id === 'in_progress') assignmentCounts.in_progress = stat.count;
      else if (stat._id === 'submitted') assignmentCounts.submitted = stat.count;
      else if (stat._id === 'approved') assignmentCounts.approved = stat.count;
      else if (stat._id === 'rejected') assignmentCounts.rejected = stat.count;
    });

    res.json({
      success: true,
      data: {
        overview: {
          totalAssignments: assignmentCount,
          totalSections: sectionCount,
          totalQuestions: questionCount,
          totalRecordings: recordingCounts.total,
          approvedRecordings: recordingCounts.approved,
          pendingReviews: recordingCounts.pending + assignmentCounts.submitted
        },
        assignments: assignmentCounts,
        recordings: recordingCounts,
        trends: {
          // This could be enhanced with time-based data
          daily: []
        },
        modelPerformance: [],
        quality: {
          firstTimeApprovalRate: recordingCounts.total > 0 ?
            Math.round((recordingCounts.approved / recordingCounts.total) * 100) : 0,
          avgReviewTimeHours: 24, // Placeholder
          revisionRequestRate: recordingCounts.total > 0 ?
            Math.round((recordingCounts.requires_revision / recordingCounts.total) * 100) : 0,
          rejectionRate: recordingCounts.total > 0 ?
            Math.round((recordingCounts.rejected / recordingCounts.total) * 100) : 0
        }
      },
    });
  } catch (error) {
    console.error("Stats overview error:", error);
    res.status(500).json({ message: error.message });
  }
});

// Error handling middleware for multer
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === "LIMIT_FILE_SIZE") {
      return res.status(400).json({
        success: false,
        message: "File too large. Maximum size is 100MB.",
      });
    }
    if (error.code === "LIMIT_FILE_COUNT") {
      return res.status(400).json({
        success: false,
        message: "Too many files. Only one file allowed.",
      });
    }
  }

  if (error.message === "Only audio files are allowed!") {
    return res.status(400).json({
      success: false,
      message: "Only audio files are allowed.",
    });
  }

  next(error);
});

// ============================================================================
// REVIEW ROUTES
// ============================================================================
// Create a review for a recording (agency only)
router.post(
  "/reviews",
  verifyRole("agency"),
  createReview
);

// Get reviews for the agency
router.get(
  "/reviews/agency",
  verifyRole("agency"),
  getAgencyReviews
);

// ============================================================================
// SENTENCE-LEVEL ROUTES
// ============================================================================
// Upload a sentence recording (model only)
router.post(
  "/recordings/sentence",
  verifyRole("model"),
  uploadSentenceRecording
);

// Get all sentence recordings for a script
router.get(
  "/recordings/script/:scriptId/sentences",
  getScriptSentenceRecordings
);

// Auto-match a sentence recording to script (agency only)
router.put(
  "/recordings/sentence/:id/match",
  verifyRole("agency"),
  autoMatchSentenceRecording
);

// Get sentence recording progress for an assignment
router.get(
  "/assignments/:assignmentId/sentence-progress",
  getSentenceProgress
);

export default router;
