PORT=3000
SERVER_HOSTING_BASEURL=http://localhost:3000
FRONTEND_HOSTING_BASEURL=http://localhost:4000
MONGO_URI=mongodb://localhost:27017/modelsuite
JWT_SECRET=your_jwt_secret

# FFmpeg Configuration (optional - will auto-detect if not provided)
FFMPEG_PATH=/usr/local/bin/ffmpeg
FFPROBE_PATH=/usr/local/bin/ffprobe

CLOUDINARY_API_KEY=your_cloudinary_key
CLOUDINARY_API_SECRET=your_cloudinary_secret
CLOUDINARY_CLOUD_NAME=your_cloud_name
PANDADOC_API_KEY=your_pandadoc_key
PANDADOC_API_URL=https://api.pandadoc.com
META_APP_ID=your_facebook_app_id
META_APP_SECRET=your_facebook_secret
PAGE_ID=your_facebook_page_id
ACCESS_TOKEN=your_facebook_access_token
INSTAGRAM_BUSINESS_ID=your_instagram_business_id
USER_ACCESS_TOKEN=your_user_access_token
LONG_LIVED_TOKEN=your_long_lived_token
SERP_API_KEY=your_serp_api_key
EMAIL_USER=your_smtp_email
EMAIL_PASS=your_smtp_password
TWILIO_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
TWILIO_PHONE=your_twilio_phone
TIKAPI_KEY=your_tikapi_key
TIKAPI_CLIENT_ID=your_tikapi_client_id
TIKAPI_REDIRECT_URI=http://localhost:4000/tiktok/success
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_REDIRECT_URI=your_google_redirect_uri
NEWS_API_KEY=your_news_api_key
YOUTUBE_API_KEY=your_youtube_api_key
RAPIDAPI_KEY=your_rapidapi_key
TWITTER_BEARER_TOKEN=your_twitter_token
LOGIN_REFRESH_TOKEN_SECRET=your_refresh_secret
LOGIN_ACCESS_TOKEN_SECRET=your_access_secret
LOGIN_REFRESH_TOKEN_EXPIRY=7d
LOGIN_ACCESS_TOKEN_EXPIRY=15m




