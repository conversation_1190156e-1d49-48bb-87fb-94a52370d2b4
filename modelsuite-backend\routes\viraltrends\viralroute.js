import express from "express";
import getTrendsData from "../../controllers/viraltrends/viralcontroller.js";
import { Trend } from "../../models/viraltrends/trendschema.js";
const router = express.Router();

router.route("/trend").get(getTrendsData);
router.get("/trends", async (req, res) => {
  try {
    const { region = "global", category, limit = 20 } = req.query;

    const filter = { region };
    if (category && category !== "all") {
      filter.category = category;
    }

    const trends = await Trend.find(filter)
      .sort({ viral_score: -1, created_at: -1 })
      .limit(parseInt(limit));

    res.json({
      success: true,
      data: trends,
      count: trends.length,
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/trends/:id", async (req, res) => {
  try {
    const trend = await Trend.findById(req.params.id);
    if (!trend) {
      return res.status(404).json({ success: false, error: "Trend not found" });
    }
    res.json({ success: true, data: trend });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/trends/:id/save", async (req, res) => {
  try {
    const trend = await Trend.findByIdAndUpdate(
      req.params.id,
      { $addToSet: { tags: "Saved" } },
      { new: true },
    );
    res.json({ success: true, data: trend });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/categories", async (req, res) => {
  try {
    const categories = await Trend.distinct("category");
    res.json({ success: true, data: categories });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/stats", async (req, res) => {
  try {
    const totalTrends = await Trend.countDocuments();
    const categoryCounts = await Trend.aggregate([
      { $group: { _id: "$category", count: { $sum: 1 } } },
    ]);
    const sourceCounts = await Trend.aggregate([
      { $group: { _id: "$source", count: { $sum: 1 } } },
    ]);

    res.json({
      success: true,
      data: {
        totalTrends,
        categoryCounts,
        sourceCounts,
      },
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Manual refresh endpoint
router.post("/refresh", async (req, res) => {
  try {
    await fetchAllTrends();
    res.json({ success: true, message: "Trends refreshed successfully" });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});
export default router;
