import { useState, useEffect } from "react";
import FAQCard from "./FAQCard";
import faqsData from "./faqs.json";

export default function FAQ() {
  const [search, setSearch] = useState("");
  const [category, setCategory] = useState("All");
  const [filtered, setFiltered] = useState(faqsData);

  const categories = [
    "All",
    ...Array.from(new Set(faqsData.map((faq) => faq.category))),
  ];

  const top5FAQs = [...faqsData]
    .sort((a, b) => (b.views || 0) - (a.views || 0))
    .slice(0, 5);

  useEffect(() => {
    const filteredFAQs = faqsData.filter(
      (faq) =>
        (category === "All" || faq.category === category) &&
        faq.question.toLowerCase().includes(search.toLowerCase()),
    );
    setFiltered(filteredFAQs);
  }, [search, category]);

  const groupedFAQs = filtered.reduce((acc, faq) => {
    if (!acc[faq.category]) acc[faq.category] = [];
    acc[faq.category].push(faq);
    return acc;
  }, {});

  return (
    <div className="min-h-screen bg-[#0b1120] px-4 py-10 text-white">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-4xl font-bold mb-10 text-center tracking-tight">
          Frequently Asked Questions
        </h1>

        <div className="flex flex-col-reverse md:flex-row gap-8">
          {/* Sidebar */}
          <aside className="md:w-1/4">
            <div className="bg-[#1e293b] rounded-xl p-5 sticky top-20">
              <h2 className="text-xl font-semibold mb-4">Browse by Category</h2>
              <ul className="space-y-2">
                {categories.map((cat, idx) => (
                  <li key={idx}>
                    <button
                      onClick={() => setCategory(cat)}
                      className={`w-full text-left px-3 py-2 rounded-md transition font-medium ${
                        category === cat
                          ? "bg-blue-600 text-white"
                          : "hover:bg-[#334155] text-gray-300"
                      }`}
                    >
                      {cat}
                    </button>
                  </li>
                ))}
              </ul>
            </div>
          </aside>

          {/* Main Content */}
          <main className="md:w-3/4 pr-2">
            {/* Search Bar (non-scrollable) */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <input
                type="text"
                placeholder="Search questions..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="bg-[#1e293b] border border-[#334155] text-white placeholder-gray-400 p-3 rounded w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <div className="text-gray-400 text-sm pt-2 sm:pt-3">
                Showing {filtered.length} result
                {filtered.length !== 1 ? "s" : ""}
              </div>
            </div>

            {/* Scrollable Questions Area */}
            <div className="max-h-[75vh] overflow-y-auto scrollbar-thin scrollbar-thumb-[#334155] scrollbar-track-transparent pr-2">
              {category === "All" && search === "" && (
                <div className="mb-10">
                  <h3 className="text-xl font-bold mb-4">Top 5 Most Asked</h3>
                  <div className="space-y-4">
                    {top5FAQs.map((faq, idx) => (
                      <FAQCard
                        key={`top-${idx}`}
                        question={faq.question}
                        answer={faq.answer}
                      />
                    ))}
                  </div>
                </div>
              )}

              {filtered.length === 0 ? (
                <p className="text-gray-400 text-center">No FAQs found.</p>
              ) : category === "All" ? (
                Object.entries(groupedFAQs).map(([cat, faqs]) => (
                  <div key={cat} className="mb-10">
                    <h3 className="text-xl font-semibold mb-4">{cat}</h3>
                    <div className="space-y-4">
                      {faqs.map((faq, idx) => (
                        <FAQCard
                          key={`${cat}-${idx}`}
                          question={faq.question}
                          answer={faq.answer}
                        />
                      ))}
                    </div>
                  </div>
                ))
              ) : (
                <div className="space-y-4">
                  {filtered.map((faq, idx) => (
                    <FAQCard
                      key={idx}
                      question={faq.question}
                      answer={faq.answer}
                    />
                  ))}
                </div>
              )}
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}
