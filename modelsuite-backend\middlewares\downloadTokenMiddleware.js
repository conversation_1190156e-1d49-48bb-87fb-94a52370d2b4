import jwt from "jsonwebtoken";
import VoiceDownloadToken from "../models/voice/VoiceDownloadToken.js";

export const verifyDownloadToken = async (req, res, next) => {
  try {
    const { token } = req.params;

    if (!token) {
      return res.status(401).json({
        success: false,
        message: "Download token is required"
      });
    }

    // First verify the JWT signature and expiry
    let decodedToken;
    try {
      decodedToken = jwt.verify(token, process.env.VOICE_TOKEN_SECRET);
    } catch (error) {
      if (error.name === "TokenExpiredError") {
        return res.status(401).json({
          success: false,
          message: "Download token has expired"
        });
      }
      return res.status(401).json({
        success: false,
        message: "Invalid download token"
      });
    }

    // Validate token structure
    if (!decodedToken.recordingId || !decodedToken.userId || !decodedToken.userRole) {
      return res.status(401).json({
        success: false,
        message: "Invalid token structure"
      });
    }

    // Check if token exists in database and is still valid
    const tokenRecord = await VoiceDownloadToken.findValidToken(token);

    if (!tokenRecord) {
      return res.status(401).json({
        success: false,
        message: "Download token not found, expired, or already used"
      });
    }

    // Verify token data matches database record
    if (
      tokenRecord.recordingId._id.toString() !== decodedToken.recordingId ||
      tokenRecord.userId.toString() !== decodedToken.userId ||
      tokenRecord.userRole !== decodedToken.userRole
    ) {
      return res.status(401).json({
        success: false,
        message: "Token data mismatch"
      });
    }

    // Optional: IP address validation (if stored during token generation)
    const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
    if (tokenRecord.ipAddress && tokenRecord.ipAddress !== clientIP) {
      console.warn(`IP mismatch for download token: expected ${tokenRecord.ipAddress}, got ${clientIP}`);
      // Note: We're logging but not blocking - IP can change due to proxies, mobile networks, etc.
    }

    // Mark token as used (single-use)
    await tokenRecord.markAsUsed();

    // Attach decoded token data and recording to request
    req.downloadToken = decodedToken;
    req.tokenRecord = tokenRecord;
    req.recording = tokenRecord.recordingId; // Already populated from findValidToken

    next();
  } catch (error) {
    console.error("Download token verification error:", error);
    return res.status(500).json({ 
      success: false,
      message: "Internal server error during token verification" 
    });
  }
};

// Optional: Middleware to log download attempts for audit purposes
export const logDownloadAttempt = (req, res, next) => {
  const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
  const userAgent = req.get('User-Agent');
  
  console.log(`Download attempt: Token=${req.params.token?.substring(0, 10)}..., IP=${clientIP}, UserAgent=${userAgent}`);
  
  next();
};

export default { verifyDownloadToken, logDownloadAttempt };
