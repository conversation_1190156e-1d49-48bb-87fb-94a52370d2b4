/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import axios from "axios";
import {
  MessageSquare,
  UploadCloud,
  Flame,
  ShieldAlert,
  Users,
  Gift,
  Trophy,
  Film,
  Calendar,
  TrendingUp,
  Plus,
  Trash2,
  Settings,
  EarthLock,
  ChartNoAxesColumn,
  CalendarCheck,
  UserPen,
  User,
  MessageCircleQuestion,
  Settings2,
} from "lucide-react";
// import ChatWindow from "../../components/ChatWindow";
// import TaskList from "../../components/task/TaskList";
import InstagramDashboard from "../../components/socialMedia/InstagramInsights";
import BillingDashboard from "../../components/Billing/Billing";
// import { GiProtectionGlasses } from "react-icons/gi";
import ViralTrendGoogleTrend from "../../components/socialMedia/viraltrends/ViralTrendGoogleTrend";
// import TypingIndicator from "../../components/ui/TypingIndicator";
import AgencyMessanger from "../../components/agencyMessanger";
import BoardsView from "../../components/task/board/BoardsView";
import CalendarView from "../../components/Calendar/Calendar";
import Agencyform from "../../components/supportteam/agency/Agencyform";
export default function CreatorInsightsDashboard() {
  const { id } = useParams();
  const user = JSON.parse(localStorage.getItem("auth"))?.user;
  const token = JSON.parse(localStorage.getItem("auth"))?.token;
  const baseURL = import.meta.env.VITE_API_BASE_URL;

  const [sidebarItems, setSidebarItems] = useState([
    { icon: MessageSquare, label: "Messenger", active: false },
    { icon: Calendar, label: "Calendar" },
    { icon: CalendarCheck, label: "Tasks" },
    { icon: TrendingUp, label: "Traffic & Analytics" },
    { icon: UploadCloud, label: "Postings & Content Upload" },
    { icon: ChartNoAxesColumn, label: "Viral Trends & Inspiration" },
    { icon: User, label: "Team Members" },
    { icon: ShieldAlert, label: "Leak Protection" },
    { icon: Calendar, label: "Billing & Finance" },
    { icon: Gift, label: "Paid Platforms" },
    { icon: Trophy, label: "Rewards & Gamification" },
    { icon: Flame, label: "Content Library" },
    { icon: Users, label: "Fan Management" },
    { icon: MessageCircleQuestion, label: "Support & Help" },
    { icon: UserPen, label: "Support Contact Manager" },
  ]);

  const [modelInfo, setModelInfo] = useState({});
  const [selectedChat, setSelectedChat] = useState(null);
  const [groupName, setGroupName] = useState("");
  const [groupList, setGroupList] = useState([]);
  const [topicsMap, setTopicsMap] = useState({});
  const [newTopic, setNewTopic] = useState("");
  const [activeGroupForTopic, setActiveGroupForTopic] = useState(null);

  const activeMenu = sidebarItems.find((item) => item.active)?.label;

  const handleTabClick = (label) => {
    const updatedItems = sidebarItems.map((item) => ({
      ...item,
      active: item.label === label,
    }));
    setSidebarItems(updatedItems);
    setSelectedChat(null);
  };

  const fetchModel = async () => {
    try {
      const res = await axios.get(`${baseURL}/model/${id}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      setModelInfo(res.data);
    } catch (err) {
      console.error("Failed to fetch model:", err);
    }
  };

  const fetchGroups = async () => {
    try {
      const res = await axios.get(`${baseURL}/messages/group?modelId=${id}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      setGroupList(res.data);

      const topicsObj = {};
      for (const group of res.data) {
        const topicRes = await axios.get(
          `${baseURL}/topic/group/${group._id}`,
          {
            headers: { Authorization: `Bearer ${token}` },
          },
        );
        topicsObj[group._id] = topicRes.data;
      }
      setTopicsMap(topicsObj);
    } catch (err) {
      console.error("Failed to fetch groups/topics:", err);
    }
  };

  // const handleCreateGroup = async () => {
  //   if (!groupName.trim()) return;
  //   try {
  //     await axios.post(
  //       `${baseURL}/messages/group/create`,
  //       {
  //         title: groupName,
  //         modelId: id,
  //         creatorModel: "Agency",
  //       },
  //       { headers: { Authorization: `Bearer ${token}` } }
  //     );
  //     setGroupName("");
  //     fetchGroups();
  //   } catch (err) {
  //     console.error("Failed to create group:", err);
  //   }
  // };

  // const handleCreateTopic = async (groupId) => {
  //   if (!newTopic.trim()) return;
  //   try {
  //     await axios.post(
  //       `${baseURL}/topic/create`,
  //       {
  //         title: newTopic,
  //         groupId,
  //       },
  //       { headers: { Authorization: `Bearer ${token}` } }
  //     );
  //     setNewTopic("");
  //     setActiveGroupForTopic(null);
  //     fetchGroups();
  //   } catch (err) {
  //     console.error("Failed to create topic:", err);
  //   }
  // };

  // const handleDeleteTopic = async (topicId) => {
  //   if (!confirm("Are you sure you want to delete this topic?")) return;
  //   try {
  //     await axios.delete(`${baseURL}/api/v1/topic/${topicId}`, {
  //       headers: { Authorization: `Bearer ${token}` },
  //     });
  //     fetchGroups();
  //   } catch (err) {
  //     console.error("Failed to delete topic:", err);
  //   }
  // };

  // const handleDeleteGroup = async (groupId) => {
  //   if (!confirm("Are you sure you want to delete this group?")) return;
  //   try {
  //     await axios.delete(`${baseURL}/messages/group/${groupId}`, {
  //       headers: { Authorization: `Bearer ${token}` },
  //     });
  //     fetchGroups();
  //     setSelectedChat(null);
  //   } catch (err) {
  //     console.error("Failed to delete group:", err);
  //   }
  // };

  useEffect(() => {
    fetchModel();
    fetchGroups();
  }, [id]);

  return (
    <div className="flex h-[100%] bg-white dark:bg-gray-950 text-gray-900 dark:text-white transition-colors duration-200">
      {/* Sidebar */}
      <div className="w-64 flex-shrink-0 bg-gray-100 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 flex flex-col transition-colors duration-200">
        <div className="p-6">
          <div
            onClick={() => {
              const reset = sidebarItems.map((item) => ({
                ...item,
                active: false,
              }));
              setSidebarItems(reset);
              setSelectedChat(null);
            }}
            className="cursor-pointer"
          >
            <div className="bg-gray-100 dark:bg-gray-800 rounded-xl p-4 shadow flex items-center gap-3 transition-colors duration-200">
              <img
                className="w-10 h-10 rounded-full object-cover border-2 border-blue-600"
                alt="AM"
                src={modelInfo.profilePhoto}
              />
              <div>
                <span className="font-semibold text-lg block">
                  {modelInfo.fullName || "Loading..."}
                </span>
                <span className="text-xs text-gray-600 dark:text-gray-400">
                  Model
                </span>
              </div>
            </div>
          </div>
          <div className="border-b border-gray-800 my-4"></div>
        </div>
        <nav className="flex-1 overflow-y-auto px-6 pb-6">
          {sidebarItems.map((item, index) => (
            <div
              key={index}
              onClick={() => handleTabClick(item.label)}
              tabIndex={0}
              role="button"
              aria-selected={item.active}
              className={`flex gap-3 px-4 py-3 rounded-lg cursor-pointer transition-all duration-150 font-medium text-base items-center select-none outline-none ${
                item.active
                  ? "bg-blue-600 shadow text-white"
                  : "hover:bg-gray-200 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300"
              } overflow-hidden mb-1`}
            >
              <item.icon className="w-5 h-5 flex-shrink-0" />
              <span className="truncate block max-w-[140px] overflow-hidden text-ellipsis">
                {item.label}
              </span>
            </div>
          ))}
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {activeMenu === "Tasks" ? (
          <div className="flex-1 flex flex-col overflow-hidden bg-gray-50 dark:bg-gray-900">
            <BoardsView modelId={id} />
          </div>
        ) : activeMenu === "Messenger" ? (
          <div className="flex-1 flex flex-col overflow-hidden">
            <AgencyMessanger modeinfo={modelInfo} />
          </div>
        ) : activeMenu === "Traffic & Analytics" ? (
          <div className="flex-1 overflow-y-auto">
            <InstagramDashboard Id={id} role={"agency"} />
          </div>
        ) : activeMenu === "Viral Trends & Inspiration" ? (
          <div className="flex-1 overflow-y-auto">
            <ViralTrendGoogleTrend />
          </div>
        ) : activeMenu === "Billing & Finance" ? (
          <div className="flex-1 overflow-y-auto">
            <BillingDashboard modelInfo={modelInfo} />
          </div>
        ) : activeMenu === "Calendar" ? (
          <div className="flex-1 overflow-y-auto">
            <CalendarView modelId={id} />
          </div>
        ) : activeMenu === "Support Contact Manager" ? (
          <div className="flex-1 overflow-y-auto">
            <Agencyform />
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <h1 className="text-3xl font-bold mb-4 text-gray-900 dark:text-white">
                Creator Insights Dashboard
              </h1>
              <p className="text-gray-600 dark:text-gray-400 text-lg">
                Select a menu item to begin.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
