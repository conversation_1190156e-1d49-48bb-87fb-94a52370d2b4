// src/components/ViralTrendsDashboard.jsx
import React, { useState, useEffect } from "react";
import axios from "axios";
import "./styles/ViralTrendsDashboard.css";

const ViralTrendsDashboard = () => {
  const [trends, setTrends] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedRegion, setSelectedRegion] = useState("global");
  const [categories, setCategories] = useState([]);
  const [stats, setStats] = useState(null);

  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

  const fetchTrends = async (
    category = selectedCategory,
    region = selectedRegion,
  ) => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/Trending/trends`, {
        params: {
          category: category,
          region: region,
          limit: 20,
        },
      });
      setTrends(response.data.data);
      setError(null);
    } catch (err) {
      setError("Failed to fetch trends");
      console.error("Error fetching trends:", err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/Trending/categories`);
      setCategories(["all", ...response.data.data]);
    } catch (err) {
      console.error("Error fetching categories:", err);
    }
  };

  // Fetch stats
  const fetchStats = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/Trending/stats`);
      setStats(response.data.data);
    } catch (err) {
      console.error("Error fetching stats:", err);
    }
  };

  // Save trend for content ideas
  const saveTrend = async (trendId) => {
    try {
      await axios.post(`${API_BASE_URL}/Trending/trends/${trendId}/save`);
      // Update local state
      setTrends(
        trends.map((trend) =>
          trend._id === trendId
            ? { ...trend, tags: [...trend.tags, "Saved"] }
            : trend,
        ),
      );
    } catch (err) {
      console.error("Error saving trend:", err);
    }
  };

  // Manual refresh
  // eslint-disable-next-line no-unused-vars
  const refreshTrends = async () => {
    try {
      setLoading(true);
      await axios.post(`${API_BASE_URL}/Trending/refresh`);
      await fetchTrends();
    } catch (err) {
      setError("Failed to refresh trends");
      console.error("Error refreshing trends:", err);
    }
  };

  // Get category icon
  const getCategoryIcon = (category) => {
    const icons = {
      Politics: "⚖️",
      "Social Media": "📱",
      Tech: "💻",
      Memes: "😂",
      Scandals: "🔥",
      Entertainment: "🎬",
      Culture: "🎭",
      Sports: "⚽",
      Music: "🎵",
      Gaming: "🎮",
    };
    return icons[category] || "📊";
  };

  // Get source icon
  const getSourceIcon = (source) => {
    const icons = {
      "Google Trends": "🔍",
      "News API": "📰",
      YouTube: "📺",
      Twitter: "🐦",
      Reddit: "🔶",
      "Hacker News": "💡",
    };
    return icons[source] || "📈";
  };

  // Get viral score color
  const getScoreColor = (score) => {
    if (score >= 80) return "text-red-500";
    if (score >= 60) return "text-orange-500";
    if (score >= 40) return "text-yellow-500";
    return "text-green-500";
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  useEffect(() => {
    fetchTrends();
    fetchCategories();
    fetchStats();
  }, []);

  useEffect(() => {
    fetchTrends(selectedCategory, selectedRegion);
  }, [selectedCategory, selectedRegion]);
  return (
    <div className="viral-trends-dashboard">
      {/* Header */}
      <div className="dashboard-header">
        <div className="header-content">
          <h1>🚀 Viral Trends Dashboard</h1>
          <p>
            Real-time trends for content creators and social media strategists
          </p>
        </div>
        {/* <button onClick={refreshTrends} className="refresh-btn" disabled={loading}>
                    {loading ? '🔄' : '↻'} Refresh
                </button> */}
      </div>

      {/* Stats Bar */}
      {stats && (
        <div className="stats-bar">
          <div className="stat-item">
            <span className="stat-number">{stats.totalTrends}</span>
            <span className="stat-label">Total Trends</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">{stats.categoryCounts.length}</span>
            <span className="stat-label">Categories</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">{stats.sourceCounts.length}</span>
            <span className="stat-label">Sources</span>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="filters">
        <div className="filter-group">
          <label>Category:</label>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="filter-select"
          >
            {categories.map((category) => (
              <option key={category} value={category}>
                {category === "all" ? "All Categories" : category}
              </option>
            ))}
          </select>
        </div>

        <div className="filter-group">
          <label>Region:</label>
          <select
            value={selectedRegion}
            onChange={(e) => setSelectedRegion(e.target.value)}
            className="filter-select"
          >
            <option value="global">🌍 Global</option>
            <option value="us">🇺🇸 United States</option>
            <option value="de">🇩🇪 Germany</option>
            <option value="uk">🇬🇧 United Kingdom</option>
          </select>
        </div>
      </div>

      {/* Error Message */}
      {error && <div className="error-message">❌ {error}</div>}

      {/* Loading State */}
      {loading && (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Fetching latest trends...</p>
        </div>
      )}

      {/* Trends Carousel */}
      <div className="trends-carousel">
        {trends.length === 0 && !loading ? (
          <div className="no-trends">
            <p>No trends found for the selected filters.</p>
          </div>
        ) : (
          <div className="trends-grid">
            {trends.map((trend) => (
              <div key={trend._id} className="trend-card text-white">
                <div className="trend-header">
                  <div className="trend-category">
                    <span className="category-icon">
                      {getCategoryIcon(trend.category)}
                    </span>
                    <span className="category-name">{trend.category}</span>
                  </div>
                  <div
                    className={`viral-score ${getScoreColor(trend.viral_score)}`}
                  >
                    🔥 {Math.round(trend.viral_score)}
                  </div>
                </div>

                <div className="trend-content">
                  <h3 className="trend-title">{trend.title}</h3>
                  <p className="trend-summary">{trend.summary}</p>
                </div>

                <div className="trend-tags">
                  {trend.tags.map((tag, index) => (
                    <span key={index} className="tag">
                      {tag}
                    </span>
                  ))}
                </div>

                <div className="trend-footer">
                  <div className="trend-source">
                    <span className="source-icon">
                      {getSourceIcon(trend.source)}
                    </span>
                    <span className="source-name">{trend.source}</span>
                    <span className="trend-date">
                      {formatDate(trend.created_at)}
                    </span>
                  </div>

                  <div className="trend-actions">
                    <a
                      href={trend.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="action-btn primary"
                    >
                      📖 Read More / View Video
                    </a>
                    <button
                      onClick={() => saveTrend(trend._id)}
                      className="action-btn secondary"
                      disabled={trend.tags.includes("Saved")}
                    >
                      {trend.tags.includes("Saved") ? "✅ Saved" : "💾 Save"}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ViralTrendsDashboard;
