import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Upload, <PERSON>, AlertTriangle } from "lucide-react";
import { useSelector, useDispatch } from "react-redux";
import {
  fetchSkills,
  selectSkills,
  selectSkillsLoading,
} from "../../globalstate/skillMatrix/skillsSlice";
import {
  fetchUserSkills,
  updateUserSkill,
  endorseUserSkill,
  fetchSkillGaps,
  selectUserSkills,
  selectSkillGaps,
  selectUserSkillsLoading,
} from "../../globalstate/skillMatrix/userSkillsSlice";

const UserSkillProfile = ({ user, onBack, currentUser }) => {
  const dispatch = useDispatch();
  const [editingSkill, setEditingSkill] = useState(null);

  // Redux selectors
  const skills = useSelector(selectSkills);
  const userSkills = useSelector(selectUserSkills(user._id));
  const skillGaps = useSelector(selectSkillGaps(user._id));
  const skillsLoading = useSelector(selectSkillsLoading);
  const userSkillsLoading = useSelector(selectUserSkillsLoading);

  const levelLabels = {
    0: "Not Applicable",
    1: "Beginner",
    2: "Basic",
    3: "Intermediate",
    4: "Advanced",
    5: "Expert",
  };

  const levelColors = {
    0: "bg-gray-100 text-gray-600",
    1: "bg-red-100 text-red-800",
    2: "bg-orange-100 text-orange-800",
    3: "bg-yellow-100 text-yellow-800",
    4: "bg-blue-100 text-blue-800",
    5: "bg-green-100 text-green-800",
  };

  useEffect(() => {
    // Fetch user skills, skill gaps, and all skills when component mounts
    dispatch(fetchUserSkills({ userId: user._id }));
    dispatch(
      fetchSkillGaps({
        userId: user._id,
        params: { target_role: user.job_title },
      }),
    );
    dispatch(fetchSkills());
  }, [dispatch, user._id, user.job_title]);

  const handleSkillUpdate = async (skillId, level, evidence = "") => {
    try {
      await dispatch(
        updateUserSkill({
          user_id: user._id,
          skill_id: skillId,
          level: level,
          source: currentUser.id === user._id ? "self" : "manual",
          evidence_url: evidence,
        }),
      ).unwrap();
    } catch (error) {
      console.error("Failed to update skill:", error);
    }
  };

  const handleEndorse = async (userSkillId) => {
    try {
      await dispatch(endorseUserSkill(userSkillId)).unwrap();
    } catch (error) {
      console.error("Failed to endorse skill:", error);
    }
  };

  const SkillEditForm = ({ userSkill, onSave, onCancel }) => {
    const [level, setLevel] = useState(userSkill?.level || 0);
    const [evidence, setEvidence] = useState(userSkill?.evidence_url || "");

    const handleSubmit = (e) => {
      e.preventDefault();
      handleSkillUpdate(userSkill.skill._id, level, evidence);
      onSave();
    };

    return (
      <form onSubmit={handleSubmit} className="bg-gray-50 p-4 rounded-lg mt-2">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Proficiency Level
            </label>
            <select
              value={level}
              onChange={(e) => setLevel(parseInt(e.target.value))}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {Object.entries(levelLabels).map(([value, label]) => (
                <option key={value} value={value}>
                  {value} - {label}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Evidence URL (Optional)
            </label>
            <input
              type="url"
              value={evidence}
              onChange={(e) => setEvidence(e.target.value)}
              placeholder="https://example.com/portfolio"
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
        <div className="flex justify-end space-x-2 mt-4">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Save
          </button>
        </div>
      </form>
    );
  };

  if (
    skillsLoading ||
    userSkillsLoading.userSkills ||
    userSkillsLoading.skillGaps
  ) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Group skills by category
  const skillsByCategory = userSkills.reduce((acc, userSkill) => {
    const categoryName = userSkill.category?.name || "Uncategorized";
    if (!acc[categoryName]) {
      acc[categoryName] = [];
    }
    acc[categoryName].push(userSkill);
    return acc;
  }, {});

  return (
    <div className="bg-white rounded-lg shadow-sm">
      {/* Header */}
      <div className="border-b border-gray-200 px-6 py-4">
        <div className="flex items-center space-x-4">
          <button onClick={onBack} className="p-2 hover:bg-gray-100 rounded-md">
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div className="flex-shrink-0 h-12 w-12">
            <div className="h-12 w-12 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium text-lg">
              {user.display_name?.charAt(0) || "U"}
            </div>
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {user.display_name}
            </h1>
            <p className="text-gray-600">{user.job_title || user.role}</p>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Skill Gaps Alert */}
        {skillGaps.length > 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-2 mb-2">
              <AlertTriangle className="w-5 h-5 text-yellow-600" />
              <h3 className="font-medium text-yellow-800">
                Skill Gaps Identified
              </h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
              {skillGaps.slice(0, 6).map((gap) => (
                <div key={gap.skill._id} className="text-sm text-yellow-700">
                  <span className="font-medium">{gap.skill.name}</span>
                  <span className="ml-2">
                    ({gap.current_level}/{gap.required_level})
                  </span>
                </div>
              ))}
            </div>
            {skillGaps.length > 6 && (
              <p className="text-sm text-yellow-700 mt-2">
                and {skillGaps.length - 6} more...
              </p>
            )}
          </div>
        )}

        {/* Skills by Category */}
        <div className="space-y-8">
          {Object.entries(skillsByCategory).map(([categoryName, skills]) => (
            <div key={categoryName}>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2">
                {categoryName}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {skills.map((userSkill) => (
                  <div
                    key={userSkill._id}
                    className="border border-gray-200 rounded-lg p-4"
                  >
                    {editingSkill === userSkill._id ? (
                      <SkillEditForm
                        userSkill={userSkill}
                        onSave={() => setEditingSkill(null)}
                        onCancel={() => setEditingSkill(null)}
                      />
                    ) : (
                      <div>
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-medium text-gray-900">
                            {userSkill.skill.name}
                          </h4>
                          <div className="flex items-center space-x-1">
                            <span
                              className={`px-2 py-1 rounded text-xs font-medium ${
                                levelColors[userSkill.level]
                              }`}
                            >
                              {userSkill.level}
                            </span>
                          </div>
                        </div>

                        <p className="text-sm text-gray-600 mb-2">
                          {levelLabels[userSkill.level]}
                        </p>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            {userSkill.evidence_url && (
                              <a
                                href={userSkill.evidence_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800"
                              >
                                <Link className="w-4 h-4" />
                              </a>
                            )}
                            {userSkill.endorsed_by?.length > 0 && (
                              <div className="flex items-center space-x-1">
                                <Star className="w-4 h-4 text-yellow-500" />
                                <span className="text-xs text-gray-600">
                                  {userSkill.endorsed_by.length}
                                </span>
                              </div>
                            )}
                          </div>

                          <div className="flex space-x-2">
                            {currentUser.role === "agency" ||
                            currentUser.id === user._id ? (
                              <button
                                onClick={() => setEditingSkill(userSkill._id)}
                                className="text-sm text-blue-600 hover:text-blue-800"
                              >
                                Edit
                              </button>
                            ) : (
                              <button
                                onClick={() => handleEndorse(userSkill._id)}
                                className="text-sm text-green-600 hover:text-green-800"
                              >
                                Endorse
                              </button>
                            )}
                          </div>
                        </div>

                        <div className="text-xs text-gray-500 mt-2">
                          Updated:{" "}
                          {new Date(userSkill.updated_at).toLocaleDateString()}
                          {userSkill.source && (
                            <span className="ml-2">via {userSkill.source}</span>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Add Missing Skills */}
        {currentUser.role === "agency" && (
          <div className="mt-8 border-t border-gray-200 pt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Add Skills
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {skills
                .filter(
                  (skill) =>
                    !userSkills.some((us) => us.skill._id === skill._id),
                )
                .slice(0, 6)
                .map((skill) => (
                  <div
                    key={skill._id}
                    className="border border-gray-200 rounded-lg p-4"
                  >
                    <h4 className="font-medium text-gray-900 mb-2">
                      {skill.name}
                    </h4>
                    <p className="text-sm text-gray-600 mb-3">
                      {skill.category_id?.name} • {skill.type}
                    </p>
                    <button
                      onClick={() => handleSkillUpdate(skill._id, 1)}
                      className="w-full px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                    >
                      Add Skill
                    </button>
                  </div>
                ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UserSkillProfile;
