import PostFeed from "../models/postFeed.js";

// Create a new post
export const createPost = async (req, res) => {
  try {
    const { title, post, image, userId, Audience, expirationDate } = req.body;

    if (!post || !userId) {
      return res
        .status(400)
        .json({ message: "Post content and userId are required." });
    }

    const newPost = new PostFeed({
      title,
      post,
      image,
      userId,
      Audience,
      expirationDate,
    });
    await newPost.save();

    res
      .status(201)
      .json({ message: "Post created successfully", post: newPost });
  } catch (error) {
    res.status(500).json({ message: "Error creating post", error });
  }
};

// Delete a post by ID
export const deletePost = async (req, res) => {
  try {
    const { id } = req.params;
    const deletedPost = await PostFeed.findByIdAndDelete(id);

    if (!deletedPost) {
      return res.status(404).json({ message: "Post not found" });
    }

    res.status(200).json({ message: "Post deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: "Error deleting post", error });
  }
};

export const getAllPosts = async (req, res) => {
  try {
    const posts = await PostFeed.find().sort({ createdAt: -1 });
    res.json(posts);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
