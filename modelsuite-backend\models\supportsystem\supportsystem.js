import mongoose from "mongoose";

const supportSystemSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, "Title is required!"],
    },
    name: {
      type: String,
      required: [true, "Name is required!"],
    },
    profile_image: {
      type: String,
      required: [true, "Profile image is required!"],
    },
    bio: {
      type: String,
      required: [true, "Bio is required!"],
    },
    support_areas: [
      {
        type: String,
        required: [true, "Support area is required!"],
      },
    ],
  },
  { timestamps: true },
);
const SupportSystem = mongoose.model("SupportSystem", supportSystemSchema);
export default SupportSystem;
