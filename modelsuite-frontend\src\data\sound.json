{"data": {"stats": [{"music": {"id": "c23c5564-4b5d-4654-b890-e2910db46c5b", "musicId": "7262123212094327595", "reposts": 278000, "duration": 87, "album": "", "url": "https://sys-datapoint.flaidata.com/media/sounds/7262123212094327595.mp3", "title": "original sound", "creator": "♱", "musicOriginal": true, "musicUrl": "https://www.tiktok.com/music/-7262123212094327595", "cover": "https://i.pinimg.com/236x/63/41/b4/6341b40178c01d891d30e6bbce3b010d.jpg", "recognitionTitle": "Mover", "recognitionDatePublished": "2016", "recognitionAuthor": "GRM Daily", "recognitionAlbum": "Best of #Rated", "recognitionLabel": "Independent", "recognitionLink": "https://www.shazam.com/track/321791968/mover", "recognitionDate": "2024-06-27T09:11:18.24382", "recognitionText": null, "parseDate": "2024-06-24T23:00:56.76661", "updateDate": "2025-07-09T09:54:07.198745", "parser": "Feed", "dailyRise": 3450, "dailyRiseForecastingError": 0.99, "dailyRiseForecasting": 3511.81, "graphWeight": 0.98, "artistRegion": "US", "authorIdLong": 6896698859814176000, "authorUniqueId": "v8lone", "authorNickname": "v8lone", "isArtist": false, "musicIdLong": 0, "dailyRiseMonth": 0, "dailyRise24hours": 0, "musicStatus": 6, "notAvailable": false, "recognizeStatus": 0, "genre": 569, "appleLink": "https://itunes.apple.com/gb/album/mover/1124459960?i=1124460468&mttnagencyid=s2n&mttnsiteid=125115&mttn3pid=Apple-Shazam&mttnsub1=Shazam_ios&mttnsub2=5348615A-616D-3235-3830-44754D6D5973&itscg=30201&app=itunes&itsct=Shazam_ios", "spotifyLink": "https://open.spotify.com/track/6gWQbRtJvbe2KFWoZaLXsO", "downloadLink": "https://audio-ssl.itunes.apple.com/itunes-assets/AudioPreview125/v4/43/67/8a/43678a65-4717-11cb-1df4-f699dba580e9/mzaf_12299670033725946262.plus.aac.ep.m4a", "youtubeLink": null, "deezerLink": "https://www.deezer.com/track/126787985"}, "positionInChart": null, "positionInCurrentChart": null, "calculations": null, "videosList": null}, {"music": {"id": "5226c322-1e84-4c25-a962-76ae129003fb", "musicId": "7429570486335589151", "reposts": 501400, "duration": 13, "album": "", "url": "https://sys-datapoint.flaidata.com/media/sounds/7429570486335589151.mp3", "title": "LOCKJAW", "creator": "kat", "musicOriginal": true, "musicUrl": "https://www.tiktok.com/music/-7429570486335589151", "cover": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSKLksCdqGa0vyEavbJhDDlABV6o7GT-o5Axb8bLkEKIFlnWxmJpy9OEw-Eftwha3H_uGA&usqp=CAU", "recognitionTitle": "Lockjaw", "recognitionDatePublished": "1992", "recognitionAuthor": "Sir Mix-A-Lot", "recognitionAlbum": "Party Hits", "recognitionLabel": "Black Sheep Media", "recognitionLink": "https://www.shazam.com/track/47261309/lockjaw", "recognitionDate": "2025-01-07T12:53:17.193527", "recognitionText": null, "parseDate": "2024-11-23T21:39:08.211235", "updateDate": "2025-07-11T04:04:15.620761", "parser": "SoundItem", "dailyRise": 2000, "dailyRiseForecastingError": 0.99, "dailyRiseForecasting": 2001.2, "graphWeight": 1, "artistRegion": "US", "authorIdLong": 6967339954415076000, "authorUniqueId": "katsvfy", "authorNickname": "kat", "isArtist": false, "musicIdLong": 0, "dailyRiseMonth": 0, "dailyRise24hours": 0, "musicStatus": 6, "notAvailable": false, "recognizeStatus": 0, "genre": 752, "appleLink": "https://itunes.apple.com/gb/album/lockjaw/572193277?i=572193597&mttnagencyid=s2n&mttnsiteid=125115&mttn3pid=Apple-Shazam&mttnsub1=Shazam_ios&mttnsub2=5348615A-616D-3235-3830-44754D6D5973&itscg=30201&app=itunes&itsct=Shazam_ios", "spotifyLink": "", "downloadLink": "https://audio-ssl.itunes.apple.com/itunes-assets/AudioPreview125/v4/1d/73/84/1d7384c6-f958-8593-495b-1e1ef33dcc48/mzaf_13681901848099033791.plus.aac.ep.m4a", "youtubeLink": null, "deezerLink": null}, "positionInChart": null, "positionInCurrentChart": null, "calculations": null, "videosList": null}, {"music": {"id": "9ead56a9-dfb2-4387-8bfe-b1e1c259dae7", "musicId": "7430451854327958289", "reposts": 340100, "duration": 39, "album": "", "url": "https://v77.tiktokcdn-eu.com/9aa143fb51bb025618ff6c5892e0758a/6871a343/video/tos/alisg/tos-alisg-v-27dcd7/owuoBXfDQQ5sJpnU2RDWQHgkeBJCWESiFTaYYg/?a=1233&bti=ODszNWYuMDE6&ch=0&cr=0&dr=0&er=0&lr=default&cd=0%7C0%7C0%7C0&br=250&bt=125&ft=.NpOcInz7ThqDwmKXq8Zmo&mime_type=audio_mpeg&qs=6&rc=ZThkaDw6PGg5PDQ6ZDdpO0Bpamppb3Y5cjk3djMzODU8NEA0MGFhXjMzX2AxLzUxNDY0YSMyNi5xMmQ0NmNgLS1kMS1zcw%3D%3D&vvpl=1&l=2025071023494830B1D0B21A64DAAC9725&btag=e00088000", "title": "original sound", "creator": "<PERSON><PERSON>", "musicOriginal": true, "musicUrl": "https://www.tiktok.com/music/-7430451854327958289", "cover": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSSjBfXkWlKqiHChLFXK0Hw09CiHkgIonHMDC8o6wzXwAsRl8-283_J9ah76DlJ8YhrVZ0&usqp=CAU", "recognitionTitle": "APT.", "recognitionDatePublished": "2024", "recognitionAuthor": "ROSÉ & Bruno Mars", "recognitionAlbum": "rosie", "recognitionLabel": "Atlantic Records", "recognitionLink": "https://www.shazam.com/track/715898597/apt", "recognitionDate": "2025-01-06T08:03:39.19326", "recognitionText": null, "parseDate": "2024-12-09T09:31:49.780793", "updateDate": "2025-07-11T01:58:24.516426", "parser": "SoundItem", "dailyRise": 1750, "dailyRiseForecastingError": 1, "dailyRiseForecasting": 1754.39, "graphWeight": 1, "artistRegion": "US", "authorIdLong": 56461545626419200, "authorUniqueId": "shaniayanofc", "authorNickname": "<PERSON><PERSON>", "isArtist": false, "musicIdLong": 0, "dailyRiseMonth": 0, "dailyRise24hours": 0, "musicStatus": 6, "notAvailable": false, "recognizeStatus": 0, "genre": 493, "appleLink": "https://itunes.apple.com/gb/album/apt/1771105914?i=1771105935&mttnagencyid=s2n&mttnsiteid=125115&mttn3pid=Apple-Shazam&mttnsub1=Shazam_ios&mttnsub2=5348615A-616D-3235-3830-44754D6D5973&itscg=30201&app=itunes&itsct=Shazam_ios", "spotifyLink": "", "downloadLink": "https://audio-ssl.itunes.apple.com/itunes-assets/AudioPreview221/v4/c0/de/10/c0de10b8-72f2-a035-90b7-b1e68c5d043e/mzaf_9202164552993779629.plus.aac.ep.m4a", "youtubeLink": null, "deezerLink": null}, "positionInChart": null, "positionInCurrentChart": null, "calculations": null, "videosList": null}, {"music": {"id": "af9f43bb-8563-4a06-8665-a2770b20418a", "musicId": "7350086979234843438", "reposts": 464800, "duration": 210, "album": "", "url": "https://tiktok-alltrends.com/media/sounds/7350086979234843438.mp3", "title": "Dust Collector", "creator": "ybg lucas", "musicOriginal": true, "musicUrl": "https://www.tiktok.com/music/-7350086979234843438", "cover": "https://sys-datapoint.flaidata.com/Sound/Avatars/7350086979234843438.jpeg", "recognitionTitle": "Dust Collector (Slowed)", "recognitionDatePublished": null, "recognitionAuthor": "1<PERSON>y", "recognitionAlbum": null, "recognitionLabel": null, "recognitionLink": "https://www.shazam.com/track/683664138/dust-collector-slowed", "recognitionDate": "2024-12-01T15:37:27.468573", "recognitionText": null, "parseDate": "2024-04-16T14:25:20.359201", "updateDate": "2025-07-09T00:12:56.294779", "parser": "Feed", "dailyRise": 1450, "dailyRiseForecastingError": 1, "dailyRiseForecasting": 1444.51, "graphWeight": 1, "artistRegion": "US", "authorIdLong": 6683580386038269000, "authorUniqueId": "lucas_wyd", "authorNickname": "lucas_wyd", "isArtist": false, "musicIdLong": 0, "dailyRiseMonth": 0, "dailyRise24hours": 0, "musicStatus": 6, "notAvailable": false, "recognizeStatus": 0, "genre": 518, "appleLink": null, "spotifyLink": null, "downloadLink": null, "youtubeLink": null, "deezerLink": null}, "positionInChart": null, "positionInCurrentChart": null, "calculations": null, "videosList": null}, {"music": {"id": "e8e04bae-6df5-42dc-98c5-96acc7803d38", "musicId": "7371913579282451233", "reposts": 44700, "duration": 312, "album": null, "url": "https://sys-datapoint.flaidata.com/media/sounds/7371913579282451233.mp3", "title": "original sound", "creator": "xavier", "musicOriginal": true, "musicUrl": "https://www.tiktok.com/music/-7371913579282451233", "cover": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRjsHXhmy2pZ1vQACln5GWTCZsnBBo7piQIqAyxaQzWUyoozvOmBQgy6SrNq89go-wYgrc&usqp=CAU", "recognitionTitle": "Kiss of Life", "recognitionDatePublished": "1992", "recognitionAuthor": "<PERSON><PERSON>", "recognitionAlbum": "Love Deluxe", "recognitionLabel": "Epic", "recognitionLink": "https://www.shazam.com/track/372743/kiss-of-life", "recognitionDate": "2024-12-18T11:42:06.023706", "recognitionText": null, "parseDate": "2024-12-18T11:38:42.83885", "updateDate": "2025-07-10T23:07:41.966509", "parser": "SoundItem", "dailyRise": 900, "dailyRiseForecastingError": 0.99, "dailyRiseForecasting": 605.38, "graphWeight": 0.99, "artistRegion": "US", "authorIdLong": 7278747902071457000, "authorUniqueId": "recordsofvinyl", "authorNickname": "xavier", "isArtist": false, "musicIdLong": 0, "dailyRiseMonth": 0, "dailyRise24hours": 0, "musicStatus": 6, "notAvailable": false, "recognizeStatus": 0, "genre": 525, "appleLink": null, "spotifyLink": "", "downloadLink": "https://audio-ssl.itunes.apple.com/itunes-assets/AudioPreview125/v4/69/53/58/69535826-aa1b-132a-a64e-3f57e2ca13dd/mzaf_17789449065565583588.plus.aac.ep.m4a", "youtubeLink": null, "deezerLink": null}, "positionInChart": null, "positionInCurrentChart": null, "calculations": null, "videosList": null}, {"music": {"id": "d8f7f52d-7c1f-485a-a3e6-9f2e77b2db4f", "musicId": "7339252588750031622", "reposts": 193200, "duration": 32, "album": "", "url": "https://v58.tiktokcdn-eu.com/video/tos/useast2a/tos-useast2a-v-27dcd7/oc0xarfJBQqTwBeCIQLoaM1CtgIcSFe8gEQWPT/?a=1233&bti=ODszNWYuMDE6&ch=0&cr=0&dr=0&er=0&lr=default&cd=0%7C0%7C0%7C0&br=250&bt=125&ft=.NpOcInz7Th0PDNKXq8Zmo&mime_type=audio_mpeg&qs=6&rc=NjM5ZDc6NmZlO2Q0ZDQ1ZkBpamc8M285cnA3cTMzNzU8M0AvYzJiYi00XzExYDYzYzVgYSM1ZXBhMmQ0M2FgLS1kMTZzcw%3D%3D&vvpl=1&l=2025070921131740F6AAB3D6F244728968&VExpiration=1752182029&VSignature=zM0_iTeF_o7YKdZcJBMLIA&btag=e00088000", "title": "son original", "creator": "Pere_marcel70", "musicOriginal": true, "musicUrl": "https://www.tiktok.com/music/-7339252588750031622", "cover": "https://sys-datapoint.flaidata.com/Sound/Avatars/7339252588750031622.jpeg", "recognitionTitle": "<PERSON><PERSON><PERSON>", "recognitionDatePublished": "2023", "recognitionAuthor": "Chike & MohBad", "recognitionAlbum": "Son of <PERSON><PERSON>", "recognitionLabel": "Brothers Records, under exclusive license to ONErpm", "recognitionLink": "https://www.shazam.com/track/685827289/egwu", "recognitionDate": "2024-08-29T18:27:50.653656", "recognitionText": null, "parseDate": "2024-03-27T09:45:00.098616", "updateDate": "2025-07-09T21:32:09.756408", "parser": "SoundItem", "dailyRise": 700, "dailyRiseForecastingError": 1, "dailyRiseForecasting": 451.93, "graphWeight": 0.99, "artistRegion": "US", "authorIdLong": 7050999298368292000, "authorUniqueId": "pere_marcel70", "authorNickname": "Pere_marcel70", "isArtist": false, "musicIdLong": 0, "dailyRiseMonth": 0, "dailyRise24hours": 0, "musicStatus": 6, "notAvailable": false, "recognizeStatus": 0, "genre": 565, "appleLink": null, "spotifyLink": "https://open.spotify.com/track/1IMRi5UVOV77PsAgdWDvzh", "downloadLink": "https://audio-ssl.itunes.apple.com/itunes-assets/AudioPreview211/v4/1a/a2/55/1aa2551c-d0ad-052e-5a1a-7faf2c2a0473/mzaf_3887114829391257725.plus.aac.p.m4a", "youtubeLink": null, "deezerLink": "https://www.deezer.com/track/**********"}, "positionInChart": null, "positionInCurrentChart": null, "calculations": null, "videosList": null}, {"music": {"id": "9deb2d3e-fa02-4cb6-85f5-5d272280de62", "musicId": "7122846832836758314", "reposts": 862500, "duration": 72, "album": "", "url": "https://sys-datapoint.flaidata.com/media/sounds/7122846832836758314.mp3", "title": "My baby my baby", "creator": "MadZ", "musicOriginal": true, "musicUrl": "https://www.tiktok.com/music/-7122846832836758314", "cover": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQIMGsNJDHnFAa1ZpGMF7ZCcUFyhrPbz_AmXombV6JNtRx-kHR8R_cMWzUlXYlERgUxaCA&usqp=CAU", "recognitionTitle": "I Bet on Losing Dogs", "recognitionDatePublished": "2016", "recognitionAuthor": "<PERSON><PERSON><PERSON>", "recognitionAlbum": "Puberty 2", "recognitionLabel": "Dead Oceans", "recognitionLink": "https://www.shazam.com/track/312728212/i-bet-on-losing-dogs", "recognitionDate": "2024-09-02T21:22:39.285452", "recognitionText": null, "parseDate": "2023-04-14T12:54:10.335812", "updateDate": "2025-07-11T04:04:44.443612", "parser": "SoundItem", "dailyRise": 650, "dailyRiseForecastingError": 1, "dailyRiseForecasting": 651.83, "graphWeight": 1, "artistRegion": "US", "authorIdLong": 6751363101239674000, "authorUniqueId": "madsendlesswrldd", "authorNickname": "MadZ", "isArtist": false, "musicIdLong": 0, "dailyRiseMonth": 0, "dailyRise24hours": 0, "musicStatus": 6, "notAvailable": false, "recognizeStatus": 0, "genre": 519, "appleLink": null, "spotifyLink": "https://open.spotify.com/track/2Co0IjcLTSHMtodwD4gzfg", "downloadLink": "https://audio-ssl.itunes.apple.com/itunes-assets/AudioPreview125/v4/56/e6/4e/56e64e2f-fb65-308e-4abb-345f8c85faf1/mzaf_10950322843026974978.plus.aac.ep.m4a", "youtubeLink": null, "deezerLink": "https://www.deezer.com/track/124889274"}, "positionInChart": null, "positionInCurrentChart": null, "calculations": null, "videosList": null}, {"music": {"id": "dec9e860-a023-41c6-aba1-d589c8b210b3", "musicId": "6996126290315053829", "reposts": 480700, "duration": 29, "album": "", "url": "https://sys-datapoint.flaidata.com/media/sounds/6996126290315053829.mp3", "title": "remember you", "creator": "Dominurmom", "musicOriginal": true, "musicUrl": "https://www.tiktok.com/music/-6996126290315053829", "cover": "https://sys-datapoint.flaidata.com/Sound/Avatars/6996126290315053829.jpeg", "recognitionTitle": "Remember You (Acoustic Version)", "recognitionDatePublished": "2021", "recognitionAuthor": "Dominurmom", "recognitionAlbum": "Remember You (Acoustic Version) - Single", "recognitionLabel": "Dominurmom Music", "recognitionLink": "https://www.shazam.com/track/584333578/remember-you-acoustic-version", "recognitionDate": "2025-01-14T11:16:48.281544", "recognitionText": null, "parseDate": "2023-05-08T20:15:59.388892", "updateDate": "2025-07-08T22:59:44.818952", "parser": "Feed", "dailyRise": 600, "dailyRiseForecastingError": 1, "dailyRiseForecasting": 171.41, "graphWeight": 1, "artistRegion": "US", "authorIdLong": 6611884616029569000, "authorUniqueId": "dominurmom", "authorNickname": "dominurmom", "isArtist": false, "musicIdLong": 0, "dailyRiseMonth": 0, "dailyRise24hours": 0, "musicStatus": 6, "notAvailable": false, "recognizeStatus": 0, "genre": 519, "appleLink": "https://itunes.apple.com/gb/album/remember-you-acoustic-version/1584939760?i=1584939761&mttnagencyid=s2n&mttnsiteid=125115&mttn3pid=Apple-Shazam&mttnsub1=Shazam_ios&mttnsub2=5348615A-616D-3235-3830-44754D6D5973&itscg=30201&app=itunes&itsct=Shazam_ios", "spotifyLink": "", "downloadLink": "https://audio-ssl.itunes.apple.com/itunes-assets/AudioPreview115/v4/36/2f/81/362f818b-3eb3-42fd-1562-ee505c6db67a/mzaf_7993539091072450587.plus.aac.ep.m4a", "youtubeLink": null, "deezerLink": null}, "positionInChart": null, "positionInCurrentChart": null, "calculations": null, "videosList": null}, {"music": {"id": "785d91e7-4a81-404d-b174-fbc1291c4447", "musicId": "6769179757323029250", "reposts": 183700, "duration": 38, "album": "", "url": "https://sys-datapoint.flaidata.com/media/sounds/6769179757323029250.mp3", "title": "original sound - siya.Sharma🍂", "creator": "👑Just_Me👑siya", "musicOriginal": true, "musicUrl": "https://www.tiktok.com/music/-6769179757323029250", "cover": "https://sys-datapoint.flaidata.com/Sound/Avatars/6769179757323029250.jpeg", "recognitionTitle": "<PERSON><PERSON>", "recognitionDatePublished": "2017", "recognitionAuthor": "<PERSON><PERSON>", "recognitionAlbum": "<PERSON><PERSON> - Single", "recognitionLabel": "Moviebox Birmingham Limited", "recognitionLink": "https://www.shazam.com/track/349863046/mere-rashke-qamar", "recognitionDate": "2024-09-06T02:22:24.693746", "recognitionText": null, "parseDate": "2024-05-19T17:59:24.767517", "updateDate": "2025-07-09T11:25:12.471533", "parser": "SoundItem", "dailyRise": 600, "dailyRiseForecastingError": 0.99, "dailyRiseForecasting": 352.62, "graphWeight": 0.99, "artistRegion": "US", "authorIdLong": 6624857434689372000, "authorUniqueId": "sweet_siya_sharma", "authorNickname": "👑Just_Me👑siya", "isArtist": false, "musicIdLong": 0, "dailyRiseMonth": 0, "dailyRise24hours": 0, "musicStatus": 6, "notAvailable": false, "recognizeStatus": 0, "genre": 506, "appleLink": null, "spotifyLink": "https://open.spotify.com/track/3YH5MnJpi2kgX4xrkBc2NA", "downloadLink": "https://audio-ssl.itunes.apple.com/itunes-assets/AudioPreview125/v4/c3/b6/78/c3b67824-c304-2c59-4542-ee3fb0a689b9/mzaf_5336156934345413463.plus.aac.ep.m4a", "youtubeLink": null, "deezerLink": "https://www.deezer.com/track/389188381"}, "positionInChart": null, "positionInCurrentChart": null, "calculations": null, "videosList": null}, {"music": {"id": "629be5ab-e2db-4112-bc72-50be6ae8e42a", "musicId": "7257105406027483930", "reposts": 164400, "duration": 15, "album": "", "url": "https://sys-datapoint.flaidata.com/media/sounds/7257105406027483930.mp3", "title": "sonido original", "creator": "MELISSA Elly 🌺", "musicOriginal": true, "musicUrl": "https://www.tiktok.com/music/-7257105406027483930", "cover": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTwyM9uqSKe4ZzWDTCCr-fmMO2ZEYe88F0O_g3fzJmCsZNtArlK_-25IcM2x8idz6ytgQg&usqp=CAU", "recognitionTitle": "<PERSON><PERSON>", "recognitionDatePublished": null, "recognitionAuthor": "<PERSON>", "recognitionAlbum": null, "recognitionLabel": null, "recognitionLink": "https://www.shazam.com/track/671811248/sabor-a-mi", "recognitionDate": "2024-08-28T16:09:27.15707", "recognitionText": null, "parseDate": "2023-12-20T15:36:10.740456", "updateDate": "2025-07-11T00:12:20.647415", "parser": "SoundItem", "dailyRise": 550, "dailyRiseForecastingError": 1, "dailyRiseForecasting": 553.77, "graphWeight": 0.99, "artistRegion": "US", "authorIdLong": 125732720680431620, "authorUniqueId": "melis<PERSON><PERSON><PERSON>", "authorNickname": "MELISSA Elly 🌺", "isArtist": false, "musicIdLong": 0, "dailyRiseMonth": 0, "dailyRise24hours": 0, "musicStatus": 6, "notAvailable": false, "recognizeStatus": 0, "genre": 484, "appleLink": null, "spotifyLink": "https://open.spotify.com/track/5kB8WUUla9s2w7yXD7h4CQ", "downloadLink": null, "youtubeLink": null, "deezerLink": null}, "positionInChart": null, "positionInCurrentChart": null, "calculations": null, "videosList": null}], "fIlters": {"skip": 0, "take": 10, "soundType": "Original", "recognize": null, "location": null, "artistLocation": "US", "videosLocation": null, "riseFrom": null, "riseTo": null, "repostsFrom": null, "repostsTo": null, "rateFrom": null, "rateTo": null, "commentFrom": null, "commentTo": null, "collectFrom": null, "collectTo": null, "shareFrom": null, "shareTo": null, "likeFrom": null, "likeTo": null, "viewFrom": null, "viewTo": null, "forecastingFrom": null, "forecastingTo": null, "durationFrom": null, "durationTo": null, "subscribersFrom": null, "subscribersTo": null, "days": 1, "sorting": "rise", "timeMachineDaysAgo": null, "search": null, "category": "90", "autorId": null, "isArtist": null, "videoStates": null, "dateScrapFrom": null, "musicStatus": null, "genre": null, "hashtags": null, "titleAlphabet": null, "creatorAlphabet": null, "dateFrom": null, "dateTo": null, "favoriteForUser": null, "order": "desc"}, "maxRiseRate": 1.121, "minRiseRate": 1.001, "parseConfig": {"id": "15441e40-d429-4e72-52d5-08d9eda6a41d", "name": "", "elapsedTime": 1065, "totalTracks": 271828, "totalStats": 334177, "newTracks": 271828, "tolerance": 10, "parseDate": "2022-02-12T00:00:00"}, "favoriteMusicIds": null, "rowsCount": 10, "totalRows": 11}, "statistics": {"total": 680201, "totalPredicts": 150819320, "succesfullRate": 98, "successPredictions": 63365, "totalPredictions": 64094}, "chartData": [{"id": "5bda06cc-6982-4292-b460-0e5a3beaca6a", "successPercentage": 98, "totalPredictions": 64094, "successPredictions": 63365, "tolerance": 5, "parseDate": "2025-07-10T00:00:00"}, {"id": "c88bf7dc-866c-4891-9e9b-8130e1918849", "successPercentage": 98, "totalPredictions": 494525, "successPredictions": 485834, "tolerance": 5, "parseDate": "2025-07-09T00:00:00"}, {"id": "ffacb4e2-66b3-4c08-ad63-877163f96628", "successPercentage": 97, "totalPredictions": 189394, "successPredictions": 184749, "tolerance": 5, "parseDate": "2025-07-08T00:00:00"}, {"id": "f605abb1-21a2-48a4-ad62-0a754191ede3", "successPercentage": 97, "totalPredictions": 340824, "successPredictions": 333884, "tolerance": 5, "parseDate": "2025-07-07T00:00:00"}, {"id": "29e45564-3b28-4801-8e98-235f750a87c5", "successPercentage": 100, "totalPredictions": 1, "successPredictions": 1, "tolerance": 5, "parseDate": "2025-07-06T00:00:00"}, {"id": "bdb0989e-9567-497a-8e5c-4b740f2ab2b7", "successPercentage": 98, "totalPredictions": 300984, "successPredictions": 297328, "tolerance": 5, "parseDate": "2025-07-05T00:00:00"}, {"id": "e686a09b-329d-4c15-8e94-151671853086", "successPercentage": 98, "totalPredictions": 395696, "successPredictions": 388438, "tolerance": 5, "parseDate": "2025-07-04T00:00:00"}, {"id": "97dad57a-a7b0-4c04-a2fc-1926fc570683", "successPercentage": 98, "totalPredictions": 157067, "successPredictions": 154138, "tolerance": 5, "parseDate": "2025-07-03T00:00:00"}, {"id": "3dabcfbc-3b7e-48a4-be27-7a1b0fbf1492", "successPercentage": 98, "totalPredictions": 205144, "successPredictions": 202768, "tolerance": 5, "parseDate": "2025-07-02T00:00:00"}, {"id": "60fe5c4b-1fbb-434a-adc5-d4beb480fd7b", "successPercentage": 98, "totalPredictions": 106338, "successPredictions": 104441, "tolerance": 5, "parseDate": "2025-07-01T00:00:00"}, {"id": "5944a747-f460-4cbc-a9c9-8b5707004504", "successPercentage": 98, "totalPredictions": 274010, "successPredictions": 270692, "tolerance": 5, "parseDate": "2025-06-30T00:00:00"}, {"id": "f5ba0875-1724-4ad9-9b60-1a8e5792bf25", "successPercentage": 98, "totalPredictions": 236273, "successPredictions": 232033, "tolerance": 5, "parseDate": "2025-06-29T00:00:00"}, {"id": "337ecb5b-a81f-4490-8b7a-ebadc9aa630f", "successPercentage": 98, "totalPredictions": 320063, "successPredictions": 314468, "tolerance": 5, "parseDate": "2025-06-28T00:00:00"}, {"id": "69f213f6-1df2-4345-b26f-a0312de37510", "successPercentage": 79, "totalPredictions": 5018, "successPredictions": 3993, "tolerance": 5, "parseDate": "2025-06-27T00:00:00"}, {"id": "12a71ca8-bf1d-4ffe-8c7a-9e343d9611c8", "successPercentage": 98, "totalPredictions": 92229, "successPredictions": 90685, "tolerance": 5, "parseDate": "2025-06-26T00:00:00"}, {"id": "08361b7b-1156-4278-a548-55c69bd3c785", "successPercentage": 96, "totalPredictions": 51969, "successPredictions": 50076, "tolerance": 5, "parseDate": "2025-06-25T00:00:00"}, {"id": "f6a80733-96a7-4a3b-b7ea-2ef73190b3ef", "successPercentage": 98, "totalPredictions": 125498, "successPredictions": 123335, "tolerance": 5, "parseDate": "2025-06-24T00:00:00"}, {"id": "9759d88b-4e42-4d9a-8a16-81b6421e845c", "successPercentage": 98, "totalPredictions": 341264, "successPredictions": 335392, "tolerance": 5, "parseDate": "2025-06-23T00:00:00"}, {"id": "2daaacd7-6cf6-49e5-8baa-0e87c2c1ecdc", "successPercentage": 97, "totalPredictions": 3700, "successPredictions": 3608, "tolerance": 5, "parseDate": "2025-06-22T00:00:00"}, {"id": "ea08c183-8a2e-424d-be0b-4846779dfe56", "successPercentage": 96, "totalPredictions": 49790, "successPredictions": 48119, "tolerance": 5, "parseDate": "2025-06-21T00:00:00"}], "dataSnapshots": [{"id": "f91c0187-0cc3-447d-a422-95f68c57f78c", "musicsCountLong": 675636, "videosCountLong": 48989566, "bloggersCountLong": 952957, "videoStatesLong": 26743614, "bloggersStatesLong": 86800034, "musicStatesLong": 146303233, "totalInfluencersLong": 952957, "influencerClipsLong": 655912967, "influencerLikesLong": 10081384868711, "influencerFollowersLong": 361430197514, "date": "2025-06-16T04:00:00.985853"}, {"id": "da6b8383-16c0-40fb-922e-3df3c3285e4d", "musicsCountLong": 673394, "videosCountLong": 49012468, "bloggersCountLong": 954956, "videoStatesLong": 23890127, "bloggersStatesLong": 86818632, "musicStatesLong": 147183863, "totalInfluencersLong": 954956, "influencerClipsLong": 656470739, "influencerLikesLong": 10090668609105, "influencerFollowersLong": 361580639180, "date": "2025-06-23T04:00:05.063856"}, {"id": "776d381e-5a9f-4935-9fd3-3d75eb082d61", "musicsCountLong": 675442, "videosCountLong": 50991981, "bloggersCountLong": 956875, "videoStatesLong": 22549869, "bloggersStatesLong": 87295904, "musicStatesLong": 148426330, "totalInfluencersLong": 956875, "influencerClipsLong": 662051915, "influencerLikesLong": 10167964144157, "influencerFollowersLong": 363212661278, "date": "2025-06-30T04:00:01.098341"}, {"id": "c203d88b-77a5-4f7e-93f1-3019086f215d", "musicsCountLong": 678479, "videosCountLong": 52092018, "bloggersCountLong": 959924, "videoStatesLong": 21395983, "bloggersStatesLong": 87574753, "musicStatesLong": 149792488, "totalInfluencersLong": 959924, "influencerClipsLong": 665737509, "influencerLikesLong": 10213681419866, "influencerFollowersLong": 364121669170, "date": "2025-07-07T04:00:00.277893"}], "previousMonthDataSnapshots": [{"id": "d57f15d0-d72c-4947-81a0-ad783e3da1b6", "musicsCountLong": 669965, "videosCountLong": 43399752, "bloggersCountLong": 943607, "videoStatesLong": 36687790, "bloggersStatesLong": 84841244, "musicStatesLong": 139487691, "totalInfluencersLong": 943607, "influencerClipsLong": 644938476, "influencerLikesLong": 9909116321594, "influencerFollowersLong": 357913599398, "date": "2025-05-12T04:00:01.847962"}, {"id": "4c93475e-798b-4c53-8eba-2691c84f43ea", "musicsCountLong": 672502, "videosCountLong": 44991498, "bloggersCountLong": 945893, "videoStatesLong": 35239251, "bloggersStatesLong": 85389234, "musicStatesLong": 141393101, "totalInfluencersLong": 945893, "influencerClipsLong": 647495183, "influencerLikesLong": 9949166944398, "influencerFollowersLong": 358785983376, "date": "2025-05-19T04:00:04.214363"}, {"id": "bd8cc816-591f-44fc-b4c8-fe5ebd720f0f", "musicsCountLong": 671491, "videosCountLong": 46676110, "bloggersCountLong": 946951, "videoStatesLong": 34189925, "bloggersStatesLong": 85976086, "musicStatesLong": 142488365, "totalInfluencersLong": 946951, "influencerClipsLong": 650212743, "influencerLikesLong": 9991214904391, "influencerFollowersLong": 359683403060, "date": "2025-05-26T04:00:02.536166"}, {"id": "bd8a506a-4e15-4c05-ac25-c5cbc76b4930", "musicsCountLong": 674507, "videosCountLong": 47430482, "bloggersCountLong": 949248, "videoStatesLong": 32913905, "bloggersStatesLong": 86264535, "musicStatesLong": 144023020, "totalInfluencersLong": 949248, "influencerClipsLong": 652058689, "influencerLikesLong": 10028015732285, "influencerFollowersLong": 360325103015, "date": "2025-06-02T04:00:03.20417"}, {"id": "ad3c1cd2-e34c-44d4-8c41-01dd625fc97a", "musicsCountLong": 675811, "videosCountLong": 48925822, "bloggersCountLong": 950360, "videoStatesLong": 33333709, "bloggersStatesLong": 86779439, "musicStatesLong": 144885292, "totalInfluencersLong": 950360, "influencerClipsLong": 655227116, "influencerLikesLong": 10070135043671, "influencerFollowersLong": 361212083830, "date": "2025-06-09T04:00:03.92283"}], "tikTokCategories": [{"id": 173, "categoryName": "Advertisement", "sounds": 767, "videos": 57590, "authors": 1624}, {"id": 146, "categoryName": "Babies", "sounds": 4248, "videos": 137506, "authors": 4744}, {"id": 143, "categoryName": "Beauty", "sounds": 3279, "videos": 338468, "authors": 10198}, {"id": 183, "categoryName": "Business & Finance", "sounds": 411, "videos": 59067, "authors": 1624}, {"id": 117, "categoryName": "Campus Life", "sounds": 1707, "videos": 28971, "authors": 1154}, {"id": 140, "categoryName": "Cars, Trucks & Motorcycles", "sounds": 8152, "videos": 290985, "authors": 7001}, {"id": 115, "categoryName": "Celebrity Clips & Variety Show", "sounds": 5698, "videos": 318830, "authors": 8434}, {"id": 111, "categoryName": "Comics & Cartoon, Anime", "sounds": 4616, "videos": 231104, "authors": 5916}, {"id": 121, "categoryName": "Cooking", "sounds": 3497, "videos": 299057, "authors": 8781}, {"id": 118, "categoryName": "Cosplay", "sounds": 1294, "videos": 35624, "authors": 963}, {"id": 136, "categoryName": "Dance", "sounds": 1689, "videos": 114041, "authors": 3264}, {"id": 122, "categoryName": "Diary & VLOG", "sounds": 5394, "videos": 381798, "authors": 16580}, {"id": 161, "categoryName": "DIY & Handcrafts", "sounds": 712, "videos": 19179, "authors": 632}, {"id": 157, "categoryName": "Drinks", "sounds": 2230, "videos": 102467, "authors": 4728}, {"id": 167, "categoryName": "Entertainment News", "sounds": 563, "videos": 54888, "authors": 1526}, {"id": 190, "categoryName": "Environmental Protections", "sounds": 12, "videos": 1132, "authors": 48}, {"id": 155, "categoryName": "Extreme Sports", "sounds": 3413, "videos": 90594, "authors": 2617}, {"id": 113, "categoryName": "Family", "sounds": 6476, "videos": 264033, "authors": 9303}, {"id": 170, "categoryName": "Farm Animals", "sounds": 2992, "videos": 62187, "authors": 1753}, {"id": 120, "categoryName": "Finger Dance & Basic Dance", "sounds": 5819, "videos": 347060, "authors": 12737}, {"id": 186, "categoryName": "Fishing, Hunting & Camping", "sounds": 712, "videos": 34791, "authors": 911}, {"id": 151, "categoryName": "Fitness", "sounds": 4742, "videos": 212112, "authors": 6004}, {"id": 156, "categoryName": "Food Display", "sounds": 3315, "videos": 116487, "authors": 4773}, {"id": 164, "categoryName": "Food Tour & Recommendations", "sounds": 1577, "videos": 105740, "authors": 3720}, {"id": 149, "categoryName": "Graphic Art", "sounds": 1549, "videos": 45894, "authors": 1276}, {"id": 123, "categoryName": "Hair", "sounds": 4391, "videos": 234317, "authors": 6520}, {"id": 180, "categoryName": "Health & Wellness", "sounds": 898, "videos": 118269, "authors": 3347}, {"id": 94, "categoryName": "Hilarious Fails", "sounds": 69, "videos": 1832, "authors": 58}, {"id": 119, "categoryName": "Home & Garden", "sounds": 2910, "videos": 167684, "authors": 4932}, {"id": 147, "categoryName": "Humanities", "sounds": 315, "videos": 40258, "authors": 1166}, {"id": 177, "categoryName": "Life Hacks", "sounds": 183, "videos": 13189, "authors": 483}, {"id": 108, "categoryName": "Lip-sync", "sounds": 31200, "videos": 1909523, "authors": 37973}, {"id": 166, "categoryName": "Magic", "sounds": 93, "videos": 7279, "authors": 220}, {"id": 162, "categoryName": "Motivation", "sounds": 1061, "videos": 52146, "authors": 1780}, {"id": 124, "categoryName": "Movies & TV works", "sounds": 4917, "videos": 412634, "authors": 10061}, {"id": 126, "categoryName": "Mukbangs & Tasting", "sounds": 1873, "videos": 154266, "authors": 4903}, {"id": 128, "categoryName": "Music", "sounds": 7261, "videos": 497434, "authors": 11449}, {"id": 165, "categoryName": "Nail Art", "sounds": 1092, "videos": 45345, "authors": 1087}, {"id": 93, "categoryName": "Non-Video Games", "sounds": 828, "videos": 61771, "authors": 1877}, {"id": 176, "categoryName": "<PERSON><PERSON>", "sounds": 294, "videos": 33251, "authors": 830}, {"id": 98, "categoryName": "Outfit", "sounds": 10326, "videos": 707577, "authors": 17267}, {"id": 130, "categoryName": "Pets", "sounds": 5806, "videos": 316723, "authors": 7718}, {"id": 171, "categoryName": "Photography", "sounds": 613, "videos": 22791, "authors": 836}, {"id": 178, "categoryName": "Pranks", "sounds": 259, "videos": 50389, "authors": 1706}, {"id": 175, "categoryName": "Professional Personal Development", "sounds": 405, "videos": 43458, "authors": 1138}, {"id": 187, "categoryName": "Professional Special Effects", "sounds": 24, "videos": 777, "authors": 20}, {"id": 163, "categoryName": "Recreation Facilities", "sounds": 1099, "videos": 31578, "authors": 916}, {"id": 91, "categoryName": "Romance", "sounds": 6565, "videos": 167420, "authors": 5222}, {"id": 133, "categoryName": "Scenery & Plants", "sounds": 1707, "videos": 28505, "authors": 996}, {"id": 141, "categoryName": "School Education", "sounds": 240, "videos": 13749, "authors": 355}, {"id": 174, "categoryName": "Science", "sounds": 170, "videos": 14382, "authors": 452}, {"id": 112, "categoryName": "Scripted Comedy", "sounds": 699, "videos": 225055, "authors": 6841}, {"id": 168, "categoryName": "Scripted Drama", "sounds": 621, "videos": 61738, "authors": 1740}, {"id": 92, "categoryName": "Selfies", "sounds": 9106, "videos": 154004, "authors": 6063}, {"id": 131, "categoryName": "Singing & Instruments", "sounds": 1314, "videos": 262522, "authors": 8163}, {"id": 153, "categoryName": "Social Issues", "sounds": 1335, "videos": 156495, "authors": 4704}, {"id": 182, "categoryName": "Social News", "sounds": 230, "videos": 62141, "authors": 1273}, {"id": 152, "categoryName": "Software & APPs", "sounds": 362, "videos": 31495, "authors": 1059}, {"id": 172, "categoryName": "Sports News", "sounds": 616, "videos": 67411, "authors": 1598}, {"id": 179, "categoryName": "Street Interviews & Social Experiments", "sounds": 368, "videos": 70568, "authors": 2079}, {"id": 158, "categoryName": "Supernatural & Horror", "sounds": 149, "videos": 28776, "authors": 819}, {"id": 169, "categoryName": "Tech Products & Infos", "sounds": 1578, "videos": 132569, "authors": 3756}, {"id": 184, "categoryName": "Theatre & Stage", "sounds": 52, "videos": 2910, "authors": 103}, {"id": 135, "categoryName": "Toys & Collectables", "sounds": 2358, "videos": 93512, "authors": 2804}, {"id": 189, "categoryName": "Traditional Culture", "sounds": 2, "videos": 66, "authors": 3}, {"id": 114, "categoryName": "Traditional Sports", "sounds": 7506, "videos": 400541, "authors": 9669}, {"id": 127, "categoryName": "Travel", "sounds": 2980, "videos": 137642, "authors": 4664}, {"id": 145, "categoryName": "Video Games", "sounds": 6488, "videos": 457681, "authors": 9058}, {"id": 154, "categoryName": "Work & Jobs", "sounds": 3004, "videos": 104518, "authors": 3499}], "artistRegions": ["CH", "UY", "LI", "IE", "PA", "AE", "LT", "JP", "DE", "GR", "NG", "JO", "KR", "MT", "RU", "FO", "UZ", "LB", "EG", "IN", "IT", "CA", "SA", "HN", "AT", "ZA", "CR", "ES", "DO", "KW", "BY", "AZ", "NL", "US", "RO", "NZ", "NO", "PE", "BD", "CL", "LU", "CO", "TH", "IL", "GB", "EC", "TW", "UA", "TR", "RS", "VN", "QA", "PT", "SE", "MY", "JM", "KZ", "PH", "FR", "IQ", "CY", "AR", "BR", "PK", "AO", "FI", "MX", "GE", "MA", "AM", "AU", "BE", "SG", "DK", "CD", "PR", "PL", "ID", "MD", "TN", "MC", "LV", "GH", "BH", "RW", "BJ", "MU", "TJ", "IS", "NA", "BO", "GA", "LK", "HK", "VE", "TZ", "DZ", "LA", "KH", "CI", "ME", "EE", "SL", "GT", "SM", "HU", "BA", "CW", "SV", "KG", "MO", "SN", "TM", "KE", "BG", "CZ", "AD", "AF", "AL", "AS", "AW", "BF", "BM", "BN", "BS", "BT", "BW", "BZ", "CG", "CM", "CN", "DM", "ET", "FJ", "GG", "GI", "GN", "GP", "GY", "HR", "HT", "IR", "KN", "KY", "LR", "LS", "LY", "MG", "MH", "MK", "ML", "MM", "MQ", "MR", "MV", "MW", "MZ", "NC", "NE", "NI", "NP", "OM", "PF", "PG", "PY", "RE", "SC", "SD", "SI", "SK", "SO", "SR", "ST", "SZ", "TC", "TD", "TG", "TO", "TT", "UG", "UM", "VC", "VI", "WS", "YE", "ZM", "ZW", "CV", "PS", "BB", "SY", "MN", "GM", "AG", "GU", "VU", "DJ", "GF", "LC", "TL", "GD", "SS", "TK", "KM", "CK", "ER", "AI", "FM", "GW", "PW", "MP", "GL", "TF", "GQ", "VG", "BI", "NU", "YT", "JE", "GS", "FK", "AQ", "MF", "SB", "KI", "TV", "BQ", "CF", "IM", "EH", "SJ", "AX", "CU", "SX", "BL", "CX", "MS", "NR"], "worldRegions": [{"regionName": "CIS", "countryCodes": "AM,AZ,BY,GE,KZ,KG,MD,RU,TJ,TM,UA,UZ"}, {"regionName": "Other", "countryCodes": "AC,AX,BV,FO,GG,GI,GS,IM,JE,RU,SJ,TF,VA"}, {"regionName": "Oceania", "countryCodes": "AS,AU,CC,CK,CX,FJ,FM,GU,KI,MH,MP,NC,NF,NR,NU,NZ,PF,PG,PN,PW,SB,TK,TO,TV,UM,VU,WF,WS"}, {"regionName": "Europe", "countryCodes": "AD,AL,AM,AT,BA,BE,BG,BY,CH,CY,CZ,DE,DK,EE,ES,FI,FR,GB,GE,GR,HR,HU,IE,IS,IT,LI,LT,LU,LV,MC,MD,ME,MK,MT,NL,NO,PL,PT,RO,RS,SE,SI,SK,SM,UA"}, {"regionName": "Asia", "countryCodes": "AE,AF,AQ,AZ,BD,BH,BN,BT,CN,HK,HM,ID,IL,IN,IQ,IR,JO,JP,KG,KH,KP,KR,KW,KZ,LA,LB,LK,MM,MN,MO,MV,MY,NP,OM,PH,PK,PS,QA,SA,SG,SY,TH,TJ,TL,TM,TR,TW,UZ,VN,YE"}, {"regionName": "South America", "countryCodes": "AR,BO,BR,CL,CO,EC,FK,GF,GY,PE,PY,SR,UY,VE"}, {"regionName": "Africa", "countryCodes": "AO,BF,BI,BJ,BW,CD,CF,CG,CI,CM,CV,DJ,DZ,EG,EH,ER,ET,GA,GH,GM,GN,GQ,GW,IO,KE,KM,LR,LS,LY,MA,MG,ML,MR,MU,MW,MZ,NA,NE,NG,RE,RW,SC,SD,SH,SL,SN,SO,SS,ST,SZ,TD,TG,TN,TZ,UG,YT,ZA,ZM,ZW"}, {"regionName": "North America", "countryCodes": "AG,AI,AW,BB,BL,BM,BQ,BS,BZ,CA,CR,CU,CW,DM,DO,GD,GL,GP,GT,HN,HT,JM,KN,KY,LC,MF,MQ,MS,MX,NI,PA,PM,PR,SV,SX,TC,TT,US,VC,VG,VI"}, {"regionName": "CS", "countryCodes": "UA,GE,KZ,AM,BY"}], "presetFilters": [{"id": "bd381a34-48fa-43c6-81c2-3030676216be", "itemType": 2, "presetName": "Top 30sec last week AE", "queryString": "sorting=rise&days=7&order=desc&take=20&duration=30&videosLocation=AE&durationFrom=15&durationTo=30", "provider": null}, {"id": "430ee2c3-857a-4192-8963-6fa68799d951", "itemType": 2, "presetName": "Top 60sec last week World", "queryString": "sorting=rise&days=7&order=desc&take=20&duration=30&durationFrom=15&durationTo=30", "provider": null}, {"id": "042506c1-af01-4c13-b242-2939de134210", "itemType": 2, "presetName": "Top 60sec last week NL", "queryString": "sorting=rise&days=7&order=desc&take=20&duration=30&videosLocation=NL&durationFrom=15&durationTo=30", "provider": null}, {"id": "0f14410c-1f76-4cb2-91c4-f822d307b07f", "itemType": 2, "presetName": "Top 60sec last week UK", "queryString": "sorting=rise&days=7&order=desc&take=20&duration=30&videosLocation=GB&durationFrom=15&durationTo=60", "provider": null}, {"id": "8c197794-9a79-4acc-aacb-cf15202a22cd", "itemType": 2, "presetName": "A&R Week Grow / Followers to 10K / videos to 5K", "queryString": "sorting=rate&days=7&order=desc&take=20&repostsFrom=500&repostsTo=5000&reposts=500%2C5000&subscribersFrom=1&subscribersTo=10000", "provider": null}, {"id": "2607ab22-fc0c-4152-997d-1a303085d2c5", "itemType": 2, "presetName": "Top 15sec Most Reposted World", "queryString": "sorting=reposts&order=desc&take=20&duration=15&search=&durationFrom=0&durationTo=15", "provider": null}, {"id": "093c82e0-40f6-46d1-acc0-bda6e716b47f", "itemType": 2, "presetName": "Top Rised Today World", "queryString": "take=20&days=1&riseFrom=1000&sorting=rise&order=desc", "provider": null}, {"id": "eec1bec9-687a-4a70-af7c-9777b091d957", "itemType": 2, "presetName": "Top Rised 3 days World", "queryString": "take=20&days=3&riseFrom=1000&sorting=rise&order=desc", "provider": null}, {"id": "e3ae9317-2228-4a3e-bb6e-fa6c1f631d9d", "itemType": 2, "presetName": "Top Rised 3 days USA", "queryString": "take=20&days=3&riseFrom=1000&sorting=rise&order=desc&videosLocation=US", "provider": null}, {"id": "d50c13b5-7453-4546-a5f5-d8ebc9dfd62d", "itemType": 2, "presetName": "Top Rised 3 days CA", "queryString": "take=20&days=3&riseFrom=1000&sorting=rise&order=desc&videosLocation=CA", "provider": null}, {"id": "e8865abf-bc57-44d9-b6ca-f3233c8bfb50", "itemType": 2, "presetName": "Top Rised 3 days UK", "queryString": "take=20&days=3&riseFrom=1000&sorting=rise&order=desc&videosLocation=GB", "provider": null}, {"id": "6aa00843-8292-4015-ae4e-1f05b77a250c", "itemType": 2, "presetName": "Top 30sec last week World", "queryString": "sorting=rise&days=7&order=desc&take=20&duration=30&durationFrom=15&durationTo=30", "provider": null}, {"id": "cdb81bcd-68b0-485b-b0a8-2bc91b3a0c58", "itemType": 2, "presetName": "Top 30sec last week NL", "queryString": "sorting=rise&days=7&order=desc&take=20&duration=30&videosLocation=NL&durationFrom=15&durationTo=30", "provider": null}], "genres": [{"id": 289, "parentGenre": 0, "genreLevel": 0, "genreName": "contemporary blues"}, {"id": 290, "parentGenre": 0, "genreLevel": 0, "genreName": "latin jazz"}, {"id": 291, "parentGenre": 0, "genreLevel": 0, "genreName": "afrikaans"}, {"id": 292, "parentGenre": 0, "genreLevel": 0, "genreName": "hiphop"}, {"id": 299, "parentGenre": 0, "genreLevel": 0, "genreName": "wedding music"}, {"id": 300, "parentGenre": 0, "genreLevel": 0, "genreName": "musical instrument"}, {"id": 301, "parentGenre": 0, "genreLevel": 0, "genreName": "violin, fiddle"}, {"id": 302, "parentGenre": 0, "genreLevel": 0, "genreName": "goth rock"}, {"id": 303, "parentGenre": 0, "genreLevel": 0, "genreName": "speech"}, {"id": 304, "parentGenre": 0, "genreLevel": 0, "genreName": "crow"}, {"id": 305, "parentGenre": 0, "genreLevel": 0, "genreName": "caw"}, {"id": 306, "parentGenre": 0, "genreLevel": 0, "genreName": "tango"}, {"id": 313, "parentGenre": 0, "genreLevel": 0, "genreName": "stories"}, {"id": 314, "parentGenre": 0, "genreLevel": 0, "genreName": "trad jazz"}, {"id": 315, "parentGenre": 0, "genreLevel": 0, "genreName": "hawaii"}, {"id": 316, "parentGenre": 0, "genreLevel": 0, "genreName": "new acoustic"}, {"id": 317, "parentGenre": 0, "genreLevel": 0, "genreName": "doo wop"}, {"id": 318, "parentGenre": 0, "genreLevel": 0, "genreName": "yoga"}, {"id": 319, "parentGenre": 0, "genreLevel": 0, "genreName": "sound effects"}, {"id": 320, "parentGenre": 0, "genreLevel": 0, "genreName": "urdu"}, {"id": 321, "parentGenre": 0, "genreLevel": 0, "genreName": "spoken word"}, {"id": 322, "parentGenre": 0, "genreLevel": 0, "genreName": "<PERSON><PERSON><PERSON>"}, {"id": 323, "parentGenre": 0, "genreLevel": 0, "genreName": "tex-mex"}, {"id": 324, "parentGenre": 0, "genreLevel": 0, "genreName": "outlaw country"}, {"id": 325, "parentGenre": 0, "genreLevel": 0, "genreName": "alternative country"}, {"id": 326, "parentGenre": 0, "genreLevel": 0, "genreName": "celtic folk"}, {"id": 327, "parentGenre": 0, "genreLevel": 0, "genreName": "maghreb dance"}, {"id": 328, "parentGenre": 0, "genreLevel": 0, "genreName": "death metal/black metal"}, {"id": 329, "parentGenre": 0, "genreLevel": 0, "genreName": "healing"}, {"id": 330, "parentGenre": 0, "genreLevel": 0, "genreName": "south africa"}, {"id": 331, "parentGenre": 0, "genreLevel": 0, "genreName": "chinese rock"}, {"id": 332, "parentGenre": 0, "genreLevel": 0, "genreName": "fado"}, {"id": 333, "parentGenre": 0, "genreLevel": 0, "genreName": "kannada"}, {"id": 334, "parentGenre": 0, "genreLevel": 0, "genreName": "iraqi folk"}, {"id": 335, "parentGenre": 0, "genreLevel": 0, "genreName": "polka"}, {"id": 336, "parentGenre": 0, "genreLevel": 0, "genreName": "medieval era"}, {"id": 337, "parentGenre": 0, "genreLevel": 0, "genreName": "mambo"}, {"id": 338, "parentGenre": 0, "genreLevel": 0, "genreName": "east coast rap"}, {"id": 339, "parentGenre": 0, "genreLevel": 0, "genreName": "hard bop"}, {"id": 340, "parentGenre": 0, "genreLevel": 0, "genreName": "tai-pop"}, {"id": 341, "parentGenre": 0, "genreLevel": 0, "genreName": "c-pop"}, {"id": 342, "parentGenre": 0, "genreLevel": 0, "genreName": "avant-garde"}, {"id": 343, "parentGenre": 0, "genreLevel": 0, "genreName": "kizomba"}, {"id": 344, "parentGenre": 0, "genreLevel": 0, "genreName": "soca"}, {"id": 345, "parentGenre": 0, "genreLevel": 0, "genreName": "choro"}, {"id": 346, "parentGenre": 0, "genreLevel": 0, "genreName": "soft rock"}, {"id": 347, "parentGenre": 0, "genreLevel": 0, "genreName": "delta blues"}, {"id": 348, "parentGenre": 0, "genreLevel": 0, "genreName": "glam rock"}, {"id": 349, "parentGenre": 0, "genreLevel": 0, "genreName": "trot"}, {"id": 350, "parentGenre": 0, "genreLevel": 0, "genreName": "pinoy pop"}, {"id": 351, "parentGenre": 0, "genreLevel": 0, "genreName": "kwaito"}, {"id": 352, "parentGenre": 0, "genreLevel": 0, "genreName": "taarab"}, {"id": 353, "parentGenre": 0, "genreLevel": 0, "genreName": "japan"}, {"id": 354, "parentGenre": 0, "genreLevel": 0, "genreName": "korean folk-pop"}, {"id": 355, "parentGenre": 0, "genreLevel": 0, "genreName": "ska"}, {"id": 356, "parentGenre": 0, "genreLevel": 0, "genreName": "celtic"}, {"id": 357, "parentGenre": 0, "genreLevel": 0, "genreName": "early music"}, {"id": 358, "parentGenre": 0, "genreLevel": 0, "genreName": "sacred"}, {"id": 359, "parentGenre": 0, "genreLevel": 0, "genreName": "roots rock"}, {"id": 360, "parentGenre": 0, "genreLevel": 0, "genreName": "russian rock"}, {"id": 361, "parentGenre": 0, "genreLevel": 0, "genreName": "christmas: religious"}, {"id": 362, "parentGenre": 0, "genreLevel": 0, "genreName": "electric blues"}, {"id": 363, "parentGenre": 0, "genreLevel": 0, "genreName": "avant-garde jazz"}, {"id": 364, "parentGenre": 0, "genreLevel": 0, "genreName": "beatboxing"}, {"id": 365, "parentGenre": 0, "genreLevel": 0, "genreName": "vocal music"}, {"id": 366, "parentGenre": 0, "genreLevel": 0, "genreName": "drum"}, {"id": 367, "parentGenre": 0, "genreLevel": 0, "genreName": "bass drum"}, {"id": 368, "parentGenre": 0, "genreLevel": 0, "genreName": "taiwanese folk"}, {"id": 369, "parentGenre": 0, "genreLevel": 0, "genreName": "american trad rock"}, {"id": 373, "parentGenre": 0, "genreLevel": 0, "genreName": "accordion"}, {"id": 374, "parentGenre": 0, "genreLevel": 0, "genreName": "marimba, xylophone"}, {"id": 375, "parentGenre": 0, "genreLevel": 0, "genreName": "glockenspiel"}, {"id": 376, "parentGenre": 0, "genreLevel": 0, "genreName": "chachacha"}, {"id": 377, "parentGenre": 0, "genreLevel": 0, "genreName": "singing bowl"}, {"id": 378, "parentGenre": 0, "genreLevel": 0, "genreName": "gong"}, {"id": 379, "parentGenre": 0, "genreLevel": 0, "genreName": "car"}, {"id": 380, "parentGenre": 0, "genreLevel": 0, "genreName": "vehicle"}, {"id": 381, "parentGenre": 0, "genreLevel": 0, "genreName": "chopping (food)"}, {"id": 382, "parentGenre": 0, "genreLevel": 0, "genreName": "hum"}, {"id": 383, "parentGenre": 0, "genreLevel": 0, "genreName": "throbbing"}, {"id": 384, "parentGenre": 0, "genreLevel": 0, "genreName": "singing"}, {"id": 385, "parentGenre": 0, "genreLevel": 0, "genreName": "heart murmur"}, {"id": 386, "parentGenre": 0, "genreLevel": 0, "genreName": "thunderstorm"}, {"id": 387, "parentGenre": 0, "genreLevel": 0, "genreName": "rain"}, {"id": 388, "parentGenre": 0, "genreLevel": 0, "genreName": "thunder"}, {"id": 389, "parentGenre": 0, "genreLevel": 0, "genreName": "chirp, tweet"}, {"id": 390, "parentGenre": 0, "genreLevel": 0, "genreName": "bird vocalization, bird call, bird song"}, {"id": 391, "parentGenre": 0, "genreLevel": 0, "genreName": "classic christian"}, {"id": 392, "parentGenre": 0, "genreLevel": 0, "genreName": "flute"}, {"id": 393, "parentGenre": 0, "genreLevel": 0, "genreName": "wind instrument, woodwind instrument"}, {"id": 394, "parentGenre": 0, "genreLevel": 0, "genreName": "sound effect"}, {"id": 395, "parentGenre": 0, "genreLevel": 0, "genreName": "ringtone"}, {"id": 396, "parentGenre": 0, "genreLevel": 0, "genreName": "brass instrument"}, {"id": 397, "parentGenre": 0, "genreLevel": 0, "genreName": "trumpet"}, {"id": 398, "parentGenre": 0, "genreLevel": 0, "genreName": "ding-dong"}, {"id": 399, "parentGenre": 0, "genreLevel": 0, "genreName": "electric shaver, electric razor"}, {"id": 400, "parentGenre": 0, "genreLevel": 0, "genreName": "lullaby"}, {"id": 401, "parentGenre": 0, "genreLevel": 0, "genreName": "humming"}, {"id": 402, "parentGenre": 0, "genreLevel": 0, "genreName": "chinese strings"}, {"id": 403, "parentGenre": 0, "genreLevel": 0, "genreName": "korean"}, {"id": 404, "parentGenre": 0, "genreLevel": 0, "genreName": "calypso"}, {"id": 405, "parentGenre": 0, "genreLevel": 0, "genreName": "iberia"}, {"id": 406, "parentGenre": 0, "genreLevel": 0, "genreName": "country gospel"}, {"id": 407, "parentGenre": 0, "genreLevel": 0, "genreName": "easter"}, {"id": 408, "parentGenre": 0, "genreLevel": 0, "genreName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 409, "parentGenre": 0, "genreLevel": 0, "genreName": "kud<PERSON>"}, {"id": 410, "parentGenre": 0, "genreLevel": 0, "genreName": "rockabilly"}, {"id": 411, "parentGenre": 0, "genreLevel": 0, "genreName": "chinese classical"}, {"id": 412, "parentGenre": 0, "genreLevel": 0, "genreName": "novelty"}, {"id": 413, "parentGenre": 0, "genreLevel": 0, "genreName": "chamber music"}, {"id": 414, "parentGenre": 0, "genreLevel": 0, "genreName": "chicago blues"}, {"id": 415, "parentGenre": 0, "genreLevel": 0, "genreName": "religious"}, {"id": 416, "parentGenre": 0, "genreLevel": 0, "genreName": "contemporary jazz"}, {"id": 417, "parentGenre": 0, "genreLevel": 0, "genreName": "khaleeji folk"}, {"id": 418, "parentGenre": 0, "genreLevel": 0, "genreName": "choral"}, {"id": 419, "parentGenre": 0, "genreLevel": 0, "genreName": "music of asia"}, {"id": 420, "parentGenre": 0, "genreLevel": 0, "genreName": "pop music"}, {"id": 421, "parentGenre": 0, "genreLevel": 0, "genreName": "cool jazz"}, {"id": 422, "parentGenre": 0, "genreLevel": 0, "genreName": "surf"}, {"id": 423, "parentGenre": 0, "genreLevel": 0, "genreName": "motown"}, {"id": 424, "parentGenre": 0, "genreLevel": 0, "genreName": "narration, monologue"}, {"id": 425, "parentGenre": 0, "genreLevel": 0, "genreName": "speech synthesizer"}, {"id": 426, "parentGenre": 0, "genreLevel": 0, "genreName": "percussion"}, {"id": 427, "parentGenre": 0, "genreLevel": 0, "genreName": "bicycle bell"}, {"id": 428, "parentGenre": 0, "genreLevel": 0, "genreName": "ding"}, {"id": 429, "parentGenre": 0, "genreLevel": 0, "genreName": "silence"}, {"id": 430, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic music"}, {"id": 431, "parentGenre": 0, "genreLevel": 0, "genreName": "frevo"}, {"id": 432, "parentGenre": 0, "genreLevel": 0, "genreName": "computer keyboard"}, {"id": 433, "parentGenre": 0, "genreLevel": 0, "genreName": "crack"}, {"id": 434, "parentGenre": 0, "genreLevel": 0, "genreName": "mantra"}, {"id": 435, "parentGenre": 0, "genreLevel": 0, "genreName": "heart sounds, heartbeat"}, {"id": 436, "parentGenre": 0, "genreLevel": 0, "genreName": "renaissance"}, {"id": 437, "parentGenre": 0, "genreLevel": 0, "genreName": "marathi"}, {"id": 438, "parentGenre": 0, "genreLevel": 0, "genreName": "whistle"}, {"id": 439, "parentGenre": 0, "genreLevel": 0, "genreName": "cello"}, {"id": 440, "parentGenre": 0, "genreLevel": 0, "genreName": "pizzicato"}, {"id": 441, "parentGenre": 0, "genreLevel": 0, "genreName": "bowed string instrument"}, {"id": 442, "parentGenre": 0, "genreLevel": 0, "genreName": "tick-tock"}, {"id": 443, "parentGenre": 0, "genreLevel": 0, "genreName": "tick"}, {"id": 444, "parentGenre": 0, "genreLevel": 0, "genreName": "male singing"}, {"id": 446, "parentGenre": 0, "genreLevel": 0, "genreName": "timba"}, {"id": 447, "parentGenre": 0, "genreLevel": 0, "genreName": "contemporary celtic"}, {"id": 448, "parentGenre": 0, "genreLevel": 0, "genreName": "tibetan native music"}, {"id": 449, "parentGenre": 0, "genreLevel": 0, "genreName": "southern rock"}, {"id": 450, "parentGenre": 0, "genreLevel": 0, "genreName": "art song"}, {"id": 451, "parentGenre": 0, "genreLevel": 0, "genreName": "telephone"}, {"id": 452, "parentGenre": 0, "genreLevel": 0, "genreName": "telephone bell ringing"}, {"id": 544, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-swing"}, {"id": 545, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-pop rap"}, {"id": 546, "parentGenre": 0, "genreLevel": 0, "genreName": "funk / soul-contemporary r&b"}, {"id": 547, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-hindustani"}, {"id": 548, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-hardcore"}, {"id": 549, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-ranchera"}, {"id": 550, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-halftime"}, {"id": 551, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-neofolk"}, {"id": 552, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-ragga hiphop"}, {"id": 553, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-disco polo"}, {"id": 554, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-chiptune"}, {"id": 555, "parentGenre": 0, "genreLevel": 0, "genreName": "reggae-dancehall"}, {"id": 556, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-heavy metal"}, {"id": 557, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-ethereal"}, {"id": 558, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-trance"}, {"id": 559, "parentGenre": 0, "genreLevel": 0, "genreName": "classical-contemporary"}, {"id": 560, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-volksmusik"}, {"id": 561, "parentGenre": 0, "genreLevel": 0, "genreName": "non-music-political"}, {"id": 562, "parentGenre": 0, "genreLevel": 0, "genreName": "patter"}, {"id": 563, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-cumbia"}, {"id": 564, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-new age"}, {"id": 565, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-deep house"}, {"id": 566, "parentGenre": 0, "genreLevel": 0, "genreName": "non-music-poetry"}, {"id": 567, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-gangsta"}, {"id": 568, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-freestyle"}, {"id": 569, "parentGenre": 0, "genreLevel": 0, "genreName": "funk / soul"}, {"id": 570, "parentGenre": 0, "genreLevel": 0, "genreName": "whispering"}, {"id": 571, "parentGenre": 0, "genreLevel": 0, "genreName": "scissors"}, {"id": 572, "parentGenre": 0, "genreLevel": 0, "genreName": "water tap, faucet"}, {"id": 573, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-romani"}, {"id": 574, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-indian classical"}, {"id": 575, "parentGenre": 0, "genreLevel": 0, "genreName": "pop-chanson"}, {"id": 576, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-neofolk"}, {"id": 577, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-hardstyle"}, {"id": 578, "parentGenre": 0, "genreLevel": 0, "genreName": "reggae-soca"}, {"id": 579, "parentGenre": 0, "genreLevel": 0, "genreName": "children's-nursery rhymes"}, {"id": 580, "parentGenre": 0, "genreLevel": 0, "genreName": "pop-schlager"}, {"id": 581, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-tribal"}, {"id": 582, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-punk"}, {"id": 583, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-techno"}, {"id": 584, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-dream pop"}, {"id": 585, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-industrial"}, {"id": 586, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-hardcore hip-hop"}, {"id": 587, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-éntekhno"}, {"id": 588, "parentGenre": 0, "genreLevel": 0, "genreName": "classical-neo-romantic"}, {"id": 589, "parentGenre": 0, "genreLevel": 0, "genreName": "reggae-rocksteady"}, {"id": 590, "parentGenre": 0, "genreLevel": 0, "genreName": "bird"}, {"id": 591, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-vallenato"}, {"id": 592, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-hard trance"}, {"id": 593, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-hard house"}, {"id": 594, "parentGenre": 0, "genreLevel": 0, "genreName": "reggae-reggae-pop"}, {"id": 595, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-folk metal"}, {"id": 596, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-screw"}, {"id": 597, "parentGenre": 0, "genreLevel": 0, "genreName": "pop-parody"}, {"id": 598, "parentGenre": 0, "genreLevel": 0, "genreName": "male speech, man speaking"}, {"id": 599, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-drum n bass"}, {"id": 600, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-ghetto"}, {"id": 601, "parentGenre": 0, "genreLevel": 0, "genreName": "cough"}, {"id": 602, "parentGenre": 0, "genreLevel": 0, "genreName": "classical-choral"}, {"id": 603, "parentGenre": 0, "genreLevel": 0, "genreName": "funk / soul-rhythm & blues"}, {"id": 604, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-dungeon synth"}, {"id": 605, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-fado"}, {"id": 606, "parentGenre": 0, "genreLevel": 0, "genreName": "boing"}, {"id": 607, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-doom metal"}, {"id": 608, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-acoustic"}, {"id": 609, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-compas"}, {"id": 610, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-g-funk"}, {"id": 611, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-breakcore"}, {"id": 612, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-salsa"}, {"id": 613, "parentGenre": 0, "genreLevel": 0, "genreName": "blues-country blues"}, {"id": 614, "parentGenre": 0, "genreLevel": 0, "genreName": "non-music-comedy"}, {"id": 615, "parentGenre": 0, "genreLevel": 0, "genreName": "non-music-field recording"}, {"id": 616, "parentGenre": 0, "genreLevel": 0, "genreName": "cricket"}, {"id": 617, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-synthwave"}, {"id": 618, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-easy listening"}, {"id": 619, "parentGenre": 0, "genreLevel": 0, "genreName": "classical-post-modern"}, {"id": 620, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-uk garage"}, {"id": 621, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-yé-yé"}, {"id": 622, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-conscious"}, {"id": 623, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-hard rock"}, {"id": 624, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-dj battle tool"}, {"id": 625, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-beat"}, {"id": 626, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-depressive black metal"}, {"id": 627, "parentGenre": 0, "genreLevel": 0, "genreName": "funk / soul-gospel"}, {"id": 628, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-thug rap"}, {"id": 629, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-trip hop"}, {"id": 630, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-samba"}, {"id": 631, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-eurodance"}, {"id": 632, "parentGenre": 0, "genreLevel": 0, "genreName": "screaming"}, {"id": 633, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-flamenco"}, {"id": 634, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-goregrind"}, {"id": 635, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-batucada"}, {"id": 636, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-celtic"}, {"id": 637, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-dubstep"}, {"id": 638, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-jungle"}, {"id": 639, "parentGenre": 0, "genreLevel": 0, "genreName": "non-music-interview"}, {"id": 640, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-schranz"}, {"id": 641, "parentGenre": 0, "genreLevel": 0, "genreName": "pop-kayōkyoku"}, {"id": 642, "parentGenre": 0, "genreLevel": 0, "genreName": "funk / soul-disco"}, {"id": 643, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-jumpstyle"}, {"id": 644, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-eurobeat"}, {"id": 645, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-contemporary jazz"}, {"id": 646, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-rhythmic noise"}, {"id": 647, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-donk"}, {"id": 648, "parentGenre": 0, "genreLevel": 0, "genreName": "funk / soul-new jack swing"}, {"id": 649, "parentGenre": 0, "genreLevel": 0, "genreName": "pop-indie pop"}, {"id": 650, "parentGenre": 0, "genreLevel": 0, "genreName": "explosion"}, {"id": 651, "parentGenre": 0, "genreLevel": 0, "genreName": "squeak"}, {"id": 652, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-boom bap"}, {"id": 1, "parentGenre": 0, "genreLevel": 1, "genreName": "unknown"}, {"id": 6, "parentGenre": 0, "genreLevel": 0, "genreName": "dance"}, {"id": 7, "parentGenre": 0, "genreLevel": 0, "genreName": "pop"}, {"id": 9, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic"}, {"id": 12, "parentGenre": 0, "genreLevel": 0, "genreName": "house"}, {"id": 13, "parentGenre": 0, "genreLevel": 0, "genreName": "holiday"}, {"id": 14, "parentGenre": 0, "genreLevel": 0, "genreName": "trance"}, {"id": 15, "parentGenre": 0, "genreLevel": 0, "genreName": "hardcore"}, {"id": 16, "parentGenre": 0, "genreLevel": 0, "genreName": "r&b/soul"}, {"id": 17, "parentGenre": 0, "genreLevel": 0, "genreName": "pop in spanish"}, {"id": 18, "parentGenre": 0, "genreLevel": 0, "genreName": "techno"}, {"id": 19, "parentGenre": 0, "genreLevel": 0, "genreName": "música mexicana"}, {"id": 20, "parentGenre": 0, "genreLevel": 0, "genreName": "country"}, {"id": 21, "parentGenre": 0, "genreLevel": 0, "genreName": "rock"}, {"id": 22, "parentGenre": 0, "genreLevel": 0, "genreName": "classical"}, {"id": 23, "parentGenre": 0, "genreLevel": 0, "genreName": "hip-hop/rap"}, {"id": 24, "parentGenre": 0, "genreLevel": 0, "genreName": "urbano latino"}, {"id": 25, "parentGenre": 0, "genreLevel": 0, "genreName": "alternative"}, {"id": 26, "parentGenre": 0, "genreLevel": 0, "genreName": "electronica"}, {"id": 27, "parentGenre": 0, "genreLevel": 0, "genreName": "reggae"}, {"id": 28, "parentGenre": 0, "genreLevel": 0, "genreName": "world"}, {"id": 29, "parentGenre": 0, "genreLevel": 0, "genreName": "brazilian"}, {"id": 30, "parentGenre": 0, "genreLevel": 0, "genreName": "singer/songwriter"}, {"id": 31, "parentGenre": 0, "genreLevel": 0, "genreName": "afro-pop"}, {"id": 32, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz"}, {"id": 33, "parentGenre": 0, "genreLevel": 0, "genreName": "latino"}, {"id": 34, "parentGenre": 0, "genreLevel": 0, "genreName": "regional indian"}, {"id": 35, "parentGenre": 0, "genreLevel": 0, "genreName": "hard rock"}, {"id": 36, "parentGenre": 0, "genreLevel": 0, "genreName": "ambient"}, {"id": 37, "parentGenre": 0, "genreLevel": 0, "genreName": "afro-beat"}, {"id": 38, "parentGenre": 0, "genreLevel": 0, "genreName": "instrumental"}, {"id": 164, "parentGenre": 0, "genreLevel": 0, "genreName": "lovers rock"}, {"id": 39, "parentGenre": 0, "genreLevel": 0, "genreName": "fitness & workout"}, {"id": 40, "parentGenre": 0, "genreLevel": 0, "genreName": "alternative rap"}, {"id": 41, "parentGenre": 0, "genreLevel": 0, "genreName": "new age"}, {"id": 42, "parentGenre": 0, "genreLevel": 0, "genreName": "downtempo"}, {"id": 43, "parentGenre": 0, "genreLevel": 0, "genreName": "vocal"}, {"id": 44, "parentGenre": 0, "genreLevel": 0, "genreName": "disco"}, {"id": 45, "parentGenre": 0, "genreLevel": 0, "genreName": "arabic"}, {"id": 46, "parentGenre": 0, "genreLevel": 0, "genreName": "christian & gospel"}, {"id": 47, "parentGenre": 0, "genreLevel": 0, "genreName": "original score"}, {"id": 48, "parentGenre": 0, "genreLevel": 0, "genreName": "hip-hop"}, {"id": 49, "parentGenre": 0, "genreLevel": 0, "genreName": "soundtrack"}, {"id": 50, "parentGenre": 0, "genreLevel": 0, "genreName": "funk"}, {"id": 51, "parentGenre": 0, "genreLevel": 0, "genreName": "indian pop"}, {"id": 52, "parentGenre": 0, "genreLevel": 0, "genreName": "african"}, {"id": 53, "parentGenre": 0, "genreLevel": 0, "genreName": "malaysian pop"}, {"id": 54, "parentGenre": 0, "genreLevel": 0, "genreName": "alternative folk"}, {"id": 55, "parentGenre": 0, "genreLevel": 0, "genreName": "latin rap"}, {"id": 56, "parentGenre": 0, "genreLevel": 0, "genreName": "música tropical"}, {"id": 57, "parentGenre": 0, "genreLevel": 0, "genreName": "easy listening"}, {"id": 58, "parentGenre": 0, "genreLevel": 0, "genreName": "folk"}, {"id": 59, "parentGenre": 0, "genreLevel": 0, "genreName": "indian folk"}, {"id": 60, "parentGenre": 0, "genreLevel": 0, "genreName": "jungle/drum'n'bass"}, {"id": 61, "parentGenre": 0, "genreLevel": 0, "genreName": "indie pop"}, {"id": 62, "parentGenre": 0, "genreLevel": 0, "genreName": "baile funk"}, {"id": 63, "parentGenre": 0, "genreLevel": 0, "genreName": "indie rock"}, {"id": 64, "parentGenre": 0, "genreLevel": 0, "genreName": "bollywood"}, {"id": 65, "parentGenre": 0, "genreLevel": 0, "genreName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 66, "parentGenre": 0, "genreLevel": 0, "genreName": "asia"}, {"id": 67, "parentGenre": 0, "genreLevel": 0, "genreName": "tamil"}, {"id": 68, "parentGenre": 0, "genreLevel": 0, "genreName": "children's music"}, {"id": 69, "parentGenre": 0, "genreLevel": 0, "genreName": "breakbeat"}, {"id": 70, "parentGenre": 0, "genreLevel": 0, "genreName": "blues"}, {"id": 71, "parentGenre": 0, "genreLevel": 0, "genreName": "turkish pop"}, {"id": 72, "parentGenre": 0, "genreLevel": 0, "genreName": "mandopop"}, {"id": 73, "parentGenre": 0, "genreLevel": 0, "genreName": "french pop"}, {"id": 74, "parentGenre": 0, "genreLevel": 0, "genreName": "rap"}, {"id": 75, "parentGenre": 0, "genreLevel": 0, "genreName": "lounge"}, {"id": 76, "parentGenre": 0, "genreLevel": 0, "genreName": "turkish"}, {"id": 77, "parentGenre": 0, "genreLevel": 0, "genreName": "gospel"}, {"id": 78, "parentGenre": 0, "genreLevel": 0, "genreName": "france"}, {"id": 79, "parentGenre": 0, "genreLevel": 0, "genreName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 80, "parentGenre": 0, "genreLevel": 0, "genreName": "<PERSON><PERSON><PERSON>"}, {"id": 81, "parentGenre": 0, "genreLevel": 0, "genreName": "comedy"}, {"id": 82, "parentGenre": 0, "genreLevel": 0, "genreName": "russian chanson"}, {"id": 83, "parentGenre": 0, "genreLevel": 0, "genreName": "rock y alternativo"}, {"id": 84, "parentGenre": 0, "genreLevel": 0, "genreName": "relaxation"}, {"id": 85, "parentGenre": 0, "genreLevel": 0, "genreName": "modern dancehall"}, {"id": 86, "parentGenre": 0, "genreLevel": 0, "genreName": "islamic"}, {"id": 87, "parentGenre": 0, "genreLevel": 0, "genreName": "karaoke"}, {"id": 88, "parentGenre": 0, "genreLevel": 0, "genreName": "mpb"}, {"id": 89, "parentGenre": 0, "genreLevel": 0, "genreName": "metal"}, {"id": 90, "parentGenre": 0, "genreLevel": 0, "genreName": "idm/experimental"}, {"id": 91, "parentGenre": 0, "genreLevel": 0, "genreName": "j-pop"}, {"id": 92, "parentGenre": 0, "genreLevel": 0, "genreName": "indian"}, {"id": 93, "parentGenre": 0, "genreLevel": 0, "genreName": "christmas: modern"}, {"id": 94, "parentGenre": 0, "genreLevel": 0, "genreName": "k-pop"}, {"id": 95, "parentGenre": 0, "genreLevel": 0, "genreName": "musicals"}, {"id": 96, "parentGenre": 0, "genreLevel": 0, "genreName": "farsi"}, {"id": 97, "parentGenre": 0, "genreLevel": 0, "genreName": "dub"}, {"id": 98, "parentGenre": 0, "genreLevel": 0, "genreName": "europe"}, {"id": 99, "parentGenre": 0, "genreLevel": 0, "genreName": "traditional folk"}, {"id": 100, "parentGenre": 0, "genreLevel": 0, "genreName": "dubstep"}, {"id": 101, "parentGenre": 0, "genreLevel": 0, "genreName": "arabic pop"}, {"id": 102, "parentGenre": 0, "genreLevel": 0, "genreName": "north america"}, {"id": 103, "parentGenre": 0, "genreLevel": 0, "genreName": "turkish hip-hop/rap"}, {"id": 104, "parentGenre": 0, "genreLevel": 0, "genreName": "folk-rock"}, {"id": 105, "parentGenre": 0, "genreLevel": 0, "genreName": "christmas: r&b"}, {"id": 106, "parentGenre": 0, "genreLevel": 0, "genreName": "mbalax"}, {"id": 107, "parentGenre": 0, "genreLevel": 0, "genreName": "rock & roll"}, {"id": 108, "parentGenre": 0, "genreLevel": 0, "genreName": "big band"}, {"id": 109, "parentGenre": 0, "genreLevel": 0, "genreName": "pop punk"}, {"id": 110, "parentGenre": 0, "genreLevel": 0, "genreName": "halk"}, {"id": 111, "parentGenre": 0, "genreLevel": 0, "genreName": "americana"}, {"id": 112, "parentGenre": 0, "genreLevel": 0, "genreName": "west coast rap"}, {"id": 113, "parentGenre": 0, "genreLevel": 0, "genreName": "indonesian religious"}, {"id": 114, "parentGenre": 0, "genreLevel": 0, "genreName": "pop/rock"}, {"id": 115, "parentGenre": 0, "genreLevel": 0, "genreName": "arabesque"}, {"id": 116, "parentGenre": 0, "genreLevel": 0, "genreName": "pagode"}, {"id": 117, "parentGenre": 0, "genreLevel": 0, "genreName": "orchestral"}, {"id": 118, "parentGenre": 0, "genreLevel": 0, "genreName": "indo pop"}, {"id": 119, "parentGenre": 0, "genreLevel": 0, "genreName": "cantopop/hk-pop"}, {"id": 120, "parentGenre": 0, "genreLevel": 0, "genreName": "gujarati"}, {"id": 121, "parentGenre": 0, "genreLevel": 0, "genreName": "christmas"}, {"id": 122, "parentGenre": 0, "genreLevel": 0, "genreName": "adult alternative"}, {"id": 123, "parentGenre": 0, "genreLevel": 0, "genreName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 124, "parentGenre": 0, "genreLevel": 0, "genreName": "christian pop"}, {"id": 125, "parentGenre": 0, "genreLevel": 0, "genreName": "devotional & spiritual"}, {"id": 126, "parentGenre": 0, "genreLevel": 0, "genreName": "christmas: pop"}, {"id": 127, "parentGenre": 0, "genreLevel": 0, "genreName": "praise & worship"}, {"id": 128, "parentGenre": 0, "genreLevel": 0, "genreName": "blues-rock"}, {"id": 129, "parentGenre": 0, "genreLevel": 0, "genreName": "uk hip-hop"}, {"id": 130, "parentGenre": 0, "genreLevel": 0, "genreName": "punjabi pop"}, {"id": 131, "parentGenre": 0, "genreLevel": 0, "genreName": "samba"}, {"id": 132, "parentGenre": 0, "genreLevel": 0, "genreName": "tv soundtrack"}, {"id": 133, "parentGenre": 0, "genreLevel": 0, "genreName": "axé"}, {"id": 134, "parentGenre": 0, "genreLevel": 0, "genreName": "punk"}, {"id": 135, "parentGenre": 0, "genreLevel": 0, "genreName": "malayalam"}, {"id": 136, "parentGenre": 0, "genreLevel": 0, "genreName": "meditation"}, {"id": 137, "parentGenre": 0, "genreLevel": 0, "genreName": "sanat"}, {"id": 138, "parentGenre": 0, "genreLevel": 0, "genreName": "dangdut"}, {"id": 139, "parentGenre": 0, "genreLevel": 0, "genreName": "telugu"}, {"id": 140, "parentGenre": 0, "genreLevel": 0, "genreName": "contemporary gospel"}, {"id": 141, "parentGenre": 0, "genreLevel": 0, "genreName": "anime"}, {"id": 142, "parentGenre": 0, "genreLevel": 0, "genreName": "teen pop"}, {"id": 143, "parentGenre": 0, "genreLevel": 0, "genreName": "indian classical"}, {"id": 144, "parentGenre": 0, "genreLevel": 0, "genreName": "swing"}, {"id": 145, "parentGenre": 0, "genreLevel": 0, "genreName": "german pop"}, {"id": 146, "parentGenre": 0, "genreLevel": 0, "genreName": "soul"}, {"id": 147, "parentGenre": 0, "genreLevel": 0, "genreName": "roots reggae"}, {"id": 148, "parentGenre": 0, "genreLevel": 0, "genreName": "underground rap"}, {"id": 149, "parentGenre": 0, "genreLevel": 0, "genreName": "highlife"}, {"id": 150, "parentGenre": 0, "genreLevel": 0, "genreName": "north african"}, {"id": 151, "parentGenre": 0, "genreLevel": 0, "genreName": "ccm"}, {"id": 152, "parentGenre": 0, "genreLevel": 0, "genreName": "music"}, {"id": 153, "parentGenre": 0, "genreLevel": 0, "genreName": "grunge"}, {"id": 154, "parentGenre": 0, "genreLevel": 0, "genreName": "turkish alternative"}, {"id": 155, "parentGenre": 0, "genreLevel": 0, "genreName": "electro-cha'abi"}, {"id": 156, "parentGenre": 0, "genreLevel": 0, "genreName": "russian pop"}, {"id": 157, "parentGenre": 0, "genreLevel": 0, "genreName": "solo instrumental"}, {"id": 158, "parentGenre": 0, "genreLevel": 0, "genreName": "christmas: classic"}, {"id": 159, "parentGenre": 0, "genreLevel": 0, "genreName": "urban cowboy"}, {"id": 160, "parentGenre": 0, "genreLevel": 0, "genreName": "thai country"}, {"id": 161, "parentGenre": 0, "genreLevel": 0, "genreName": "worldbeat"}, {"id": 162, "parentGenre": 0, "genreLevel": 0, "genreName": "ghazals"}, {"id": 163, "parentGenre": 0, "genreLevel": 0, "genreName": "contemporary latin"}, {"id": 165, "parentGenre": 0, "genreLevel": 0, "genreName": "b<PERSON><PERSON><PERSON><PERSON>"}, {"id": 166, "parentGenre": 0, "genreLevel": 0, "genreName": "contemporary folk"}, {"id": 167, "parentGenre": 0, "genreLevel": 0, "genreName": "baladas y boleros"}, {"id": 168, "parentGenre": 0, "genreLevel": 0, "genreName": "özgün"}, {"id": 169, "parentGenre": 0, "genreLevel": 0, "genreName": "thai pop"}, {"id": 170, "parentGenre": 0, "genreLevel": 0, "genreName": "oldies"}, {"id": 171, "parentGenre": 0, "genreLevel": 0, "genreName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 172, "parentGenre": 0, "genreLevel": 0, "genreName": "<PERSON><PERSON><PERSON>"}, {"id": 173, "parentGenre": 0, "genreLevel": 0, "genreName": "prog-rock/art rock"}, {"id": 174, "parentGenre": 0, "genreLevel": 0, "genreName": "classical crossover"}, {"id": 175, "parentGenre": 0, "genreLevel": 0, "genreName": "vocal pop"}, {"id": 176, "parentGenre": 0, "genreLevel": 0, "genreName": "traditional gospel"}, {"id": 177, "parentGenre": 0, "genreLevel": 0, "genreName": "korean hip-hop"}, {"id": 178, "parentGenre": 0, "genreLevel": 0, "genreName": "turkish rock"}, {"id": 179, "parentGenre": 0, "genreLevel": 0, "genreName": "chinese"}, {"id": 180, "parentGenre": 0, "genreLevel": 0, "genreName": "bass"}, {"id": 181, "parentGenre": 0, "genreLevel": 0, "genreName": "traditional pop"}, {"id": 182, "parentGenre": 0, "genreLevel": 0, "genreName": "sufi"}, {"id": 183, "parentGenre": 0, "genreLevel": 0, "genreName": "industrial"}, {"id": 184, "parentGenre": 0, "genreLevel": 0, "genreName": "contemporary country"}, {"id": 185, "parentGenre": 0, "genreLevel": 0, "genreName": "disney"}, {"id": 186, "parentGenre": 0, "genreLevel": 0, "genreName": "russian"}, {"id": 187, "parentGenre": 0, "genreLevel": 0, "genreName": "flamenco"}, {"id": 188, "parentGenre": 0, "genreLevel": 0, "genreName": "adult contemporary"}, {"id": 189, "parentGenre": 0, "genreLevel": 0, "genreName": "neo-soul"}, {"id": 190, "parentGenre": 0, "genreLevel": 0, "genreName": "nature"}, {"id": 191, "parentGenre": 0, "genreLevel": 0, "genreName": "gangsta rap"}, {"id": 192, "parentGenre": 0, "genreLevel": 0, "genreName": "travel"}, {"id": 193, "parentGenre": 0, "genreLevel": 0, "genreName": "sing-along"}, {"id": 194, "parentGenre": 0, "genreLevel": 0, "genreName": "gua<PERSON><PERSON>"}, {"id": 195, "parentGenre": 0, "genreLevel": 0, "genreName": "original pilipino music"}, {"id": 196, "parentGenre": 0, "genreLevel": 0, "genreName": "old school rap"}, {"id": 197, "parentGenre": 0, "genreLevel": 0, "genreName": "contemporary r&b"}, {"id": 198, "parentGenre": 0, "genreLevel": 0, "genreName": "fusion"}, {"id": 199, "parentGenre": 0, "genreLevel": 0, "genreName": "emo"}, {"id": 200, "parentGenre": 0, "genreLevel": 0, "genreName": "chinese hip-hop"}, {"id": 201, "parentGenre": 0, "genreLevel": 0, "genreName": "psychedelic"}, {"id": 202, "parentGenre": 0, "genreLevel": 0, "genreName": "kayokyoku"}, {"id": 203, "parentGenre": 0, "genreLevel": 0, "genreName": "romantic era"}, {"id": 204, "parentGenre": 0, "genreLevel": 0, "genreName": "hardcore rap"}, {"id": 205, "parentGenre": 0, "genreLevel": 0, "genreName": "south america"}, {"id": 206, "parentGenre": 0, "genreLevel": 0, "genreName": "marching"}, {"id": 207, "parentGenre": 0, "genreLevel": 0, "genreName": "halloween"}, {"id": 208, "parentGenre": 0, "genreLevel": 0, "genreName": "standards"}, {"id": 209, "parentGenre": 0, "genreLevel": 0, "genreName": "chant"}, {"id": 210, "parentGenre": 0, "genreLevel": 0, "genreName": "college rock"}, {"id": 211, "parentGenre": 0, "genreLevel": 0, "genreName": "german folk"}, {"id": 212, "parentGenre": 0, "genreLevel": 0, "genreName": "korean indie"}, {"id": 213, "parentGenre": 0, "genreLevel": 0, "genreName": "chinese regional folk"}, {"id": 214, "parentGenre": 0, "genreLevel": 0, "genreName": "bossa nova"}, {"id": 215, "parentGenre": 0, "genreLevel": 0, "genreName": "christian rap"}, {"id": 216, "parentGenre": 0, "genreLevel": 0, "genreName": "video game"}, {"id": 217, "parentGenre": 0, "genreLevel": 0, "genreName": "new wave"}, {"id": 218, "parentGenre": 0, "genreLevel": 0, "genreName": "britpop"}, {"id": 219, "parentGenre": 0, "genreLevel": 0, "genreName": "caribbean"}, {"id": 220, "parentGenre": 0, "genreLevel": 0, "genreName": "smooth jazz"}, {"id": 221, "parentGenre": 0, "genreLevel": 0, "genreName": "lullabies"}, {"id": 222, "parentGenre": 0, "genreLevel": 0, "genreName": "israeli"}, {"id": 223, "parentGenre": 0, "genreLevel": 0, "genreName": "enka"}, {"id": 224, "parentGenre": 0, "genreLevel": 0, "genreName": "acoustic blues"}, {"id": 225, "parentGenre": 0, "genreLevel": 0, "genreName": "ndombolo"}, {"id": 226, "parentGenre": 0, "genreLevel": 0, "genreName": "tribute"}, {"id": 227, "parentGenre": 0, "genreLevel": 0, "genreName": "cuban"}, {"id": 228, "parentGenre": 0, "genreLevel": 0, "genreName": "contemporary era"}, {"id": 229, "parentGenre": 0, "genreLevel": 0, "genreName": "classic blues"}, {"id": 230, "parentGenre": 0, "genreLevel": 0, "genreName": "bluegrass"}, {"id": 231, "parentGenre": 0, "genreLevel": 0, "genreName": "dini"}, {"id": 232, "parentGenre": 0, "genreLevel": 0, "genreName": "contemporary singer/songwriter"}, {"id": 233, "parentGenre": 0, "genreLevel": 0, "genreName": "christian metal"}, {"id": 234, "parentGenre": 0, "genreLevel": 0, "genreName": "christian rock"}, {"id": 235, "parentGenre": 0, "genreLevel": 0, "genreName": "dirty south"}, {"id": 236, "parentGenre": 0, "genreLevel": 0, "genreName": "piano"}, {"id": 237, "parentGenre": 0, "genreLevel": 0, "genreName": "traditional country"}, {"id": 238, "parentGenre": 0, "genreLevel": 0, "genreName": "baroque era"}, {"id": 239, "parentGenre": 0, "genreLevel": 0, "genreName": "modern era"}, {"id": 240, "parentGenre": 0, "genreLevel": 0, "genreName": "country blues"}, {"id": 241, "parentGenre": 0, "genreLevel": 0, "genreName": "bolero"}, {"id": 242, "parentGenre": 0, "genreLevel": 0, "genreName": "australia"}, {"id": 243, "parentGenre": 0, "genreLevel": 0, "genreName": "klezmer"}, {"id": 244, "parentGenre": 0, "genreLevel": 0, "genreName": "crossover jazz"}, {"id": 245, "parentGenre": 0, "genreLevel": 0, "genreName": "standup comedy"}, {"id": 246, "parentGenre": 0, "genreLevel": 0, "genreName": "vocal jazz"}, {"id": 247, "parentGenre": 0, "genreLevel": 0, "genreName": "<PERSON><PERSON><PERSON>"}, {"id": 248, "parentGenre": 0, "genreLevel": 0, "genreName": "assamese"}, {"id": 249, "parentGenre": 0, "genreLevel": 0, "genreName": "honky tonk"}, {"id": 250, "parentGenre": 0, "genreLevel": 0, "genreName": "ragtime"}, {"id": 251, "parentGenre": 0, "genreLevel": 0, "genreName": "foreign cinema"}, {"id": 252, "parentGenre": 0, "genreLevel": 0, "genreName": "cajun"}, {"id": 253, "parentGenre": 0, "genreLevel": 0, "genreName": "traditional celtic"}, {"id": 254, "parentGenre": 0, "genreLevel": 0, "genreName": "garage"}, {"id": 285, "parentGenre": 0, "genreLevel": 0, "genreName": "walk, footsteps"}, {"id": 286, "parentGenre": 0, "genreLevel": 0, "genreName": "zipper (clothing)"}, {"id": 287, "parentGenre": 0, "genreLevel": 0, "genreName": "inside, small room"}, {"id": 453, "parentGenre": 0, "genreLevel": 0, "genreName": "guitar"}, {"id": 454, "parentGenre": 0, "genreLevel": 0, "genreName": "applause"}, {"id": 455, "parentGenre": 0, "genreLevel": 0, "genreName": "opera"}, {"id": 456, "parentGenre": 0, "genreLevel": 0, "genreName": "water"}, {"id": 457, "parentGenre": 0, "genreLevel": 0, "genreName": "bathtub (filling or washing)"}, {"id": 458, "parentGenre": 0, "genreLevel": 0, "genreName": "snoring"}, {"id": 459, "parentGenre": 0, "genreLevel": 0, "genreName": "domestic animals, pets"}, {"id": 460, "parentGenre": 0, "genreLevel": 0, "genreName": "animal"}, {"id": 461, "parentGenre": 0, "genreLevel": 0, "genreName": "saxophone"}, {"id": 462, "parentGenre": 0, "genreLevel": 0, "genreName": "drum kit"}, {"id": 463, "parentGenre": 0, "genreLevel": 0, "genreName": "korean rock"}, {"id": 464, "parentGenre": 0, "genreLevel": 0, "genreName": "russian bard"}, {"id": 465, "parentGenre": 0, "genreLevel": 0, "genreName": "christmas: classical"}, {"id": 466, "parentGenre": 0, "genreLevel": 0, "genreName": "engine starting"}, {"id": 467, "parentGenre": 0, "genreLevel": 0, "genreName": "engine"}, {"id": 468, "parentGenre": 0, "genreLevel": 0, "genreName": "honk"}, {"id": 469, "parentGenre": 0, "genreLevel": 0, "genreName": "fowl"}, {"id": 470, "parentGenre": 0, "genreLevel": 0, "genreName": "mains hum"}, {"id": 471, "parentGenre": 0, "genreLevel": 0, "genreName": "thanksgiving"}, {"id": 472, "parentGenre": 0, "genreLevel": 1, "genreName": "unknown genre"}, {"id": 477, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-african"}, {"id": 478, "parentGenre": 0, "genreLevel": 0, "genreName": "non-music-audiobook"}, {"id": 479, "parentGenre": 0, "genreLevel": 0, "genreName": "pop-ballad"}, {"id": 480, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-synth-pop"}, {"id": 481, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-bounce"}, {"id": 482, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-laïkó"}, {"id": 483, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-psychedelic rock"}, {"id": 484, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-experimental"}, {"id": 485, "parentGenre": 0, "genreLevel": 0, "genreName": "non-music-religious"}, {"id": 486, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-horrorcore"}, {"id": 487, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-indie rock"}, {"id": 488, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-trap"}, {"id": 489, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-vaporwave"}, {"id": 490, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-raï"}, {"id": 491, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-nordic"}, {"id": 492, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-séga"}, {"id": 493, "parentGenre": 0, "genreLevel": 0, "genreName": "pop-k-pop"}, {"id": 494, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-big beat"}, {"id": 495, "parentGenre": 0, "genreLevel": 0, "genreName": "funk / soul-swingbeat"}, {"id": 496, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-reggaeton"}, {"id": 497, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-canzone napoletana"}, {"id": 498, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-folk rock"}, {"id": 499, "parentGenre": 0, "genreLevel": 0, "genreName": "children's-educational"}, {"id": 500, "parentGenre": 0, "genreLevel": 0, "genreName": "pop-j-pop"}, {"id": 501, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-dance-pop"}, {"id": 502, "parentGenre": 0, "genreLevel": 0, "genreName": "pop-bollywood"}, {"id": 503, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-hip hop"}, {"id": 504, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-instrumental"}, {"id": 505, "parentGenre": 0, "genreLevel": 0, "genreName": "classical-baroque"}, {"id": 506, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-tropical house"}, {"id": 507, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-euro house"}, {"id": 508, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-grime"}, {"id": 509, "parentGenre": 0, "genreLevel": 0, "genreName": "stage & screen-soundtrack"}, {"id": 510, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-zouk"}, {"id": 511, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-electro house"}, {"id": 512, "parentGenre": 0, "genreLevel": 0, "genreName": "crunch"}, {"id": 513, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-house"}, {"id": 514, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-symphonic rock"}, {"id": 515, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-italodance"}, {"id": 516, "parentGenre": 0, "genreLevel": 0, "genreName": "funk / soul-neo soul"}, {"id": 517, "parentGenre": 0, "genreLevel": 0, "genreName": "children's-story"}, {"id": 518, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-ambient"}, {"id": 519, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country"}, {"id": 520, "parentGenre": 0, "genreLevel": 0, "genreName": "non-music-spoken word"}, {"id": 521, "parentGenre": 0, "genreLevel": 0, "genreName": "non-music-radioplay"}, {"id": 522, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-electro"}, {"id": 523, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-norteño"}, {"id": 524, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-rnb/swing"}, {"id": 525, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-downtempo"}, {"id": 526, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-ghetto house"}, {"id": 527, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-euro-disco"}, {"id": 528, "parentGenre": 0, "genreLevel": 0, "genreName": "pop-vocal"}, {"id": 529, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-alternative rock"}, {"id": 530, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-brit pop"}, {"id": 531, "parentGenre": 0, "genreLevel": 0, "genreName": "reggae-ragga"}, {"id": 532, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-bolero"}, {"id": 533, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-happy hardcore"}, {"id": 534, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-makina"}, {"id": 535, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-juke"}, {"id": 536, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-grime"}, {"id": 537, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-mpb"}, {"id": 538, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-pop rock"}, {"id": 539, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-hands up"}, {"id": 540, "parentGenre": 0, "genreLevel": 0, "genreName": "pop-europop"}, {"id": 541, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-cloud rap"}, {"id": 542, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-soukous"}, {"id": 543, "parentGenre": 0, "genreLevel": 0, "genreName": "hands"}, {"id": 653, "parentGenre": 0, "genreLevel": 0, "genreName": "child speech, kid speaking"}, {"id": 654, "parentGenre": 0, "genreLevel": 0, "genreName": "reggae-lovers rock"}, {"id": 655, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-forró"}, {"id": 656, "parentGenre": 0, "genreLevel": 0, "genreName": "slap, smack"}, {"id": 657, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-italo-disco"}, {"id": 658, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-chillwave"}, {"id": 659, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-beatdown"}, {"id": 660, "parentGenre": 0, "genreLevel": 0, "genreName": "funk / soul-afrobeat"}, {"id": 661, "parentGenre": 0, "genreLevel": 0, "genreName": "blues-modern electric blues"}, {"id": 662, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-speedcore"}, {"id": 663, "parentGenre": 0, "genreLevel": 0, "genreName": "classical-modern"}, {"id": 664, "parentGenre": 0, "genreLevel": 0, "genreName": "reggae-calypso"}, {"id": 665, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-afro-cuban jazz"}, {"id": 666, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-acid jazz"}, {"id": 667, "parentGenre": 0, "genreLevel": 0, "genreName": "shofar"}, {"id": 668, "parentGenre": 0, "genreLevel": 0, "genreName": "blues-delta blues"}, {"id": 669, "parentGenre": 0, "genreLevel": 0, "genreName": "snicker"}, {"id": 670, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-baião"}, {"id": 671, "parentGenre": 0, "genreLevel": 0, "genreName": "pop-bubblegum"}, {"id": 672, "parentGenre": 0, "genreLevel": 0, "genreName": "child singing"}, {"id": 673, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-goth rock"}, {"id": 674, "parentGenre": 0, "genreLevel": 0, "genreName": "dog"}, {"id": 675, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-pop punk"}, {"id": 676, "parentGenre": 0, "genreLevel": 0, "genreName": "stage & screen-score"}, {"id": 677, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-jazzy hip-hop"}, {"id": 678, "parentGenre": 0, "genreLevel": 0, "genreName": "sine wave"}, {"id": 679, "parentGenre": 0, "genreLevel": 0, "genreName": "door"}, {"id": 680, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-pub rock"}, {"id": 681, "parentGenre": 0, "genreLevel": 0, "genreName": "a capella"}, {"id": 682, "parentGenre": 0, "genreLevel": 0, "genreName": "stage & screen-musical"}, {"id": 683, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-latin"}, {"id": 684, "parentGenre": 0, "genreLevel": 0, "genreName": "sizzle"}, {"id": 685, "parentGenre": 0, "genreLevel": 0, "genreName": "sigh"}, {"id": 686, "parentGenre": 0, "genreLevel": 0, "genreName": "turkey"}, {"id": 687, "parentGenre": 0, "genreLevel": 0, "genreName": "battle cry"}, {"id": 688, "parentGenre": 0, "genreLevel": 0, "genreName": "crumpling, crinkling"}, {"id": 689, "parentGenre": 0, "genreLevel": 0, "genreName": "reggae-dub"}, {"id": 690, "parentGenre": 0, "genreLevel": 0, "genreName": "liquid"}, {"id": 691, "parentGenre": 0, "genreLevel": 0, "genreName": "machine gun"}, {"id": 692, "parentGenre": 0, "genreLevel": 0, "genreName": "chainsaw"}, {"id": 693, "parentGenre": 0, "genreLevel": 0, "genreName": "firecracker"}, {"id": 694, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-mariachi"}, {"id": 695, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-merengue"}, {"id": 696, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-bassline"}, {"id": 697, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-rock & roll"}, {"id": 698, "parentGenre": 0, "genreLevel": 0, "genreName": "non-music-dialogue"}, {"id": 699, "parentGenre": 0, "genreLevel": 0, "genreName": "laughter"}, {"id": 700, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-speed garage"}, {"id": 701, "parentGenre": 0, "genreLevel": 0, "genreName": "classical-neo-classical"}, {"id": 702, "parentGenre": 0, "genreLevel": 0, "genreName": "ocean"}, {"id": 703, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-nueva cancion"}, {"id": 704, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-dark ambient"}, {"id": 705, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-smooth jazz"}, {"id": 706, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-surf"}, {"id": 707, "parentGenre": 0, "genreLevel": 0, "genreName": "brass & military-marches"}, {"id": 708, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-highlife"}, {"id": 709, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-post-punk"}, {"id": 710, "parentGenre": 0, "genreLevel": 0, "genreName": "classical-medieval"}, {"id": 711, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-psy-trance"}, {"id": 712, "parentGenre": 0, "genreLevel": 0, "genreName": "whip"}, {"id": 713, "parentGenre": 0, "genreLevel": 0, "genreName": "squish"}, {"id": 714, "parentGenre": 0, "genreLevel": 0, "genreName": "oink"}, {"id": 715, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-darkwave"}, {"id": 716, "parentGenre": 0, "genreLevel": 0, "genreName": "fart"}, {"id": 717, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-boss<PERSON><PERSON>"}, {"id": 718, "parentGenre": 0, "genreLevel": 0, "genreName": "blues-piano blues"}, {"id": 719, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-progressive breaks"}, {"id": 720, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-electroclash"}, {"id": 721, "parentGenre": 0, "genreLevel": 0, "genreName": "funk / soul-uk street soul"}, {"id": 722, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-lo-fi"}, {"id": 723, "parentGenre": 0, "genreLevel": 0, "genreName": "dishes, pots, and pans"}, {"id": 724, "parentGenre": 0, "genreLevel": 0, "genreName": "classical-romantic"}, {"id": 725, "parentGenre": 0, "genreLevel": 0, "genreName": "tools"}, {"id": 726, "parentGenre": 0, "genreLevel": 0, "genreName": "pop-city pop"}, {"id": 727, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-pachanga"}, {"id": 728, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-black metal"}, {"id": 729, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-britcore"}, {"id": 730, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-noisecore"}, {"id": 731, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-pacific"}, {"id": 732, "parentGenre": 0, "genreLevel": 0, "genreName": "insect"}, {"id": 733, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-turntablism"}, {"id": 734, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-pornogrind"}, {"id": 735, "parentGenre": 0, "genreLevel": 0, "genreName": "female singing"}, {"id": 736, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-atmospheric black metal"}, {"id": 737, "parentGenre": 0, "genreLevel": 0, "genreName": "neigh, whinny"}, {"id": 738, "parentGenre": 0, "genreLevel": 0, "genreName": "rub"}, {"id": 739, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-no wave"}, {"id": 740, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-disco"}, {"id": 741, "parentGenre": 0, "genreLevel": 0, "genreName": "finger snapping"}, {"id": 742, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-crunk"}, {"id": 743, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-bossa nova"}, {"id": 744, "parentGenre": 0, "genreLevel": 0, "genreName": "pop-music hall"}, {"id": 745, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-garage rock"}, {"id": 746, "parentGenre": 0, "genreLevel": 0, "genreName": "reggae-roots reggae"}, {"id": 747, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-deathrock"}, {"id": 748, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-trip hop"}, {"id": 749, "parentGenre": 0, "genreLevel": 0, "genreName": "cat"}, {"id": 750, "parentGenre": 0, "genreLevel": 0, "genreName": "typing"}, {"id": 751, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-abstract"}, {"id": 752, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-bass music"}, {"id": 753, "parentGenre": 0, "genreLevel": 0, "genreName": "chicken, rooster"}, {"id": 754, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-hi nrg"}, {"id": 755, "parentGenre": 0, "genreLevel": 0, "genreName": "breaking"}, {"id": 756, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-mod"}, {"id": 757, "parentGenre": 0, "genreLevel": 0, "genreName": "crushing"}, {"id": 758, "parentGenre": 0, "genreLevel": 0, "genreName": "non-music-education"}, {"id": 759, "parentGenre": 0, "genreLevel": 0, "genreName": "writing"}, {"id": 760, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-progressive house"}, {"id": 761, "parentGenre": 0, "genreLevel": 0, "genreName": "funk / soul-free funk"}, {"id": 762, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-new wave"}, {"id": 763, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-nu-disco"}, {"id": 764, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-tech trance"}, {"id": 765, "parentGenre": 0, "genreLevel": 0, "genreName": "brass & military"}, {"id": 766, "parentGenre": 0, "genreLevel": 0, "genreName": "roar"}, {"id": 767, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-breaks"}, {"id": 768, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-gypsy jazz"}, {"id": 769, "parentGenre": 0, "genreLevel": 0, "genreName": "pop-novelty"}, {"id": 770, "parentGenre": 0, "genreLevel": 0, "genreName": "sneeze"}, {"id": 771, "parentGenre": 0, "genreLevel": 0, "genreName": "baby laughter"}, {"id": 772, "parentGenre": 0, "genreLevel": 0, "genreName": "chatter"}, {"id": 773, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-glitch"}, {"id": 774, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-hard techno"}, {"id": 775, "parentGenre": 0, "genreLevel": 0, "genreName": "blues-texas blues"}, {"id": 776, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-afrobeat"}, {"id": 777, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-porro"}, {"id": 778, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-blues rock"}, {"id": 779, "parentGenre": 0, "genreLevel": 0, "genreName": "clicking"}, {"id": 780, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-miami bass"}, {"id": 781, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-country rock"}, {"id": 782, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-dub"}, {"id": 783, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-tango"}, {"id": 784, "parentGenre": 0, "genreLevel": 0, "genreName": "tearing"}, {"id": 785, "parentGenre": 0, "genreLevel": 0, "genreName": "cutlery, silverware"}, {"id": 786, "parentGenre": 0, "genreLevel": 0, "genreName": "hammer"}, {"id": 787, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-tejano"}, {"id": 788, "parentGenre": 0, "genreLevel": 0, "genreName": "filing (rasp)"}, {"id": 789, "parentGenre": 0, "genreLevel": 0, "genreName": "pink noise"}, {"id": 790, "parentGenre": 0, "genreLevel": 0, "genreName": "chewing, mastication"}, {"id": 791, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-avant-garde jazz"}, {"id": 792, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-modern classical"}, {"id": 793, "parentGenre": 0, "genreLevel": 0, "genreName": "siren"}, {"id": 794, "parentGenre": 0, "genreLevel": 0, "genreName": "hair dryer"}, {"id": 795, "parentGenre": 0, "genreLevel": 0, "genreName": "steam"}, {"id": 796, "parentGenre": 0, "genreLevel": 0, "genreName": "vehicle horn, car horn, honking"}, {"id": 797, "parentGenre": 0, "genreLevel": 0, "genreName": "brass & military-brass band"}, {"id": 798, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-afro-cuban"}, {"id": 799, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-death metal"}, {"id": 800, "parentGenre": 0, "genreLevel": 0, "genreName": "classical-renaissance"}, {"id": 801, "parentGenre": 0, "genreLevel": 0, "genreName": "wind"}, {"id": 802, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-sludge metal"}, {"id": 803, "parentGenre": 0, "genreLevel": 0, "genreName": "hiss"}, {"id": 804, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-parody"}, {"id": 805, "parentGenre": 0, "genreLevel": 0, "genreName": "bleat"}, {"id": 806, "parentGenre": 0, "genreLevel": 0, "genreName": "whoosh, swoosh, swish"}, {"id": 807, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-bluegrass"}, {"id": 808, "parentGenre": 0, "genreLevel": 0, "genreName": "fireworks"}, {"id": 809, "parentGenre": 0, "genreLevel": 0, "genreName": "snort"}, {"id": 810, "parentGenre": 0, "genreLevel": 0, "genreName": "crowd"}, {"id": 811, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-ska"}, {"id": 812, "parentGenre": 0, "genreLevel": 0, "genreName": "blues-jump blues"}, {"id": 813, "parentGenre": 0, "genreLevel": 0, "genreName": "funk / soul-p.funk"}, {"id": 814, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-prog rock"}, {"id": 815, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-goa trance"}, {"id": 816, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-gospel"}, {"id": 817, "parentGenre": 0, "genreLevel": 0, "genreName": "basketball bounce"}, {"id": 818, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-minimal techno"}, {"id": 819, "parentGenre": 0, "genreLevel": 0, "genreName": "pig"}, {"id": 820, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-new wave"}, {"id": 821, "parentGenre": 0, "genreLevel": 0, "genreName": "civil defense siren"}, {"id": 822, "parentGenre": 0, "genreLevel": 0, "genreName": "thump, thud"}, {"id": 823, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-power pop"}, {"id": 824, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-breakbeat"}, {"id": 825, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-new beat"}, {"id": 826, "parentGenre": 0, "genreLevel": 0, "genreName": "plop"}, {"id": 827, "parentGenre": 0, "genreLevel": 0, "genreName": "clapping"}, {"id": 828, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-jazzdance"}, {"id": 829, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-beguine"}, {"id": 830, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-post-hardcore"}, {"id": 831, "parentGenre": 0, "genreLevel": 0, "genreName": "chime"}, {"id": 832, "parentGenre": 0, "genreLevel": 0, "genreName": "gasp"}, {"id": 833, "parentGenre": 0, "genreLevel": 0, "genreName": "clip-clop"}, {"id": 834, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-hardcore"}, {"id": 835, "parentGenre": 0, "genreLevel": 0, "genreName": "blues-louisiana blues"}, {"id": 836, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-emo"}, {"id": 837, "parentGenre": 0, "genreLevel": 0, "genreName": "beep, bleep"}, {"id": 838, "parentGenre": 0, "genreLevel": 0, "genreName": "wind noise (microphone)"}, {"id": 839, "parentGenre": 0, "genreLevel": 0, "genreName": "microwave oven"}, {"id": 840, "parentGenre": 0, "genreLevel": 0, "genreName": "fusillade"}, {"id": 841, "parentGenre": 0, "genreLevel": 0, "genreName": "keys jangling"}, {"id": 842, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-electro"}, {"id": 843, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-southern rock"}, {"id": 844, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-broken beat"}, {"id": 845, "parentGenre": 0, "genreLevel": 0, "genreName": "rain on surface"}, {"id": 846, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-psychobilly"}, {"id": 847, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-noise"}, {"id": 848, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-dixieland"}, {"id": 849, "parentGenre": 0, "genreLevel": 0, "genreName": "fire"}, {"id": 850, "parentGenre": 0, "genreLevel": 0, "genreName": "toothbrush"}, {"id": 851, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-latin jazz"}, {"id": 852, "parentGenre": 0, "genreLevel": 0, "genreName": "bee, wasp, etc."}, {"id": 853, "parentGenre": 0, "genreLevel": 0, "genreName": "coin (dropping)"}, {"id": 854, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-ebm"}, {"id": 855, "parentGenre": 0, "genreLevel": 0, "genreName": "grunt"}, {"id": 856, "parentGenre": 0, "genreLevel": 0, "genreName": "single-lens reflex camera"}, {"id": 857, "parentGenre": 0, "genreLevel": 0, "genreName": "gunshot, gunfire"}, {"id": 858, "parentGenre": 0, "genreLevel": 0, "genreName": "printer"}, {"id": 859, "parentGenre": 0, "genreLevel": 0, "genreName": "non-music-monolog"}, {"id": 860, "parentGenre": 0, "genreLevel": 0, "genreName": "rumble"}, {"id": 861, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-nu metal"}, {"id": 862, "parentGenre": 0, "genreLevel": 0, "genreName": "classical-impressionist"}, {"id": 863, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-doo wop"}, {"id": 864, "parentGenre": 0, "genreLevel": 0, "genreName": "field recording"}, {"id": 865, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-charanga"}, {"id": 866, "parentGenre": 0, "genreLevel": 0, "genreName": "scrape"}, {"id": 867, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-rumba"}, {"id": 868, "parentGenre": 0, "genreLevel": 0, "genreName": "drip"}, {"id": 869, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-glam"}, {"id": 870, "parentGenre": 0, "genreLevel": 0, "genreName": "frying (food)"}, {"id": 871, "parentGenre": 0, "genreLevel": 0, "genreName": "chop"}, {"id": 872, "parentGenre": 0, "genreLevel": 0, "genreName": "train horn"}, {"id": 873, "parentGenre": 0, "genreLevel": 0, "genreName": "pop-light music"}, {"id": 874, "parentGenre": 0, "genreLevel": 0, "genreName": "dial tone"}, {"id": 875, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-power electronics"}, {"id": 876, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-cubano"}, {"id": 877, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-catalan music"}, {"id": 878, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-gothic metal"}, {"id": 879, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-experimental"}, {"id": 880, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-metalcore"}, {"id": 881, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-aor"}, {"id": 882, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-post bop"}, {"id": 883, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-berlin-school"}, {"id": 884, "parentGenre": 0, "genreLevel": 0, "genreName": "reversing beeps"}, {"id": 885, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-free jazz"}, {"id": 886, "parentGenre": 0, "genreLevel": 0, "genreName": "purr"}, {"id": 887, "parentGenre": 0, "genreLevel": 0, "genreName": "wood"}, {"id": 888, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-free improvisation"}, {"id": 889, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-tech house"}, {"id": 890, "parentGenre": 0, "genreLevel": 0, "genreName": "crackle"}, {"id": 891, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-funeral doom metal"}, {"id": 892, "parentGenre": 0, "genreLevel": 0, "genreName": "sidetone"}, {"id": 893, "parentGenre": 0, "genreLevel": 0, "genreName": "knock"}, {"id": 894, "parentGenre": 0, "genreLevel": 0, "genreName": "stomach rumble"}, {"id": 895, "parentGenre": 0, "genreLevel": 0, "genreName": "white noise"}, {"id": 896, "parentGenre": 0, "genreLevel": 0, "genreName": "babbling"}, {"id": 897, "parentGenre": 0, "genreLevel": 0, "genreName": "outside, rural or natural"}, {"id": 898, "parentGenre": 0, "genreLevel": 0, "genreName": "motorcycle"}, {"id": 899, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-grindcore"}, {"id": 900, "parentGenre": 0, "genreLevel": 0, "genreName": "arrow"}, {"id": 901, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-polka"}, {"id": 902, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-twist"}, {"id": 903, "parentGenre": 0, "genreLevel": 0, "genreName": "crying, sobbing"}, {"id": 904, "parentGenre": 0, "genreLevel": 0, "genreName": "sliding door"}, {"id": 905, "parentGenre": 0, "genreLevel": 0, "genreName": "pour"}, {"id": 906, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-bop"}, {"id": 907, "parentGenre": 0, "genreLevel": 0, "genreName": "pant"}, {"id": 908, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-cajun"}, {"id": 909, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-big band"}, {"id": 910, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-post-metal"}, {"id": 911, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-industrial"}, {"id": 912, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-cool jazz"}, {"id": 913, "parentGenre": 0, "genreLevel": 0, "genreName": "owl"}, {"id": 914, "parentGenre": 0, "genreLevel": 0, "genreName": "typewriter"}, {"id": 915, "parentGenre": 0, "genreLevel": 0, "genreName": "biting"}, {"id": 916, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-hip-house"}, {"id": 917, "parentGenre": 0, "genreLevel": 0, "genreName": "whistling"}, {"id": 918, "parentGenre": 0, "genreLevel": 0, "genreName": "snare drum"}, {"id": 919, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-coldwave"}, {"id": 920, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-bleep"}, {"id": 921, "parentGenre": 0, "genreLevel": 0, "genreName": "classical-opera"}, {"id": 922, "parentGenre": 0, "genreLevel": 0, "genreName": "blues-harmonica blues"}, {"id": 923, "parentGenre": 0, "genreLevel": 0, "genreName": "rabi<PERSON> sangeet"}, {"id": 924, "parentGenre": 0, "genreLevel": 0, "genreName": "frog"}, {"id": 925, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-guajira"}, {"id": 926, "parentGenre": 0, "genreLevel": 0, "genreName": "yodeling"}, {"id": 927, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-soul-jazz"}, {"id": 928, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-lounge"}, {"id": 929, "parentGenre": 0, "genreLevel": 0, "genreName": "whack, thwack"}, {"id": 930, "parentGenre": 0, "genreLevel": 0, "genreName": "chink, clink"}, {"id": 931, "parentGenre": 0, "genreLevel": 0, "genreName": "throat clearing"}, {"id": 932, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-shoegaze"}, {"id": 933, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-boogaloo"}, {"id": 934, "parentGenre": 0, "genreLevel": 0, "genreName": "busy signal"}, {"id": 935, "parentGenre": 0, "genreLevel": 0, "genreName": "groan"}, {"id": 936, "parentGenre": 0, "genreLevel": 0, "genreName": "spray"}, {"id": 937, "parentGenre": 0, "genreLevel": 0, "genreName": "hiccup"}, {"id": 938, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-guaguanc<PERSON>"}, {"id": 939, "parentGenre": 0, "genreLevel": 0, "genreName": "thunk"}, {"id": 940, "parentGenre": 0, "genreLevel": 0, "genreName": "traffic noise, roadway noise"}, {"id": 941, "parentGenre": 0, "genreLevel": 0, "genreName": "non-music-promotional"}, {"id": 942, "parentGenre": 0, "genreLevel": 0, "genreName": "baby cry, infant cry"}, {"id": 943, "parentGenre": 0, "genreLevel": 0, "genreName": "stream"}, {"id": 944, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-illbient"}, {"id": 945, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-fusion"}, {"id": 946, "parentGenre": 0, "genreLevel": 0, "genreName": "drawer open or close"}, {"id": 947, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-arena rock"}, {"id": 948, "parentGenre": 0, "genreLevel": 0, "genreName": "raindrop"}, {"id": 949, "parentGenre": 0, "genreLevel": 0, "genreName": "burping, eructation"}, {"id": 950, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-cha-cha"}, {"id": 951, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-soft rock"}, {"id": 952, "parentGenre": 0, "genreLevel": 0, "genreName": "cattle, bovinae"}, {"id": 953, "parentGenre": 0, "genreLevel": 0, "genreName": "police car (siren)"}, {"id": 954, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-power metal"}, {"id": 955, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-son"}, {"id": 956, "parentGenre": 0, "genreLevel": 0, "genreName": "cupboard open or close"}, {"id": 957, "parentGenre": 0, "genreLevel": 0, "genreName": "tuning fork"}, {"id": 958, "parentGenre": 0, "genreLevel": 0, "genreName": "whimper"}, {"id": 959, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-art rock"}, {"id": 960, "parentGenre": 0, "genreLevel": 0, "genreName": "whoop"}, {"id": 961, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-noise"}, {"id": 962, "parentGenre": 0, "genreLevel": 0, "genreName": "cap gun"}, {"id": 963, "parentGenre": 0, "genreLevel": 0, "genreName": "drill"}, {"id": 964, "parentGenre": 0, "genreLevel": 0, "genreName": "toot"}, {"id": 965, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-stoner rock"}, {"id": 966, "parentGenre": 0, "genreLevel": 0, "genreName": "mechanisms"}, {"id": 967, "parentGenre": 0, "genreLevel": 0, "genreName": "rodents, rats, mice"}, {"id": 968, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-deathcore"}, {"id": 969, "parentGenre": 0, "genreLevel": 0, "genreName": "boom"}, {"id": 970, "parentGenre": 0, "genreLevel": 0, "genreName": "waves, surf"}, {"id": 971, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-tribal house"}, {"id": 972, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-dub techno"}, {"id": 973, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-hard bop"}, {"id": 974, "parentGenre": 0, "genreLevel": 0, "genreName": "engine knocking"}, {"id": 975, "parentGenre": 0, "genreLevel": 0, "genreName": "fly, housefly"}, {"id": 976, "parentGenre": 0, "genreLevel": 0, "genreName": "electric toothbrush"}, {"id": 977, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-minimal"}, {"id": 978, "parentGenre": 0, "genreLevel": 0, "genreName": "air conditioning"}, {"id": 979, "parentGenre": 0, "genreLevel": 0, "genreName": "blues-boogie woogie"}, {"id": 980, "parentGenre": 0, "genreLevel": 0, "genreName": "blues-electric blues"}, {"id": 981, "parentGenre": 0, "genreLevel": 0, "genreName": "wail, moan"}, {"id": 982, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-progressive trance"}, {"id": 983, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-melodic death metal"}, {"id": 984, "parentGenre": 0, "genreLevel": 0, "genreName": "doorbell"}, {"id": 985, "parentGenre": 0, "genreLevel": 0, "genreName": "female speech, woman speaking"}, {"id": 986, "parentGenre": 0, "genreLevel": 0, "genreName": "train"}, {"id": 987, "parentGenre": 0, "genreLevel": 0, "genreName": "reggae-ska"}, {"id": 988, "parentGenre": 0, "genreLevel": 0, "genreName": "livestock, farm animals, working animals"}, {"id": 989, "parentGenre": 0, "genreLevel": 0, "genreName": "air horn, truck horn"}, {"id": 990, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-progressive metal"}, {"id": 991, "parentGenre": 0, "genreLevel": 0, "genreName": "whir"}, {"id": 992, "parentGenre": 0, "genreLevel": 0, "genreName": "gargling"}, {"id": 993, "parentGenre": 0, "genreLevel": 0, "genreName": "quack"}, {"id": 994, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-honky tonk"}, {"id": 995, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-mambo"}, {"id": 996, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-ragtime"}, {"id": 997, "parentGenre": 0, "genreLevel": 0, "genreName": "camera"}, {"id": 998, "parentGenre": 0, "genreLevel": 0, "genreName": "environmental noise"}, {"id": 999, "parentGenre": 0, "genreLevel": 0, "genreName": "slam"}, {"id": 1000, "parentGenre": 0, "genreLevel": 0, "genreName": "pulse"}, {"id": 1001, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-deep techno"}, {"id": 1002, "parentGenre": 0, "genreLevel": 0, "genreName": "stir"}, {"id": 1003, "parentGenre": 0, "genreLevel": 0, "genreName": "boat, water vehicle"}, {"id": 1004, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-melodic hardcore"}, {"id": 1005, "parentGenre": 0, "genreLevel": 0, "genreName": "funk / soul-boogie"}, {"id": 1006, "parentGenre": 0, "genreLevel": 0, "genreName": "rustling leaves"}, {"id": 1007, "parentGenre": 0, "genreLevel": 0, "genreName": "telephone dialing, dtmf"}, {"id": 1008, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-future jazz"}, {"id": 1009, "parentGenre": 0, "genreLevel": 0, "genreName": "ping"}, {"id": 1010, "parentGenre": 0, "genreLevel": 0, "genreName": "meow"}, {"id": 1011, "parentGenre": 0, "genreLevel": 0, "genreName": "skateboard"}, {"id": 1012, "parentGenre": 0, "genreLevel": 0, "genreName": "bouncing"}, {"id": 1013, "parentGenre": 0, "genreLevel": 0, "genreName": "toilet flush"}, {"id": 1014, "parentGenre": 0, "genreLevel": 0, "genreName": "cowbell"}, {"id": 1015, "parentGenre": 0, "genreLevel": 0, "genreName": "buzzer"}, {"id": 1016, "parentGenre": 0, "genreLevel": 0, "genreName": "helicopter"}, {"id": 1017, "parentGenre": 0, "genreLevel": 0, "genreName": "power windows, electric windows"}, {"id": 1018, "parentGenre": 0, "genreLevel": 0, "genreName": "sawing"}, {"id": 1019, "parentGenre": 0, "genreLevel": 0, "genreName": "wheeze"}, {"id": 1020, "parentGenre": 0, "genreLevel": 0, "genreName": "aircraft"}, {"id": 1021, "parentGenre": 0, "genreLevel": 0, "genreName": "funk / soul-psychedelic"}, {"id": 1022, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-guaracha"}, {"id": 1023, "parentGenre": 0, "genreLevel": 0, "genreName": "emergency vehicle"}, {"id": 1024, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-technical death metal"}, {"id": 1025, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-drone"}, {"id": 1026, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-space-age"}, {"id": 1027, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-musique concrète"}, {"id": 1028, "parentGenre": 0, "genreLevel": 0, "genreName": "howl"}, {"id": 1029, "parentGenre": 0, "genreLevel": 0, "genreName": "scratch"}, {"id": 1030, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-idm"}, {"id": 1031, "parentGenre": 0, "genreLevel": 0, "genreName": "crowing, cock-a-doodle-doo"}, {"id": 1032, "parentGenre": 0, "genreLevel": 0, "genreName": "skidding"}, {"id": 1033, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-jazz-funk"}, {"id": 1034, "parentGenre": 0, "genreLevel": 0, "genreName": "static"}, {"id": 1035, "parentGenre": 0, "genreLevel": 0, "genreName": "goose"}, {"id": 1036, "parentGenre": 0, "genreLevel": 0, "genreName": "eruption"}, {"id": 1037, "parentGenre": 0, "genreLevel": 0, "genreName": "waterfall"}, {"id": 1038, "parentGenre": 0, "genreLevel": 0, "genreName": "hoot"}, {"id": 1039, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-funk metal"}, {"id": 1040, "parentGenre": 0, "genreLevel": 0, "genreName": "blues-chicago blues"}, {"id": 1041, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-sound collage"}, {"id": 1042, "parentGenre": 0, "genreLevel": 0, "genreName": "horse"}, {"id": 1043, "parentGenre": 0, "genreLevel": 0, "genreName": "cash register"}, {"id": 1044, "parentGenre": 0, "genreLevel": 0, "genreName": "clang"}, {"id": 1045, "parentGenre": 0, "genreLevel": 0, "genreName": "mosquito"}, {"id": 1046, "parentGenre": 0, "genreLevel": 0, "genreName": "ambulance (siren)"}, {"id": 1047, "parentGenre": 0, "genreLevel": 0, "genreName": "burst, pop"}, {"id": 1048, "parentGenre": 0, "genreLevel": 0, "genreName": "tap"}, {"id": 1049, "parentGenre": 0, "genreLevel": 0, "genreName": "hip hop-cut-up/dj"}, {"id": 1050, "parentGenre": 0, "genreLevel": 0, "genreName": "blues-rhythm & blues"}, {"id": 1051, "parentGenre": 0, "genreLevel": 0, "genreName": "vacuum cleaner"}, {"id": 1052, "parentGenre": 0, "genreLevel": 0, "genreName": "rattle"}, {"id": 1053, "parentGenre": 0, "genreLevel": 0, "genreName": "clatter"}, {"id": 1054, "parentGenre": 0, "genreLevel": 0, "genreName": "car alarm"}, {"id": 1055, "parentGenre": 0, "genreLevel": 0, "genreName": "folk, world, & country-hillbilly"}, {"id": 1056, "parentGenre": 0, "genreLevel": 0, "genreName": "alarm clock"}, {"id": 1057, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-math rock"}, {"id": 1058, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-descarga"}, {"id": 1059, "parentGenre": 0, "genreLevel": 0, "genreName": "blender"}, {"id": 1060, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-viking metal"}, {"id": 1061, "parentGenre": 0, "genreLevel": 0, "genreName": "bang"}, {"id": 1062, "parentGenre": 0, "genreLevel": 0, "genreName": "mouse"}, {"id": 1063, "parentGenre": 0, "genreLevel": 0, "genreName": "roaring cats (lions, tigers)"}, {"id": 1064, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-acid house"}, {"id": 1065, "parentGenre": 0, "genreLevel": 0, "genreName": "sheep"}, {"id": 1066, "parentGenre": 0, "genreLevel": 0, "genreName": "latin-son montuno"}, {"id": 1067, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-leftfield"}, {"id": 1068, "parentGenre": 0, "genreLevel": 0, "genreName": "sink (filling or washing)"}, {"id": 1069, "parentGenre": 0, "genreLevel": 0, "genreName": "squeal"}, {"id": 1070, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-rockabilly"}, {"id": 1071, "parentGenre": 0, "genreLevel": 0, "genreName": "snake"}, {"id": 1072, "parentGenre": 0, "genreLevel": 0, "genreName": "sewing machine"}, {"id": 1073, "parentGenre": 0, "genreLevel": 0, "genreName": "artillery fire"}, {"id": 1074, "parentGenre": 0, "genreLevel": 0, "genreName": "glass"}, {"id": 1075, "parentGenre": 0, "genreLevel": 0, "genreName": "shuffling cards"}, {"id": 1076, "parentGenre": 0, "genreLevel": 0, "genreName": "breathing"}, {"id": 1077, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-power violence"}, {"id": 1078, "parentGenre": 0, "genreLevel": 0, "genreName": "fill (with liquid)"}, {"id": 1079, "parentGenre": 0, "genreLevel": 0, "genreName": "smoke detector, smoke alarm"}, {"id": 1080, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-thrash"}, {"id": 1081, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-grunge"}, {"id": 1082, "parentGenre": 0, "genreLevel": 0, "genreName": "squawk"}, {"id": 1083, "parentGenre": 0, "genreLevel": 0, "genreName": "jingle, tinkle"}, {"id": 1084, "parentGenre": 0, "genreLevel": 0, "genreName": "jingle bell"}, {"id": 1085, "parentGenre": 0, "genreLevel": 0, "genreName": "bicycle"}, {"id": 1086, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-acid"}, {"id": 1087, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-classic rock"}, {"id": 1088, "parentGenre": 0, "genreLevel": 0, "genreName": "children shouting"}, {"id": 1089, "parentGenre": 0, "genreLevel": 0, "genreName": "noise"}, {"id": 1090, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-jazz-rock"}, {"id": 1091, "parentGenre": 0, "genreLevel": 0, "genreName": "synthetic singing"}, {"id": 1092, "parentGenre": 0, "genreLevel": 0, "genreName": "rowboat, canoe, kayak"}, {"id": 1093, "parentGenre": 0, "genreLevel": 0, "genreName": "sanding"}, {"id": 1094, "parentGenre": 0, "genreLevel": 0, "genreName": "goat"}, {"id": 1095, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-post rock"}, {"id": 1096, "parentGenre": 0, "genreLevel": 0, "genreName": "giggle"}, {"id": 1097, "parentGenre": 0, "genreLevel": 0, "genreName": "subway, metro, underground"}, {"id": 1098, "parentGenre": 0, "genreLevel": 0, "genreName": "choir"}, {"id": 1099, "parentGenre": 0, "genreLevel": 0, "genreName": "fire engine, fire truck (siren)"}, {"id": 1100, "parentGenre": 0, "genreLevel": 0, "genreName": "mechanical fan"}, {"id": 1101, "parentGenre": 0, "genreLevel": 0, "genreName": "trickle, dribble"}, {"id": 1102, "parentGenre": 0, "genreLevel": 0, "genreName": "bellow"}, {"id": 1103, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic-italo house"}, {"id": 1104, "parentGenre": 0, "genreLevel": 0, "genreName": "wind chime"}, {"id": 1105, "parentGenre": 0, "genreLevel": 0, "genreName": "electric piano"}, {"id": 1106, "parentGenre": 0, "genreLevel": 0, "genreName": "bagpipes"}, {"id": 1107, "parentGenre": 0, "genreLevel": 0, "genreName": "brass & woodwinds"}, {"id": 1108, "parentGenre": 0, "genreLevel": 0, "genreName": "tabla"}, {"id": 1109, "parentGenre": 0, "genreLevel": 0, "genreName": "harp"}, {"id": 1110, "parentGenre": 0, "genreLevel": 0, "genreName": "organ"}, {"id": 1111, "parentGenre": 0, "genreLevel": 0, "genreName": "mandolin"}, {"id": 1112, "parentGenre": 0, "genreLevel": 0, "genreName": "sitar"}, {"id": 1113, "parentGenre": 0, "genreLevel": 0, "genreName": "zither"}, {"id": 1114, "parentGenre": 0, "genreLevel": 0, "genreName": "timpani"}, {"id": 1115, "parentGenre": 0, "genreLevel": 0, "genreName": "foghorn"}, {"id": 1116, "parentGenre": 0, "genreLevel": 0, "genreName": "harmonica"}, {"id": 1117, "parentGenre": 0, "genreLevel": 0, "genreName": "stage & screen-theme"}, {"id": 1118, "parentGenre": 0, "genreLevel": 0, "genreName": "double bass"}, {"id": 1119, "parentGenre": 0, "genreLevel": 0, "genreName": "rail transport"}, {"id": 1120, "parentGenre": 0, "genreLevel": 0, "genreName": "shatter"}, {"id": 1121, "parentGenre": 0, "genreLevel": 0, "genreName": "church bell"}, {"id": 1122, "parentGenre": 0, "genreLevel": 0, "genreName": "hi-hat"}, {"id": 1123, "parentGenre": 0, "genreLevel": 0, "genreName": "alarm"}, {"id": 1124, "parentGenre": 0, "genreLevel": 0, "genreName": "wood block"}, {"id": 1125, "parentGenre": 0, "genreLevel": 0, "genreName": "pulleys"}, {"id": 1126, "parentGenre": 0, "genreLevel": 0, "genreName": "clock"}, {"id": 1127, "parentGenre": 0, "genreLevel": 0, "genreName": "chirp tone"}, {"id": 1128, "parentGenre": 0, "genreLevel": 0, "genreName": "zing"}, {"id": 1129, "parentGenre": 0, "genreLevel": 0, "genreName": "fixed-wing aircraft, airplane"}, {"id": 1130, "parentGenre": 0, "genreLevel": 0, "genreName": "trombone"}, {"id": 1131, "parentGenre": 0, "genreLevel": 0, "genreName": "maraca"}, {"id": 1132, "parentGenre": 0, "genreLevel": 0, "genreName": "drum roll"}, {"id": 1133, "parentGenre": 0, "genreLevel": 0, "genreName": "lawn mower"}, {"id": 1134, "parentGenre": 0, "genreLevel": 0, "genreName": "sonar"}, {"id": 1135, "parentGenre": 0, "genreLevel": 0, "genreName": "rapping"}, {"id": 1136, "parentGenre": 0, "genreLevel": 0, "genreName": "boiling"}, {"id": 1137, "parentGenre": 0, "genreLevel": 0, "genreName": "theremin"}, {"id": 1138, "parentGenre": 0, "genreLevel": 0, "genreName": "ratchet, pawl"}, {"id": 1139, "parentGenre": 0, "genreLevel": 0, "genreName": "cheering"}, {"id": 1140, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-krautrock"}, {"id": 1141, "parentGenre": 0, "genreLevel": 0, "genreName": "vibraphone"}, {"id": 1142, "parentGenre": 0, "genreLevel": 0, "genreName": "jackhammer"}, {"id": 1143, "parentGenre": 0, "genreLevel": 0, "genreName": "radio"}, {"id": 1144, "parentGenre": 0, "genreLevel": 0, "genreName": "clarinet"}, {"id": 1145, "parentGenre": 0, "genreLevel": 0, "genreName": "pigeon, dove"}, {"id": 1146, "parentGenre": 0, "genreLevel": 0, "genreName": "cacophony"}, {"id": 1147, "parentGenre": 0, "genreLevel": 0, "genreName": "harpsichord"}, {"id": 1148, "parentGenre": 0, "genreLevel": 0, "genreName": "whale vocalization"}, {"id": 1149, "parentGenre": 0, "genreLevel": 0, "genreName": "bell"}, {"id": 1150, "parentGenre": 0, "genreLevel": 0, "genreName": "wild animals"}, {"id": 1151, "parentGenre": 0, "genreLevel": 0, "genreName": "synthesizer"}, {"id": 1152, "parentGenre": 0, "genreLevel": 0, "genreName": "mallet percussion"}, {"id": 1153, "parentGenre": 0, "genreLevel": 0, "genreName": "french horn"}, {"id": 1154, "parentGenre": 0, "genreLevel": 0, "genreName": "power tool"}, {"id": 1155, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-oi"}, {"id": 1156, "parentGenre": 0, "genreLevel": 0, "genreName": "moo"}, {"id": 1157, "parentGenre": 0, "genreLevel": 0, "genreName": "didger<PERSON>o"}, {"id": 1158, "parentGenre": 0, "genreLevel": 0, "genreName": "shuffle"}, {"id": 1159, "parentGenre": 0, "genreLevel": 0, "genreName": "accelerating, revving, vroom"}, {"id": 1160, "parentGenre": 0, "genreLevel": 0, "genreName": "bow-wow"}, {"id": 1161, "parentGenre": 0, "genreLevel": 0, "genreName": "tubular bells"}, {"id": 1162, "parentGenre": 0, "genreLevel": 0, "genreName": "air brake"}, {"id": 1163, "parentGenre": 0, "genreLevel": 0, "genreName": "scratching (performance technique)"}, {"id": 1164, "parentGenre": 0, "genreLevel": 0, "genreName": "afrobeat"}, {"id": 1165, "parentGenre": 0, "genreLevel": 0, "genreName": "buzz"}, {"id": 1166, "parentGenre": 0, "genreLevel": 0, "genreName": "ice cream truck, ice cream van"}, {"id": 1167, "parentGenre": 0, "genreLevel": 0, "genreName": "pump (liquid)"}, {"id": 1168, "parentGenre": 0, "genreLevel": 0, "genreName": "electronic organ"}, {"id": 1169, "parentGenre": 0, "genreLevel": 0, "genreName": "croak"}, {"id": 1170, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-acid rock"}, {"id": 1171, "parentGenre": 0, "genreLevel": 0, "genreName": "train whistle"}, {"id": 1172, "parentGenre": 0, "genreLevel": 0, "genreName": "gobble"}, {"id": 1173, "parentGenre": 0, "genreLevel": 0, "genreName": "gears"}, {"id": 1174, "parentGenre": 0, "genreLevel": 0, "genreName": "jazz-modal"}, {"id": 1175, "parentGenre": 0, "genreLevel": 0, "genreName": "soukous"}, {"id": 1176, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-speed metal"}, {"id": 1177, "parentGenre": 0, "genreLevel": 0, "genreName": "slosh"}, {"id": 1178, "parentGenre": 0, "genreLevel": 0, "genreName": "effects unit"}, {"id": 1179, "parentGenre": 0, "genreLevel": 0, "genreName": "children playing"}, {"id": 1180, "parentGenre": 0, "genreLevel": 0, "genreName": "echo"}, {"id": 1181, "parentGenre": 0, "genreLevel": 0, "genreName": "banjo"}, {"id": 1182, "parentGenre": 0, "genreLevel": 0, "genreName": "odia"}, {"id": 1183, "parentGenre": 0, "genreLevel": 0, "genreName": "cymbal"}, {"id": 1184, "parentGenre": 0, "genreLevel": 0, "genreName": "ukulele"}, {"id": 1185, "parentGenre": 0, "genreLevel": 0, "genreName": "television"}, {"id": 1186, "parentGenre": 0, "genreLevel": 0, "genreName": "rock-space rock"}, {"id": 1187, "parentGenre": 0, "genreLevel": 0, "genreName": "duck"}, {"id": 1188, "parentGenre": 0, "genreLevel": 0, "genreName": "run"}, {"id": 1189, "parentGenre": 0, "genreLevel": 0, "genreName": "gush"}, {"id": 1190, "parentGenre": 0, "genreLevel": 0, "genreName": "steam whistle"}, {"id": 1191, "parentGenre": 0, "genreLevel": 0, "genreName": "motorboat, speedboat"}, {"id": 1192, "parentGenre": 0, "genreLevel": 0, "genreName": "christmas: children's"}, {"id": 1193, "parentGenre": 0, "genreLevel": 0, "genreName": "splinter"}, {"id": 1194, "parentGenre": 0, "genreLevel": 0, "genreName": "gurgling"}, {"id": 1195, "parentGenre": 0, "genreLevel": 0, "genreName": "drum machine"}, {"id": 1196, "parentGenre": 0, "genreLevel": 0, "genreName": "rattle (instrument)"}, {"id": 1197, "parentGenre": 0, "genreLevel": 0, "genreName": "bird flight, flapping wings"}, {"id": 1198, "parentGenre": 0, "genreLevel": 0, "genreName": "inside, large room or hall"}, {"id": 1199, "parentGenre": 0, "genreLevel": 0, "genreName": "drum and bass"}, {"id": 1200, "parentGenre": 0, "genreLevel": 0, "genreName": "chuckle, chortle"}, {"id": 1201, "parentGenre": 0, "genreLevel": 0, "genreName": "change ringing (campanology)"}, {"id": 1202, "parentGenre": 0, "genreLevel": 0, "genreName": "jet engine"}]}