import mongoose from "mongoose";

const otpSchema = new mongoose.Schema(
  {
    email: String,
    phone: String,
    otp: {
      type: String,
      required: true,
    },
    expiresAt: {
      type: Date,
      required: true,
    },
    userData: {
      type: Object,
      required: true,
    },
    type: {
      type: String,
      enum: ["email", "phone"],
      required: true,
    },
  },
  { timestamps: true },
);

const Otp = new mongoose.model("Otp", otpSchema);
export default Otp;
