import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { skillMatrixService } from "../../utils/skillMatrixAPI";

// Async thunks for skill matrix
export const fetchSkillMatrix = createAsyncThunk(
  "skillMatrix/fetchMatrix",
  async ({ agencyId, params = {} }, { rejectWithValue }) => {
    try {
      const response = await skillMatrixService.getMatrix(agencyId, params);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to fetch skill matrix",
      );
    }
  },
);

const initialState = {
  matrix: [],
  loading: false,
  error: null,
  filters: {
    searchTerm: "",
    role: "",
    department: "",
    skillCategory: "",
  },
};

const skillMatrixSlice = createSlice({
  name: "skillMatrix",
  initialState,
  reducers: {
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {
        searchTerm: "",
        role: "",
        department: "",
        skillCategory: "",
      };
    },
    clearError: (state) => {
      state.error = null;
    },
    updateMatrixUserSkill: (state, action) => {
      const { userId, skillId, level } = action.payload;
      const userMatrix = state.matrix.find((m) => m.user._id === userId);
      if (userMatrix) {
        userMatrix.skills[skillId] = {
          ...userMatrix.skills[skillId],
          level,
          updated_at: new Date().toISOString(),
        };
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch skill matrix
      .addCase(fetchSkillMatrix.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSkillMatrix.fulfilled, (state, action) => {
        state.loading = false;
        state.matrix = action.payload;
      })
      .addCase(fetchSkillMatrix.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { setFilters, clearFilters, clearError, updateMatrixUserSkill } =
  skillMatrixSlice.actions;

// Selectors
export const selectSkillMatrix = (state) => state.skillMatrix.matrix;
export const selectSkillMatrixLoading = (state) => state.skillMatrix.loading;
export const selectSkillMatrixError = (state) => state.skillMatrix.error;
export const selectSkillMatrixFilters = (state) => state.skillMatrix.filters;

// Filtered selectors
export const selectFilteredSkillMatrix = (state) => {
  const { matrix, filters } = state.skillMatrix;
  const { searchTerm, role, department } = filters;

  return matrix.filter((userMatrix) => {
    const user = userMatrix.user;
    const matchesSearch =
      !searchTerm ||
      user.display_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.job_title?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesRole = !role || user.role === role || user.job_title === role;
    const matchesDepartment = !department || user.department === department;

    return matchesSearch && matchesRole && matchesDepartment;
  });
};

export default skillMatrixSlice.reducer;
