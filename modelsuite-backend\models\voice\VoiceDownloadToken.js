import mongoose from "mongoose";

const voiceDownloadTokenSchema = new mongoose.Schema(
  {
    token: {
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    recordingId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "VoiceRecording",
      required: true,
      index: true,
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      index: true,
    },
    userRole: {
      type: String,
      required: true,
      enum: ["model", "agency", "employee"],
    },
    expiresAt: {
      type: Date,
      required: true,
      index: { expireAfterSeconds: 0 }, // MongoDB TTL index for automatic cleanup
    },
    used: {
      type: Boolean,
      default: false,
      index: true,
    },
    ipAddress: {
      type: String,
      required: false,
    },
    userAgent: {
      type: String,
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

// Compound index for efficient queries
voiceDownloadTokenSchema.index({ token: 1, used: 1 });
voiceDownloadTokenSchema.index({ recordingId: 1, userId: 1 });
voiceDownloadTokenSchema.index({ expiresAt: 1, used: 1 });

// Instance method to check if token is valid
voiceDownloadTokenSchema.methods.isValid = function() {
  return !this.used && this.expiresAt > new Date();
};

// Instance method to mark token as used
voiceDownloadTokenSchema.methods.markAsUsed = async function() {
  this.used = true;
  return await this.save();
};

// Static method to cleanup expired tokens (optional, TTL index handles this automatically)
voiceDownloadTokenSchema.statics.cleanupExpired = async function() {
  const result = await this.deleteMany({
    $or: [
      { expiresAt: { $lt: new Date() } },
      { used: true, createdAt: { $lt: new Date(Date.now() - 24 * 60 * 60 * 1000) } } // Remove used tokens older than 24 hours
    ]
  });
  return result.deletedCount;
};

// Static method to find valid token
voiceDownloadTokenSchema.statics.findValidToken = async function(token) {
  return await this.findOne({
    token,
    used: false,
    expiresAt: { $gt: new Date() }
  }).populate('recordingId');
};

const VoiceDownloadToken = mongoose.model("VoiceDownloadToken", voiceDownloadTokenSchema);

export default VoiceDownloadToken;
