import ffmpeg from "fluent-ffmpeg";
import os from "os";
import path from "path";
import fs from "fs";
import ffmpegPath from "ffmpeg-static";
import ffprobePath from "ffprobe-static";

/**
 * Configure FFmpeg paths based on environment and platform
 */
class FFmpegConfig {
  constructor() {
    this.platform = os.platform();
    this.ffmpegPath = null;
    this.ffprobePath = null;
    this.initialized = false;
  }

  /**
   * Initialize FFmpeg configuration
   */
  async initialize() {
    if (this.initialized) return;

    try {
      console.log("🔍 ffmpegPath from ffmpeg-static:", ffmpegPath, "Type:", typeof ffmpegPath);
      console.log("🔍 ffprobePath from ffprobe-static:", ffprobePath, "Type:", typeof ffprobePath);

      // Extract the actual path from ffprobe-static if it's an object
      let actualFfprobePath = ffprobePath;
      if (typeof ffprobePath === 'object' && ffprobePath.path) {
        actualFfprobePath = ffprobePath.path;
        console.log("🔧 Extracted ffprobe path from object:", actualFfprobePath);
      }

      // Try static binaries first (from ffmpeg-static and ffprobe-static packages)
      if (ffmpegPath && actualFfprobePath && typeof ffmpegPath === 'string' && typeof actualFfprobePath === 'string') {
        this.ffmpegPath = ffmpegPath;
        this.ffprobePath = actualFfprobePath;
        
        this.setupFFmpeg();
        this.initialized = true;
        console.log("✅ FFmpeg configured using static binaries");
        console.log(`FFmpeg: ${this.ffmpegPath}`);
        console.log(`FFprobe: ${this.ffprobePath}`);
        return;
      }

      // Try environment variables
      if (process.env.FFMPEG_PATH && process.env.FFPROBE_PATH) {
        this.ffmpegPath = process.env.FFMPEG_PATH;
        this.ffprobePath = process.env.FFPROBE_PATH;
        
        if (await this.validatePaths()) {
          this.setupFFmpeg();
          this.initialized = true;
          console.log("✅ FFmpeg configured from environment variables");
          return;
        }
      }

      // Try to detect automatically
      await this.autoDetectPaths();
      
      if (this.ffmpegPath && this.ffprobePath) {
        this.setupFFmpeg();
        this.initialized = true;
        console.log("✅ FFmpeg auto-detected and configured");
      } else {
        throw new Error("FFmpeg not found. Please install FFmpeg or set FFMPEG_PATH and FFPROBE_PATH environment variables.");
      }
    } catch (error) {
      console.error("❌ FFmpeg configuration failed:", error.message);
      throw error;
    }
  }

  /**
   * Auto-detect FFmpeg paths based on platform
   */
  async autoDetectPaths() {
    const commonPaths = this.getCommonPaths();
    
    for (const pathSet of commonPaths) {
      if (await this.validatePaths(pathSet.ffmpeg, pathSet.ffprobe)) {
        this.ffmpegPath = pathSet.ffmpeg;
        this.ffprobePath = pathSet.ffprobe;
        return;
      }
    }
  }

  /**
   * Get common installation paths for each platform
   */
  getCommonPaths() {
    switch (this.platform) {
      case 'win32':
        return [
          {
            ffmpeg: 'C:\\ffmpeg\\bin\\ffmpeg.exe',
            ffprobe: 'C:\\ffmpeg\\bin\\ffprobe.exe'
          },
          {
            ffmpeg: 'C:\\Program Files\\ffmpeg\\bin\\ffmpeg.exe',
            ffprobe: 'C:\\Program Files\\ffmpeg\\bin\\ffprobe.exe'
          },
          {
            ffmpeg: 'ffmpeg.exe', // Try system PATH
            ffprobe: 'ffprobe.exe'
          }
        ];
      case 'darwin': // macOS
        return [
          {
            ffmpeg: '/usr/local/bin/ffmpeg',
            ffprobe: '/usr/local/bin/ffprobe'
          },
          {
            ffmpeg: '/opt/homebrew/bin/ffmpeg',
            ffprobe: '/opt/homebrew/bin/ffprobe'
          },
          {
            ffmpeg: 'ffmpeg', // Try system PATH
            ffprobe: 'ffprobe'
          }
        ];
      case 'linux':
        return [
          {
            ffmpeg: '/usr/bin/ffmpeg',
            ffprobe: '/usr/bin/ffprobe'
          },
          {
            ffmpeg: '/usr/local/bin/ffmpeg',
            ffprobe: '/usr/local/bin/ffprobe'
          },
          {
            ffmpeg: 'ffmpeg', // Try system PATH
            ffprobe: 'ffprobe'
          }
        ];
      default:
        return [
          {
            ffmpeg: 'ffmpeg',
            ffprobe: 'ffprobe'
          }
        ];
    }
  }

  /**
   * Validate that FFmpeg paths exist and are executable
   */
  async validatePaths(ffmpegPath = this.ffmpegPath, ffprobePath = this.ffprobePath) {
    if (!ffmpegPath || !ffprobePath) return false;

    try {
      // For system PATH binaries, try to execute them
      if (!path.isAbsolute(ffmpegPath)) {
        return await this.testExecutables(ffmpegPath, ffprobePath);
      }

      // For absolute paths, check file existence
      const ffmpegExists = fs.existsSync(ffmpegPath);
      const ffprobeExists = fs.existsSync(ffprobePath);
      
      return ffmpegExists && ffprobeExists;
    } catch (error) {
      return false;
    }
  }

  /**
   * Test if executables work by running version commands
   */
  async testExecutables(ffmpegPath, ffprobePath) {
    return new Promise((resolve) => {
      const { spawn } = require('child_process');
      
      const testFFmpeg = spawn(ffmpegPath, ['-version'], { stdio: 'ignore' });
      testFFmpeg.on('close', (code) => {
        if (code === 0) {
          const testFFprobe = spawn(ffprobePath, ['-version'], { stdio: 'ignore' });
          testFFprobe.on('close', (probeCode) => {
            resolve(probeCode === 0);
          });
          testFFprobe.on('error', () => resolve(false));
        } else {
          resolve(false);
        }
      });
      testFFmpeg.on('error', () => resolve(false));
    });
  }

  /**
   * Setup FFmpeg with detected paths
   */
  setupFFmpeg() {
    if (this.ffmpegPath && this.ffprobePath) {
      console.log("🔍 Setting FFmpeg paths:");
      console.log("  FFmpeg path:", this.ffmpegPath, "Type:", typeof this.ffmpegPath);
      console.log("  FFprobe path:", this.ffprobePath, "Type:", typeof this.ffprobePath);
      
      ffmpeg.setFfmpegPath(this.ffmpegPath);
      ffmpeg.setFfprobePath(this.ffprobePath);
      
      console.log("✅ FFmpeg paths set successfully");
    }
  }

  /**
   * Get current configuration
   */
  getConfig() {
    return {
      platform: this.platform,
      ffmpegPath: this.ffmpegPath,
      ffprobePath: this.ffprobePath,
      initialized: this.initialized
    };
  }
}

// Create singleton instance
const ffmpegConfig = new FFmpegConfig();

export default ffmpegConfig;
export { ffmpegConfig };
