// models/ModelContract.js

import mongoose from "mongoose";

const modelContractSchema = new mongoose.Schema(
  {
    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser", // 🔁 relation with your model schema
      required: [true, "Model Id is required!"],
    },
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency", // 🔁 assume you have an agency collection
      required: [true, "Agency Id is required!"],
    },
    templateName: {
      type: String,
      required: [true, "Template Name is required!"],
    },
    pandaDocId: {
      type: String,
      required: [true, "Panda DOC ID is required!"],
    },
    status: {
      type: String,
      enum: ["sent", "viewed", "signed", "draft"],
      default: "sent",
    },
  },
  { timestamps: true },
);

//indexes
modelContractSchema.index({ pandaDocId: 1 });
modelContractSchema.index({ agencyId: 1 });

const ModelContract = mongoose.model("ModelContract", modelContractSchema);

export default ModelContract;
