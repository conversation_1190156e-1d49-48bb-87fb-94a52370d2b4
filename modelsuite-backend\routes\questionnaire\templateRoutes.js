import express from "express";
import {
  createTemplate,
  getTemplates,
  getTemplateById,
  updateTemplate,
  deleteTemplate,
} from "../../controllers/questionnaire/templateController.js";
import { verifyToken } from "../../middlewares/authMiddleware.js";

const router = express.Router();

// Only logged in users can do these
router.post("/", verifyToken, createTemplate);
router.get("/", verifyToken, getTemplates);
router.get("/:id", verifyToken, getTemplateById);
router.put("/:id", verifyToken, updateTemplate);
router.delete("/:id", verifyToken, deleteTemplate);

export default router;
