import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  fetchSkills,
  selectSkills,
  selectSkillsLoading,
} from "../../globalstate/skillMatrix/skillsSlice";

const SkillMatrixTable = ({ searchTerm = "" }) => {
  const dispatch = useDispatch();
  const skills = useSelector(selectSkills);
  const loading = useSelector(selectSkillsLoading);

  useEffect(() => {
    dispatch(fetchSkills());
  }, [dispatch]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-800 rounded-xl border border-gray-700">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          <p className="text-gray-400 text-sm">Loading skill matrix...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-xl border border-gray-700 overflow-hidden shadow-xl">
      {/* Header */}
      <div className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 px-6 py-4 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-bold text-white tracking-wide">
              SKILL MATRIX
            </h2>
            <div className="flex items-center space-x-2">
              <p className="text-gray-400 text-sm mt-1">
                Staff assignments by category and role
              </p>
              {searchTerm && (
                <span className="px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full border border-blue-500/30">
                  Filtered: "{searchTerm}"
                </span>
              )}
            </div>
          </div>
          <div className="flex items-center">
            <button
              onClick={() => dispatch(fetchSkills())}
              className="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border border-blue-500/30 rounded-lg text-sm font-medium transition-all duration-200 shadow-lg"
            >
              REFRESH DATA
            </button>
          </div>
        </div>
      </div>

      {/* Dynamic Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gradient-to-r from-gray-800 to-gray-700 border-b border-gray-600">
            <tr>
              <th className="px-6 py-4 text-left text-sm font-bold text-gray-100 uppercase tracking-wider border-r border-gray-600">
                STAFF NAME
              </th>
              {/* Get unique categories for column headers */}
              {[
                ...new Set(
                  skills.map(
                    (skill) => skill.category_id?.name || "Uncategorized",
                  ),
                ),
              ].map((categoryName) => (
                <th
                  key={categoryName}
                  className="px-6 py-4 text-center text-sm font-bold text-gray-100 uppercase tracking-wider border-r border-gray-600 last:border-r-0"
                >
                  {categoryName.toUpperCase()}
                </th>
              ))}
              {skills.length === 0 && (
                <th className="px-6 py-4 text-center text-sm font-bold text-gray-100 uppercase tracking-wider">
                  NO CATEGORIES DEFINED
                </th>
              )}
            </tr>
          </thead>
          <tbody className="bg-gray-800 divide-y divide-gray-700">
            {skills.length > 0 ? (
              // Group skills by staff name and display staff names as rows
              (() => {
                const allStaffEntries = Object.entries(
                  skills.reduce((acc, skill) => {
                    const staffName = skill.name;
                    if (!acc[staffName]) {
                      acc[staffName] = [];
                    }
                    acc[staffName].push(skill);
                    return acc;
                  }, {}),
                );

                // Filter staff names based on search term
                const filteredStaffEntries = allStaffEntries.filter(
                  ([staffName]) =>
                    searchTerm === "" ||
                    staffName.toLowerCase().includes(searchTerm.toLowerCase()),
                );

                // If no results found after filtering, show no results message
                if (filteredStaffEntries.length === 0 && searchTerm !== "") {
                  return (
                    <tr>
                      <td
                        colSpan={Math.max(
                          [
                            ...new Set(
                              skills.map(
                                (skill) =>
                                  skill.category_id?.name || "Uncategorized",
                              ),
                            ),
                          ].length + 1,
                          2,
                        )}
                        className="px-6 py-12 text-center"
                      >
                        <div className="text-gray-400">
                          <svg
                            className="mx-auto h-12 w-12 text-gray-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                            />
                          </svg>
                          <h3 className="mt-2 text-sm font-medium text-gray-300">
                            No staff found
                          </h3>
                          <p className="mt-1 text-sm text-gray-500">
                            No staff members match "{searchTerm}". Try a
                            different search term.
                          </p>
                        </div>
                      </td>
                    </tr>
                  );
                }

                return filteredStaffEntries.map(([staffName, staffSkills]) => {
                  // Get unique categories for column mapping
                  const uniqueCategories = [
                    ...new Set(
                      skills.map(
                        (skill) => skill.category_id?.name || "Uncategorized",
                      ),
                    ),
                  ];

                  return (
                    <tr
                      key={staffName}
                      className="hover:bg-gray-700/50 transition-colors duration-200"
                    >
                      <td className="px-6 py-4 whitespace-nowrap border-r border-gray-700">
                        <div className="text-sm font-medium text-white">
                          {staffName.toUpperCase()}
                        </div>
                        <div className="text-xs text-gray-400">
                          {staffSkills.length} categor
                          {staffSkills.length !== 1 ? "ies" : "y"}
                        </div>
                      </td>
                      {uniqueCategories.map((categoryName) => {
                        // Find the skill for this staff member in this category
                        const categorySkill = staffSkills.find(
                          (skill) =>
                            (skill.category_id?.name || "Uncategorized") ===
                            categoryName,
                        );

                        const proficiencyLevel =
                          categorySkill?.proficiency_level;
                        const isAssigned = !!categorySkill;

                        // Proficiency level colors and display
                        const getProficiencyDisplay = (level) => {
                          const levels = {
                            beginner: {
                              label: "BEGINNER",
                              color:
                                "bg-yellow-500/20 text-yellow-400 border-yellow-500/30",
                            },
                            intermediate: {
                              label: "INTERMEDIATE",
                              color:
                                "bg-blue-500/20 text-blue-400 border-blue-500/30",
                            },
                            advanced: {
                              label: "ADVANCED",
                              color:
                                "bg-purple-500/20 text-purple-400 border-purple-500/30",
                            },
                            expert: {
                              label: "EXPERT",
                              color:
                                "bg-green-500/20 text-green-400 border-green-500/30",
                            },
                          };
                          return (
                            levels[level] || {
                              label: "N/A",
                              color:
                                "bg-gray-600/50 text-gray-400 border-gray-600/30",
                            }
                          );
                        };

                        const display = isAssigned
                          ? getProficiencyDisplay(proficiencyLevel)
                          : {
                              label: "N/A",
                              color:
                                "bg-gray-600/50 text-gray-400 border-gray-600/30",
                            };

                        return (
                          <td
                            key={categoryName}
                            className="px-6 py-4 whitespace-nowrap text-center border-r border-gray-700 last:border-r-0"
                          >
                            <div className="inline-flex items-center">
                              <div
                                className={`w-2 h-2 rounded-full mr-3 ${isAssigned ? "bg-green-400" : "bg-gray-500"}`}
                              ></div>
                              <span
                                className={`px-3 py-1 rounded-full text-xs font-medium uppercase tracking-wide border ${display.color}`}
                              >
                                {display.label}
                              </span>
                            </div>
                          </td>
                        );
                      })}
                    </tr>
                  );
                });
              })()
            ) : (
              <tr>
                <td
                  colSpan={Math.max(
                    [
                      ...new Set(
                        skills.map(
                          (skill) => skill.category_id?.name || "Uncategorized",
                        ),
                      ),
                    ].length + 1,
                    2,
                  )}
                  className="px-6 py-12 text-center"
                >
                  <div className="text-gray-400">
                    <svg
                      className="mx-auto h-12 w-12 text-gray-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                      />
                    </svg>
                    <h3 className="mt-2 text-sm font-medium text-gray-300">
                      No staff defined
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Get started by creating categories and adding staff
                      members as skills in the "Manage Skills" tab.
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default SkillMatrixTable;
