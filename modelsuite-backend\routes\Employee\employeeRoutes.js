import express from "express";
import {
  inviteEmployee,
  activateEmployee,
  getEmployees,
  searchEmployees,
  deleteEmployee,
} from "../../controllers/Employee/employeeController.js";
import { verifyRole, verifyToken } from "../../middlewares/authMiddleware.js";

const router = express.Router();

router.post("/invite", verifyToken, verifyRole("agency"), inviteEmployee);
router.post("/activate", activateEmployee);
router.delete("/:id", verifyToken, verifyRole("agency"), deleteEmployee);

// New routes
router.get("/list", verifyToken, verifyRole("agency"), getEmployees);
router.get("/search", verifyToken, verifyRole("agency"), searchEmployees);

export default router;
