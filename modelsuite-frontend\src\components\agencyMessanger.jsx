import React, { useEffect, useState } from "react";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "./ChatWindow";
import {
  Check,
  CheckCheckIcon,
  Loader,
  Loader2,
  Loader2Icon,
  LoaderIcon,
  MessagesSquare,
  Plus,
} from "lucide-react";
import { useDispatch, useSelector } from "react-redux";
import axios from "axios";
import { addNewDmconversation } from "../globalstate/dmSlice";
import socket from "../utils/socket";
import { FileImage } from "lucide-react";
import { FileVideo } from "lucide-react";
import { FileAudio } from "lucide-react";
import { File } from "lucide-react";
import { FileText } from "lucide-react";

// Dummy data for demonstration
const groups = [];
const channels = [];

export default function AgencyMessanger({ modeinfo }) {
  const user = JSON.parse(localStorage.getItem("auth"))?.user;
  const token = JSON.parse(localStorage.getItem("auth"))?.token;

  const [activeTab, setActiveTab] = useState("groups");
  const [search, setSearch] = useState("");
  const [activeChat, setActiveChat] = useState(null);
  const [isCreatingDM, setIsCreatingDm] = useState(false);
  const dispatch = useDispatch();
  const baseURL = import.meta.env.VITE_API_BASE_URL;
  const dmConversations = useSelector((state) => state.dm.conversations);
  // Filter to get only conversations where members include the target user
  const existingDm = dmConversations?.filter((convo) =>
    convo.members.some((member) => member.userId === modeinfo._id),
  )[0];
  const dmUnread =
    existingDm?.messages?.filter(
      (m) => m.status !== "seen" && m.senderId !== user._id,
    ).length || 0;

  console.log(existingDm, "existingdm");

  const isDmLoading = useSelector((state) => state.dm.isLoading);
  // Filter conversations by tab and search
  const conversations = activeTab === "groups" ? groups : channels;
  const filteredConvos = conversations.filter((c) =>
    c.name.toLowerCase().includes(search.toLowerCase()),
  );

  const createNewDm = async (targetUserId) => {
    setIsCreatingDm(true);
    try {
      const res = await axios.post(
        `${baseURL}/messanger/dm/createNewDm`,
        { targetUserId },
        {
          headers: { Authorization: `Bearer ${token}` },
        },
      );
      // return the new conversation object
      dispatch(addNewDmconversation([res.data.conversation, user._id]));
      socket.emit("dm:addNewConversation", {
        dmConvo: res.data.conversation,
        targetUserId,
      });
      console.log(res.data.conversation);
      setIsCreatingDm(false);
    } catch (err) {
      console.error("error while adding new dm", err);
      setIsCreatingDm(false);
    }
  };

  function renderLastMessage(c) {
    if (c.messages.length === 0) return;
    const lastMessage = c.messages[c.messages.length - 1];
    const attachments = lastMessage.attachments || [];
    const text = lastMessage.text?.trim();

    if (attachments.length === 0) {
      return text || "Attachment";
    }

    const counts = {
      image: 0,
      video: 0,
      audio: 0,
      document: 0,
      other: 0,
    };

    attachments.forEach((att) => {
      if (att.mimeType.includes("image")) counts.image++;
      else if (att.mimeType.includes("video")) counts.video++;
      else if (att.mimeType.includes("audio")) counts.audio++;
      else if (
        ["pdf", "doc", "docx", "text", "txt"].includes(att.fileExtension)
      ) {
        counts.document++;
      } else {
        counts.other++;
      }
    });

    const parts = [];

    if (counts.image > 0) {
      parts.push(
        <span key="img" className="flex items-center gap-1">
          <FileImage className="w-4 h-4 text-muted-foreground" />
          {counts.image} {counts.image === 1 ? "image" : "images"}
        </span>,
      );
    }

    if (counts.video > 0) {
      parts.push(
        <span key="vid" className="flex items-center gap-1">
          <FileVideo className="w-4 h-4 text-muted-foreground" />
          {counts.video} {counts.video === 1 ? "video" : "videos"}
        </span>,
      );
    }

    if (counts.audio > 0) {
      parts.push(
        <span key="aud" className="flex items-center gap-1">
          <FileAudio className="w-4 h-4 text-muted-foreground" />
          {counts.audio} {counts.audio === 1 ? "audio" : "audios"}
        </span>,
      );
    }

    if (counts.document > 0) {
      parts.push(
        <span key="doc" className="flex items-center gap-1">
          <FileText className="w-4 h-4 text-muted-foreground" />
          {counts.document} {counts.document === 1 ? "document" : "documents"}
        </span>,
      );
    }

    if (counts.other > 0) {
      parts.push(
        <span key="other" className="flex items-center gap-1">
          <File className="w-4 h-4 text-muted-foreground" />
          {counts.other} {counts.other === 1 ? "file" : "files"}
        </span>,
      );
    }

    // Combine all into a wrapper span with spacing
    return <span className="flex flex-wrap gap-x-2 gap-y-1">{parts}</span>;
  }

  useEffect(() => {}, [existingDm, modeinfo]);

  // Demo: open ChatWindow with dummy data
  if (activeChat) {
    return (
      <ChatWindow
        convoId={activeChat.convoId}
        type={activeChat.type}
        onBack={() => setActiveChat(null)}
      />
    );
  }

  return (
    <div className="bg-gradient-to-br from-slate-900 via-blue-950 to-slate-900 text-white min-h-screen max-h-screen">
      {/* Enhanced DM Card with distinctive styling */}
      <div className="mb-6 relative">
        {/* Glow effect backdrop */}
        <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/20 via-blue-500/20 to-purple-500/20 blur-xl rounded-3xl"></div>

        {isDmLoading ? (
          // Enhanced loading effect
          <div className="relative bg-gradient-to-r from-slate-800/90 to-slate-700/90 backdrop-blur-xl border border-slate-600/50 shadow-2xl px-6 py-5 animate-pulse">
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-slate-600 to-slate-700 animate-pulse"></div>
              <div className="flex flex-col flex-1 space-y-3">
                <div className="h-5 bg-slate-600 rounded-lg w-2/3 animate-pulse"></div>
                <div className="h-3 bg-slate-700 rounded w-1/2 animate-pulse"></div>
              </div>
              <div className="w-16 h-8 bg-slate-600 rounded-full animate-pulse"></div>
            </div>
          </div>
        ) : existingDm ? (
          // Enhanced existing DM card
          <div
            className="relative bg-gradient-to-r from-cyan-600/20 via-blue-600/20 to-purple-600/20 backdrop-blur-xl border border-cyan-400/30 shadow-2xl px-6 py-5 cursor-pointer group hover:from-cyan-500/30 hover:via-blue-500/30 hover:to-purple-500/30 transition-all duration-300"
            onClick={() =>
              setActiveChat({ convoId: existingDm.convoId, type: "dm" })
            }
          >
            {/* Animated border gradient */}
            <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>

            <div className="relative flex items-center gap-5">
              <div className="relative">
                <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-cyan-500 to-purple-600 flex items-center justify-center text-white font-bold text-xl shadow-lg border-2 border-cyan-300/50 group-hover:border-cyan-300 group-hover:shadow-cyan-500/25 transition-all duration-300">
                  {modeinfo.fullName?.charAt(0)}
                </div>
                {/* Enhanced online indicator */}
                {((existingDm?.members[0]?.userId !== user._id &&
                  existingDm?.members[0]?.status.isOnline) ||
                  (existingDm?.members[1]?.userId !== user._id &&
                    existingDm?.members[1]?.status.isOnline)) && (
                  <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-gradient-to-br from-green-400 to-green-600 rounded-full border-3 border-slate-800 shadow-lg">
                    <div className="w-full h-full rounded-full bg-green-400 animate-pulse"></div>
                  </div>
                )}
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="text-xl font-bold bg-gradient-to-r from-cyan-300 to-purple-300 bg-clip-text text-transparent group-hover:from-cyan-200 group-hover:to-purple-200 transition-all duration-300">
                    {modeinfo?.fullName}
                  </h3>
                  <div className="px-3 py-1 bg-gradient-to-r from-cyan-500/30 to-purple-500/30 rounded-full border border-cyan-400/50">
                    <span className="text-xs font-semibold text-cyan-200">
                      DIRECT MESSAGE
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-2 text-sm text-slate-300">
                  {existingDm?.messages[existingDm.messages.length - 1]
                    ?.senderId == user._id &&
                  existingDm?.messages[existingDm.messages.length - 1]
                    ?.status === "sent" ? (
                    <Check className="w-4 h-4 text-slate-400 shrink-0" />
                  ) : existingDm?.messages[existingDm.messages.length - 1]
                      ?.senderId == user._id &&
                    existingDm?.messages[existingDm.messages.length - 1]
                      ?.status === "seen" ? (
                    <CheckCheckIcon className="w-4 h-4 text-cyan-400 shrink-0" />
                  ) : null}
                  <span className="truncate">
                    {renderLastMessage(existingDm)}
                  </span>
                </div>
              </div>

              {dmUnread > 0 ? (
                <div className="relative">
                  <div className="absolute inset-0 bg-green-500 blur-sm rounded-full"></div>
                  <div className="relative bg-gradient-to-br from-green-500 to-green-600 text-white rounded-full text-sm flex items-center justify-center min-w-6 p-1 h-6 text-center font-bold shadow-lg border border-green-400">
                    {dmUnread}
                  </div>
                </div>
              ) : (
                <MessagesSquare />
              )}
            </div>
          </div>
        ) : (
          // Enhanced create DM card
          <div
            className="relative bg-gradient-to-r from-slate-800/50 to-slate-700/50 backdrop-blur-xl border border-slate-600/50 shadow-2xl px-6 py-5 cursor-pointer group hover:from-slate-700/60 hover:to-slate-600/60 transition-all duration-300"
            onClick={() => createNewDm(modeinfo._id)}
          >
            <div className="flex items-center gap-5">
              <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-slate-600 to-slate-700 flex items-center justify-center text-white font-bold text-xl shadow-lg border-2 border-slate-500 group-hover:border-slate-400 transition-all duration-300">
                {modeinfo.fullName?.charAt(0)}
              </div>

              <div className="flex-1">
                <h3 className="text-xl font-bold text-white mb-2">
                  Start conversation with{" "}
                  <span className="text-slate-300">{modeinfo.fullName}</span>
                </h3>
                <p className="text-sm text-slate-400">
                  Agency to Model Communication
                </p>
              </div>

              <div className="flex items-center gap-3">
                <div className="px-4 py-2 bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/30 rounded-xl">
                  {isCreatingDM ? (
                    <Loader className="w-5 h-5 text-green-400 animate-spin" />
                  ) : (
                    <span className="text-sm font-semibold text-green-400">
                      CREATE
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Main messenger container */}
      <div className="bg-gradient-to-br from-slate-800/80 to-slate-900/80 backdrop-blur-xl border border-slate-700/50 shadow-2xl h-[calc(100vh-200px)] flex flex-col">
        {/* Enhanced tabs */}
        <div className="flex bg-gradient-to-r from-slate-900/90 to-slate-800/90 border-b border-slate-700/50">
          <button
            onClick={() => setActiveTab("groups")}
            className={`flex-1 px-6 py-4 font-semibold text-base transition-all duration-300 relative ${
              activeTab === "groups"
                ? "text-white bg-gradient-to-b from-blue-600/30 to-blue-700/30 border-b-2 border-blue-400"
                : "text-slate-400 hover:text-white hover:bg-slate-800/50"
            }`}
          >
            <span className="flex items-center justify-center gap-2">
              Groups
              <span className="bg-blue-500/20 text-blue-300 rounded-full text-xs px-2 py-1 min-w-[20px] font-bold border border-blue-500/30">
                {groups.length}
              </span>
            </span>
            {activeTab === "groups" && (
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full"></div>
            )}
          </button>

          <button
            onClick={() => setActiveTab("channels")}
            className={`flex-1 px-6 py-4 font-semibold text-base transition-all duration-300 relative ${
              activeTab === "channels"
                ? "text-white bg-gradient-to-b from-purple-600/30 to-purple-700/30 border-b-2 border-purple-400"
                : "text-slate-400 hover:text-white hover:bg-slate-800/50"
            }`}
          >
            <span className="flex items-center justify-center gap-2">
              Campaign Channels
              <span className="bg-purple-500/20 text-purple-300 rounded-full text-xs px-2 py-1 min-w-[20px] font-bold border border-purple-500/30">
                {channels.length}
              </span>
            </span>
            {activeTab === "channels" && (
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-gradient-to-r from-purple-400 to-purple-600 rounded-full"></div>
            )}
          </button>
        </div>

        {/* Enhanced search bar */}
        <div className="p-6 bg-gradient-to-r from-slate-800/50 to-slate-900/50 border-b border-slate-700/30">
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <input
                type="text"
                placeholder={`Search in ${activeTab}...`}
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-full px-4 py-3 rounded-xl bg-slate-700/50 border border-slate-600/50 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 backdrop-blur-sm"
              />
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/10 to-purple-500/10 pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity duration-300"></div>
            </div>

            <button className="w-12 h-12 rounded-full bg-gradient-to-br from-emerald-500 to-green-600 hover:from-emerald-400 hover:to-green-500 flex items-center justify-center shadow-lg hover:shadow-emerald-500/25 transition-all duration-300 border border-emerald-400/30 hover:border-emerald-300/50">
              <Plus className="w-6 h-6 text-white" />
            </button>
          </div>
        </div>

        {/* Enhanced conversation list */}
        <div className="flex-1 overflow-y-auto custom-scrollbar bg-gradient-to-b from-slate-800/30 to-slate-900/30">
          {filteredConvos.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center py-12">
              <div className="w-24 h-24 rounded-full bg-gradient-to-br from-slate-700 to-slate-800 flex items-center justify-center mb-6 border border-slate-600/50">
                <Plus className="w-12 h-12 text-slate-400" />
              </div>
              <h3 className="text-xl font-semibold text-slate-300 mb-2">
                No {activeTab} found
              </h3>
              <p className="text-slate-400 max-w-md">
                {activeTab === "groups"
                  ? "Create your first group to start collaborating with your team."
                  : "Set up campaign channels to organize your marketing efforts."}
              </p>
            </div>
          ) : (
            filteredConvos.map((c) => (
              <div
                key={c.id}
                onClick={() =>
                  setActiveChat({ convoId: "c.convoId", type: "c.type" })
                }
                className="flex items-center px-6 py-4 border-b border-slate-700/30 hover:bg-gradient-to-r hover:from-slate-700/30 hover:to-slate-800/30 cursor-pointer transition-all duration-300 group"
              >
                <div
                  className={`w-14 h-14 rounded-xl flex items-center justify-center mr-4 text-2xl font-bold transition-all duration-300 ${
                    c.unread
                      ? "bg-gradient-to-br from-blue-500 to-purple-600 border-2 border-blue-400/50 shadow-lg shadow-blue-500/20"
                      : "bg-gradient-to-br from-slate-600 to-slate-700 border border-slate-600/50 group-hover:border-slate-500"
                  }`}
                >
                  {c.avatar}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-semibold text-white truncate group-hover:text-slate-100 transition-colors">
                      {c.name}
                    </h4>
                    {c.unread > 0 && (
                      <span className="bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-full text-xs px-2 py-1 min-w-[20px] text-center font-bold shadow-lg border border-blue-400/50">
                        {c.unread}
                      </span>
                    )}
                  </div>
                  <p className="text-slate-400 text-sm truncate group-hover:text-slate-300 transition-colors">
                    {c.lastMessage}
                  </p>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
