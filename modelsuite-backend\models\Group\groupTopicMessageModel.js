import mongoose from "mongoose";

const groupTopicMessageSchema = new mongoose.Schema({
  groupId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Group",
    required: [true, "Group Id is required!"],
  },
  topicId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Topic",
    required: [true, "Topic Id is required!"],
  },
  senderId: {
    type: mongoose.Schema.Types.ObjectId,
    required: [true, "Sender Id is required!"],
    refPath: "senderModel",
  },
  senderModel: {
    type: String,
    required: true,
    enum: ["Agency", "ModelUser"], // add 'Employee' later
  },
  text: {
    type: String,
    required: [true, "Text is required!"],
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

groupTopicMessageSchema.index({ groupId: 1, topicId: 1 });
groupTopicMessageSchema.index({ topicId: 1 });

export default mongoose.model("GroupTopicMessage", groupTopicMessageSchema);
