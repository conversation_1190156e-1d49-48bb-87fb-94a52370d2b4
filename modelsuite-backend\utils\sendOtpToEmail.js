import nodemailer from "nodemailer";

export const sendOtpToEmail = async (email, otp) => {
  try {
    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: email,
      subject: "OTP for ModelSuite.ai",
      text: `Your OTP for is: ${otp}. It expires in 10 minutes.`,
    });

    // console.log(`OTP sent to email: ${email}`);
  } catch (error) {
    console.error("Failed to send OTP via Email:", error.message);
    throw new Error("<PERSON><PERSON> failed");
  }
};

export const sendEmployeeWelcomeEmail = async (to, name, tempPassword) => {
  try {
    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    const loginURL = `${process.env.FRONTEND_HOSTING_BASEURL}/agency/login`;

    await transporter.sendMail({
      from: `"ModelSuite" <${process.env.EMAIL_USER}>`,
      to,
      subject: "Your Employee Account on ModelSuite.ai",
      html: `
        <div style="font-family: Arial, sans-serif; padding: 10px;">
          <h2>Hi ${name},</h2>
          <p>You’ve been added as an employee to your agency on <strong>ModelSuite.ai</strong>.</p>
          <p><strong>Login:</strong> <a href="${loginURL}">${loginURL}</a></p>
          <p><strong>Temporary Password:</strong> ${tempPassword}</p>
          <p>Please log in and change your password immediately for security.</p>
          <br/>
          <p>– The ModelSuite Team</p>
        </div>
      `,
    });
  } catch (error) {
    console.error("Failed to send employee welcome email:", error.message);
    throw new Error("Email sending failed");
  }
};

export const sendEmployeeInviteEmail = async (to, activationLink) => {
  try {
    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    await transporter.sendMail({
      from: `"ModelSuite" <${process.env.EMAIL_USER}>`,
      to,
      subject: "You've been invited to join ModelSuite.ai",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2>Welcome to ModelSuite! 🎉</h2>
          <p>You've been invited to join a team on ModelSuite.ai. To get started, click the button below to activate your account:</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${activationLink}" 
               style="background-color: #4F46E5; color: white; padding: 12px 24px; 
                      text-decoration: none; border-radius: 6px; font-weight: bold;">
              Activate Your Account
            </a>
          </div>
          
          <p style="color: #666; font-size: 14px;">
            This invite link will expire in 24 hours. If you don't recognize this invitation, 
            please ignore this email.
          </p>
          
          <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
          
          <p style="color: #666; font-size: 12px;">
            – The ModelSuite Team
          </p>
        </div>
      `,
    });
  } catch (error) {
    console.error("Failed to send invite email:", error.message);
    throw new Error("Email sending failed");
  }
};

export const sendInvoiceEmailToClient = async ({
  clientEmail,
  clientName,
  invoiceFileUrl,
}) => {
  try {
    // Check required fields
    if (!clientEmail || !clientName || !invoiceFileUrl) {
      throw new Error(
        "Missing required fields: clientEmail, clientName, or invoiceFileUrl",
      );
    }

    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    await transporter.sendMail({
      from: `"ModelSuite" <${process.env.EMAIL_USER}>`,
      to: clientEmail,
      subject: "Your Invoice from ModelSuite.ai",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: auto; padding: 20px;">
          <h2 style="color: #4F46E5;">Hello ${clientName},</h2>
          <p>Your agency has generated a new invoice for you on <strong>ModelSuite.ai</strong>.</p>
          
          <p>
            <strong>Download your Invoice:</strong><br/>
            <a href="${invoiceFileUrl}" target="_blank" style="
              display: inline-block;
              margin-top: 10px;
              padding: 10px 20px;
              background-color: #4F46E5;
              color: #fff;
              text-decoration: none;
              border-radius: 5px;
              font-weight: bold;
            ">
              View / Download Invoice
            </a>
          </p>
          
          <p style="margin-top: 20px;">If you have any questions, please reach out to your agency directly.</p>
          
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;" />
          <p style="font-size: 12px; color: #666;">– The ModelSuite Team</p>
        </div>
      `,
    });
  } catch (error) {
    console.error("Failed to send invoice email:", error.message);
    throw new Error("Failed to send invoice email to client");
  }
};

export const sendPaymentStatusEmail = async ({
  email,
  name,
  status,
  method,
  reason,
  invoiceId,
  fileUrl,
}) => {
  const transporter = nodemailer.createTransport({
    service: "gmail",
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  });

  const subject = `Invoice ${status} - ModelSuite.ai`;

  const html = `
  <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f4f4f4; padding: 30px;">
    <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 10px; padding: 30px; box-shadow: 0 4px 8px rgba(0,0,0,0.05);">
      <div style="text-align: center;">
        <img src="https://modelsuite.ai/logo.png" alt="ModelSuite Logo" style="height: 50px; margin-bottom: 20px;" />
        <h2 style="color: #333333;">Payment Status Update</h2>
      </div>

      <p style="font-size: 16px; color: #555;">Hi <strong>${name}</strong>,</p>
      <p style="font-size: 16px; color: #555;">
        The payment status for your invoice <strong>${invoiceId}</strong> has been updated to: 
        <span style="color: ${status === "Failed" ? "#e63946" : "#2a9d8f"}; font-weight: bold;">${status}</span>.
      </p>

      <p style="font-size: 16px; color: #555;"><strong>Method:</strong> ${method}</p>

      ${
        status === "Failed"
          ? `
        <p style="font-size: 16px; color: #e63946;"><strong>Failure Reason:</strong> ${reason}</p>
      `
          : ""
      }

      <div style="margin: 20px 0; text-align: center;">
        <a href="${fileUrl}" target="_blank" style="background-color: #2a9d8f; color: #fff; padding: 12px 25px; border-radius: 5px; text-decoration: none; font-weight: bold;">View Invoice</a>
      </div>

      <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;" />

      <p style="font-size: 14px; color: #888;">Thank you for working with <strong>ModelSuite.ai</strong>. For any queries, feel free to contact our support team.</p>

      <p style="font-size: 14px; color: #aaa;">– The ModelSuite Team</p>
    </div>
  </div>
`;

  await transporter.sendMail({
    from: `"ModelSuite" <${process.env.EMAIL_USER}>`,
    to: email,
    subject,
    html,
  });
};
