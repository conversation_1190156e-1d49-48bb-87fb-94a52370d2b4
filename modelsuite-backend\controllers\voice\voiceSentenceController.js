import VoiceScript from "../../models/voice/VoiceScript.js";
import VoiceRecording from "../../models/voice/VoiceRecording.js";
import VoiceAssignment from "../../models/voice/VoiceAssignment.js";
import ModelUser from "../../models/model.js";
import { asyncHandler } from "../../utils/asyncHandler.js";
import { ApiError } from "../../utils/ApiError.js";
import { ApiResponse } from "../../utils/ApiResponse.js";
import mongoose from "mongoose";

/**
 * Upload sentence recording
 * @route POST /api/v1/voice/recordings/sentence
 * @access Model only
 */
export const uploadSentenceRecording = asyncHandler(async (req, res) => {
  const {
    assignmentId,
    scriptId,
    sentenceId,
    sentenceOrder,
    originalFilename,
    fileUrl,
    cloudinaryPublicId,
    fileSize,
    duration,
    format,
    mimeType,
    modelNotes
  } = req.body;

  if (req.user.role !== "model") {
    throw new ApiError(403, "Only models can upload sentence recordings");
  }

  // Validate required fields
  if (!assignmentId || !scriptId || !sentenceId || !fileUrl) {
    throw new ApiError(400, "Missing required fields");
  }

  // Verify assignment exists and belongs to the model
  const assignment = await VoiceAssignment.findById(assignmentId);
  if (!assignment || assignment.modelId.toString() !== req.user._id.toString()) {
    throw new ApiError(404, "Assignment not found or access denied");
  }

  // Verify script exists and has the sentence
  const script = await VoiceScript.findById(scriptId);
  if (!script || script.isDeleted) {
    throw new ApiError(404, "Script not found");
  }

  const sentence = script.sentences.find(s => s.id === sentenceId);
  if (!sentence) {
    throw new ApiError(404, "Sentence not found in script");
  }

  // Generate filename for sentence recording
  const generatedFilename = VoiceRecording.generateSentenceFilename(
    scriptId,
    req.user._id,
    sentenceId
  );

  // Create recording record
  const recording = await VoiceRecording.create({
    assignmentId,
    scriptId,
    modelId: req.user._id,
    agencyId: assignment.agencyId,
    recordingMode: 'sentence',
    sentenceId,
    sentenceOrder: sentenceOrder || sentence.order,
    originalFilename,
    generatedFilename,
    fileUrl,
    cloudinaryPublicId,
    fileSize,
    duration,
    format,
    mimeType,
    modelNotes: modelNotes || '',
    status: 'draft',
    processingStatus: 'completed'
  });

  res.status(201).json(
    new ApiResponse(201, recording, "Sentence recording uploaded successfully")
  );
});

/**
 * Get all sentence recordings for a script
 * @route GET /api/v1/voice/recordings/script/:scriptId/sentences
 * @access Model and Agency
 */
export const getScriptSentenceRecordings = asyncHandler(async (req, res) => {
  const { scriptId } = req.params;
  const { modelId } = req.query;

  if (!mongoose.Types.ObjectId.isValid(scriptId)) {
    throw new ApiError(400, "Invalid script ID");
  }

  // Build query based on user role
  let queryModelId = null;
  if (req.user.role === "model") {
    queryModelId = req.user._id;
  } else if (req.user.role === "agency" && modelId) {
    queryModelId = modelId;
  }

  const recordings = await VoiceRecording.findSentencesByScript(scriptId, queryModelId)
    .populate('modelId', 'fullName username')
    .populate('assignmentId', 'status deadline')
    .sort({ sentenceOrder: 1, createdAt: -1 });

  // Group recordings by sentence
  const recordingsBySentence = recordings.reduce((acc, recording) => {
    const sentenceId = recording.sentenceId;
    if (!acc[sentenceId]) {
      acc[sentenceId] = [];
    }
    acc[sentenceId].push(recording);
    return acc;
  }, {});

  res.status(200).json(
    new ApiResponse(
      200,
      {
        recordings: recordingsBySentence,
        totalRecordings: recordings.length
      },
      "Sentence recordings retrieved successfully"
    )
  );
});

/**
 * Auto-match sentence recording to script sentence
 * @route PUT /api/v1/voice/recordings/sentence/:id/match
 * @access Agency only
 */
export const autoMatchSentenceRecording = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { sentenceId, confidence } = req.body;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid recording ID");
  }

  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can perform auto-matching");
  }

  const recording = await VoiceRecording.findById(id);
  if (!recording || recording.isDeleted) {
    throw new ApiError(404, "Recording not found");
  }

  if (recording.agencyId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  // Verify the sentence exists in the script
  const script = await VoiceScript.findById(recording.scriptId);
  if (!script) {
    throw new ApiError(404, "Script not found");
  }

  const sentence = script.sentences.find(s => s.id === sentenceId);
  if (!sentence) {
    throw new ApiError(404, "Target sentence not found in script");
  }

  // Update recording with matched sentence
  recording.sentenceId = sentenceId;
  recording.sentenceOrder = sentence.order;
  
  // Store matching confidence in content analysis
  if (!recording.contentAnalysis) {
    recording.contentAnalysis = {};
  }
  recording.contentAnalysis.keywordMatch = confidence || 85;

  await recording.save();

  res.status(200).json(
    new ApiResponse(200, recording, "Recording matched to sentence successfully")
  );
});

/**
 * Get sentence recording progress for an assignment
 * @route GET /api/v1/voice/assignments/:assignmentId/sentence-progress
 * @access Model and Agency
 */
export const getSentenceProgress = asyncHandler(async (req, res) => {
  const { assignmentId } = req.params;

  if (!mongoose.Types.ObjectId.isValid(assignmentId)) {
    throw new ApiError(400, "Invalid assignment ID");
  }

  const assignment = await VoiceAssignment.findById(assignmentId)
    .populate('scriptId')
    .populate('modelId', 'fullName username');

  if (!assignment) {
    throw new ApiError(404, "Assignment not found");
  }

  // Check access permissions
  if (req.user.role === "model" && assignment.modelId._id.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }
  if (req.user.role === "agency" && assignment.agencyId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  const script = assignment.scriptId;
  if (script.recordingMode !== 'sentence') {
    throw new ApiError(400, "Script is not in sentence recording mode");
  }

  // Get all sentence recordings for this assignment
  const recordings = await VoiceRecording.find({
    assignmentId,
    recordingMode: 'sentence',
    isDeleted: false,
    isLatestVersion: true
  }).sort({ sentenceOrder: 1 });

  // Create progress map
  const sentenceProgress = script.sentences.map(sentence => {
    const recording = recordings.find(r => r.sentenceId === sentence.id);
    return {
      sentenceId: sentence.id,
      text: sentence.text,
      order: sentence.order,
      agencyComments: sentence.agencyComments,
      status: recording ? recording.status : 'not_started',
      recordingId: recording ? recording._id : null,
      duration: recording ? recording.duration : null,
      recordedAt: recording ? recording.createdAt : null
    };
  });

  const totalSentences = script.sentences.length;
  const recordedSentences = recordings.filter(r => r.status !== 'draft').length;
  const completionPercentage = totalSentences > 0 ? Math.round((recordedSentences / totalSentences) * 100) : 0;

  res.status(200).json(
    new ApiResponse(
      200,
      {
        assignment: {
          id: assignment._id,
          scriptTitle: script.title,
          modelName: assignment.modelId.fullName,
          status: assignment.status,
          deadline: assignment.deadline
        },
        progress: {
          totalSentences,
          recordedSentences,
          completionPercentage,
          sentences: sentenceProgress
        }
      },
      "Sentence progress retrieved successfully"
    )
  );
});

/**
 * Submit sentence recording for review
 * @route PUT /api/v1/voice/recordings/sentence/:id/submit
 * @access Model only
 */
export const submitSentenceRecording = asyncHandler(async (req, res) => {
  const { id } = req.params;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid recording ID");
  }

  if (req.user.role !== "model") {
    throw new ApiError(403, "Only models can submit recordings");
  }

  const recording = await VoiceRecording.findById(id);
  if (!recording || recording.isDeleted) {
    throw new ApiError(404, "Recording not found");
  }

  if (recording.modelId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  if (recording.status !== 'draft') {
    throw new ApiError(400, "Recording is not in draft status");
  }

  recording.status = 'submitted';
  recording.submittedAt = new Date();
  await recording.save();

  res.status(200).json(
    new ApiResponse(200, recording, "Sentence recording submitted for review")
  );
});

/**
 * Bulk submit all sentence recordings for an assignment
 * @route PUT /api/v1/voice/assignments/:assignmentId/submit-sentences
 * @access Model only
 */
export const bulkSubmitSentenceRecordings = asyncHandler(async (req, res) => {
  const { assignmentId } = req.params;

  if (!mongoose.Types.ObjectId.isValid(assignmentId)) {
    throw new ApiError(400, "Invalid assignment ID");
  }

  if (req.user.role !== "model") {
    throw new ApiError(403, "Only models can submit recordings");
  }

  const assignment = await VoiceAssignment.findById(assignmentId);
  if (!assignment || assignment.modelId.toString() !== req.user._id.toString()) {
    throw new ApiError(404, "Assignment not found or access denied");
  }

  // Find all draft sentence recordings for this assignment
  const draftRecordings = await VoiceRecording.find({
    assignmentId,
    modelId: req.user._id,
    recordingMode: 'sentence',
    status: 'draft',
    isDeleted: false,
    isLatestVersion: true
  });

  if (draftRecordings.length === 0) {
    throw new ApiError(400, "No draft sentence recordings found");
  }

  // Update all draft recordings to submitted
  const submittedAt = new Date();
  await VoiceRecording.updateMany(
    {
      _id: { $in: draftRecordings.map(r => r._id) }
    },
    {
      status: 'submitted',
      submittedAt
    }
  );

  // Update assignment status
  assignment.status = 'submitted';
  assignment.submittedAt = submittedAt;
  await assignment.save();

  res.status(200).json(
    new ApiResponse(
      200,
      {
        submittedCount: draftRecordings.length,
        assignmentStatus: assignment.status
      },
      "All sentence recordings submitted for review"
    )
  );
});