import mongoose from "mongoose";
import jwt from "jsonwebtoken";

const permissionSchema = new mongoose.Schema(
  {
    tasks: {
      view: { type: Boolean, default: false },
      assign: { type: Boolean, default: false },
      approve: { type: Boolean, default: false },
    },
    model: {
      view: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
    },
    uploads: {
      review: { type: Boolean, default: false },
    },
    calendar: {
      view: { type: Boolean, default: false },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
    },
    messages: {
      view: { type: Boolean, default: false },
      send: { type: Boolean, default: false },
    },
    earnings: {
      view: { type: Boolean, default: false },
      manage: { type: Boolean, default: false },
    },
    campaign: {
      view: { type: Boolean, default: false },
      assign: { type: Boolean, default: false },
    },
    notes: {
      view: { type: Boolean, default: false },
      create: { type: Boolean, default: false },
    },
    performance: {
      view: { type: Boolean, default: false },
    },
    accesslog: {
      view: { type: <PERSON>olean, default: false },
    },
  },
  { _id: false },
);

const employeeSchema = new mongoose.Schema({
  agencyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Agency",
    required: true,
  },

  role: {
    type: String,
    default: "employee",
    immutable: true,
  },

  userType: {
    type: String,
    enum: ["creator", "manager", "viewer"],
    required: true,
  },

  name: {
    type: String,
    required: true,
    trim: true,
  },

  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
  },

  password: {
    type: String,
    required: true,
    minlength: 6,
  },

  status: {
    type: String,
    enum: ["active", "inactive", "suspended"],
    default: "active",
  },

  loginRefreshToken: {
    type: String,
  },

  permissions: {
    type: permissionSchema,
    default: () => ({}),
  },

  profileImage: String,
  phone: String,

  createdAt: {
    type: Date,
    default: Date.now,
  },
});

employeeSchema.methods.generateAccessToken = function () {
  return jwt.sign(
    { _id: this._id, role: "employee" },
    process.env.LOGIN_ACCESS_TOKEN_SECRET,
    {
      expiresIn: process.env.LOGIN_ACCESS_TOKEN_EXPIRY,
    },
  );
};

employeeSchema.methods.generateRefreshToken = function () {
  return jwt.sign(
    { _id: this._id, role: "employee" },
    process.env.LOGIN_REFRESH_TOKEN_SECRET,
    {
      expiresIn: process.env.LOGIN_REFRESH_TOKEN_EXPIRY,
    },
  );
};

export default mongoose.model("Employee", employeeSchema);
