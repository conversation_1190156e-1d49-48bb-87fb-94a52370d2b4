import express from "express";
import upload from "../../middlewares/cloudinaryUpload.js";

import {
  uploadInvoice,
  getInvoices,
  updateSingleInvoiceWithFile,
  mass_payment_status,
  exportInvoices,
  getInvoiceById,
  updateInvoice,
  deleteInvoice,
  getInvoices_dash,
} from "../../controllers/billing/billingController.js";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import { checkAgency } from "../../middlewares/roleCheck.js";
const router = express.Router();

router.post(
  "/invoice",
  verifyToken,
  checkAgency,
  upload.single("file"),
  uploadInvoice,
);
router.post(
  "/mass-update",
  verifyToken,
  checkAgency,
  upload.any(),
  mass_payment_status,
);
router.get("/invoices", verifyToken, getInvoices);

router.post(
  "/invoice",
  verifyToken,
  checkAgency,
  upload.single("file"),
  uploadInvoice,
);
router.get("/invoices", verifyToken, getInvoices);

router.get("/invoices/export", verifyToken, exportInvoices);
router.put("/invoices/:id", verifyToken, checkAgency, updateInvoice);
router.delete("/invoices/:id", verifyToken, checkAgency, deleteInvoice);
router.get("/invoices/:id", verifyToken, getInvoiceById);
router.put(
  "/update/:id",
  verifyToken,
  checkAgency,
  upload.single("screenshot"),
  updateSingleInvoiceWithFile,
);

router.get("/dashboard", verifyToken, checkAgency, getInvoices_dash);
export default router;
