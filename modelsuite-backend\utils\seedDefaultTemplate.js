import Template from "../models/questionnaire/Template.js";
import defaultTemplate from "./defaultTemplate.js";

export const seedDefaultTemplate = async () => {
  try {
    const existingTemplate = await Template.findOne({
      title: defaultTemplate.title,
      agencyId: null,
    });

    if (!existingTemplate) {
      await Template.create({
        ...defaultTemplate,
        agencyId: null,
        createdBy: null,
      });
      console.log("✅ Default template seeded successfully");
    } else {
      console.log("✅ Default template already exists");
    }
  } catch (error) {
    console.error("❌ Failed to seed default template:", error);
  }
};
