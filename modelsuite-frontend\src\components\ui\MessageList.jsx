import React, { memo } from "react";
import ChatMessage from "./ChatMessage";
import { Loader } from "lucide-react";

const MessageList = memo(
  ({
    messages,
    user,
    type,
    hasMore,
    expandedMessages,
    toggleReadMore,
    handleMessageSeen,
    handleRightClick,
    handleSendMessage,
    handleTouchStart,
    handleTouchEnd,
    handleTouchMove,
    messageRef,
    isFetchingMore,
    activeAudios,
    handleReactionsClick,
  }) => {
    let lastDate = null;

    const getDateLabel = (msgDate, today, yesterday) => {
      if (msgDate.toDateString() === today.toDateString()) {
        return "Today";
      } else if (msgDate.toDateString() === yesterday.toDateString()) {
        return "Yesterday";
      } else {
        return msgDate.toLocaleDateString(undefined, {
          year: "numeric",
          month: "long",
          day: "numeric",
        });
      }
    };

    return (
      <>
        {/* Loading indicator at top */}
        {isFetchingMore && (
          <div className="flex justify-center py-4">
            <Loader className="animate-spin text-blue-400" size={20} />
          </div>
        )}

        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-32 text-gray-400">
            No messages yet. Start the conversation!
          </div>
        ) : (
          messages.map((msg, i) => {
            const msgDate = new Date(msg.createdAt);
            const msgDateString = msgDate.toDateString();

            const today = new Date();
            const yesterday = new Date();
            yesterday.setDate(today.getDate() - 1);

            let dateLabel = null;

            let replyTomsg;
            if (msg.replyTo) {
              replyTomsg = messages.find((m) => m._id === msg.replyTo);
            }

            if (i === 0) {
              // First message: set lastDate and show label only if no more messages to fetch
              lastDate = msgDateString;
              if (!hasMore) {
                dateLabel = getDateLabel(msgDate, today, yesterday);
                lastDate = msgDateString;
              }
            }

            const isOwn = msg.senderId === user._id;
            let position;
            if (
              messages[i - 1]?.senderId !== msg.senderId &&
              messages[i + 1]?.senderId === msg.senderId
            )
              position = "top";
            if (
              messages[i - 1]?.senderId === msg.senderId &&
              messages[i + 1]?.senderId === msg.senderId
            )
              position = "center";
            if (
              messages[i - 1]?.senderId === msg.senderId &&
              messages[i + 1]?.senderId !== msg.senderId
            )
              position = "bottom";

            return (
              <MemoizedMessageItem
                key={msg._id || i}
                msg={msg}
                i={i}
                isOwn={isOwn}
                type={type}
                position={position}
                replyTomsg={replyTomsg}
                dateLabel={dateLabel}
                expandedMessages={expandedMessages}
                toggleReadMore={toggleReadMore}
                handleMessageSeen={handleMessageSeen}
                handleRightClick={handleRightClick}
                handleSendMessage={handleSendMessage}
                handleTouchStart={handleTouchStart}
                handleTouchEnd={handleTouchEnd}
                handleTouchMove={handleTouchMove}
                messageRef={messageRef}
                activeAudios={activeAudios}
                handleReactionsClick={handleReactionsClick}
              />
            );
          })
        )}
      </>
    );
  },
);

// Individual Message Item Component - Memoized to prevent re-renders
const MemoizedMessageItem = memo(
  ({
    msg,
    i,
    isOwn,
    type,
    position,
    dateLabel,
    replyTomsg,
    expandedMessages,
    toggleReadMore,
    handleMessageSeen,
    handleRightClick,
    handleSendMessage,
    handleTouchStart,
    handleTouchEnd,
    handleTouchMove,
    messageRef,
    activeAudios,
    handleReactionsClick,
  }) => {
    return (
      <div>
        {dateLabel && (
          <div className="flex items-center justify-center text-sm text-gray-400 py-2">
            <span className="px-4 py-1 rounded-full bg-[#2a3942]">
              {dateLabel}
            </span>
          </div>
        )}
        <div
          ref={messageRef}
          onTouchStart={(e) => handleTouchStart(e, msg._id, isOwn)}
          onTouchEnd={handleTouchEnd}
          onTouchMove={handleTouchMove}
          className="select-none"
        >
          <ChatMessage
            msg={msg}
            i={i}
            key={msg._id}
            id={msg._id}
            isOwn={isOwn}
            convoType={type}
            msgType={msg.type}
            position={position}
            replyTomsg={replyTomsg}
            expandedMessages={expandedMessages}
            toggleReadMore={toggleReadMore}
            handleMessageSeen={handleMessageSeen}
            handleRightClick={handleRightClick}
            handleSendMessage={handleSendMessage}
            activeAudios={activeAudios}
            handleReactionsClick={handleReactionsClick}
          />
        </div>
      </div>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison function - only re-render if these specific props change
    return (
      prevProps.msg._id === nextProps.msg._id &&
      prevProps.msg.status === nextProps.msg.status &&
      prevProps.msg.text === nextProps.msg.text &&
      prevProps.expandedMessages[prevProps.i] ===
        nextProps.expandedMessages[nextProps.i] &&
      prevProps.position === nextProps.position &&
      prevProps.dateLabel === nextProps.dateLabel &&
      JSON.stringify(prevProps.msg.attachments) ===
        JSON.stringify(nextProps.msg.attachments) &&
      JSON.stringify(prevProps.msg.reactions) ===
        JSON.stringify(nextProps.msg.reactions)
    );
  },
);

export default React.memo(MessageList);
