import { asyncHand<PERSON> } from "../../utils/asyncHandler.js";
import { ApiError } from "../../utils/ApiError.js";
import { ApiResponse } from "../../utils/ApiResponse.js";
import QuestionSection from "../../models/voice/QuestionSection.js";
import QuestionTemplate from "../../models/voice/QuestionTemplate.js";
import VoiceAssignment from "../../models/voice/VoiceAssignment.js";

// ============================================================================
// QUESTION SECTIONS MANAGEMENT
// ============================================================================

// Get all sections (default + agency custom)
export const getSections = asyncHandler(async (req, res) => {
  const { page = 1, limit = 50, includeQuestionCount = true } = req.query;
  
  // Build query - include default sections and user's custom sections
  const query = {
    $or: [
      { isDefault: true },
      { createdBy: req.user._id }
    ]
  };

  const sections = await QuestionSection.find(query)
    .sort({ isDefault: -1, createdAt: -1 })
    .skip((parseInt(page) - 1) * parseInt(limit))
    .limit(parseInt(limit))
    .lean();

  // Add question count if requested
  if (includeQuestionCount && sections.length > 0) {
    const sectionIds = sections.map(s => s._id);
    const questionCounts = await QuestionTemplate.aggregate([
      { $match: { sectionId: { $in: sectionIds }, isDeleted: false } },
      { $group: { _id: "$sectionId", count: { $sum: 1 } } }
    ]);

    const countMap = {};
    questionCounts.forEach(item => {
      countMap[item._id.toString()] = item.count;
    });

    sections.forEach(section => {
      section.questionCount = countMap[section._id.toString()] || 0;
    });
  }

  const totalSections = await QuestionSection.countDocuments(query);

  res.status(200).json(
    new ApiResponse(200, {
      sections,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalSections / parseInt(limit)),
        totalSections,
        hasNextPage: parseInt(page) < Math.ceil(totalSections / parseInt(limit)),
        hasPrevPage: parseInt(page) > 1
      }
    }, "Sections retrieved successfully")
  );
});

// Get section by ID
export const getSectionById = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { includeQuestions = false } = req.query;

  const section = await QuestionSection.findById(id);
  if (!section) {
    throw new ApiError(404, "Section not found");
  }

  // Check access permissions
  if (!section.isDefault && section.createdBy?.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied to this section");
  }

  let result = section.toObject();

  if (includeQuestions) {
    const questions = await QuestionTemplate.find({ 
      sectionId: id, 
      isDeleted: false 
    }).sort({ createdAt: 1 });
    result.questions = questions;
  }

  res.status(200).json(
    new ApiResponse(200, result, "Section retrieved successfully")
  );
});

// Create custom section (Agency only)
export const createSection = asyncHandler(async (req, res) => {
  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can create custom sections");
  }

  const { title, description } = req.body;

  if (!title?.trim()) {
    throw new ApiError(400, "Section title is required");
  }

  // Check for duplicate titles for this agency
  const existingSection = await QuestionSection.findOne({
    title: title.trim(),
    createdBy: req.user._id,
    isDeleted: false
  });

  if (existingSection) {
    throw new ApiError(400, "Section with this title already exists");
  }

  const section = await QuestionSection.create({
    title: title.trim(),
    description: description?.trim() || "",
    isDefault: false,
    createdBy: req.user._id
  });

  res.status(201).json(
    new ApiResponse(201, section, "Custom section created successfully")
  );
});

// Update section (Agency only, custom sections only)
export const updateSection = asyncHandler(async (req, res) => {
  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can update sections");
  }

  const { id } = req.params;
  const { title, description } = req.body;

  const section = await QuestionSection.findById(id);
  if (!section) {
    throw new ApiError(404, "Section not found");
  }

  if (section.isDefault) {
    throw new ApiError(400, "Cannot update default sections");
  }

  if (section.createdBy?.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Can only update your own sections");
  }

  // Check for duplicate titles (excluding current section)
  if (title && title.trim() !== section.title) {
    const existingSection = await QuestionSection.findOne({
      title: title.trim(),
      createdBy: req.user._id,
      _id: { $ne: id },
      isDeleted: false
    });

    if (existingSection) {
      throw new ApiError(400, "Section with this title already exists");
    }
  }

  if (title?.trim()) section.title = title.trim();
  if (description !== undefined) section.description = description?.trim() || "";

  await section.save();

  res.status(200).json(
    new ApiResponse(200, section, "Section updated successfully")
  );
});

// Delete section (Agency only, custom sections only)
export const deleteSection = asyncHandler(async (req, res) => {
  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can delete sections");
  }

  const { id } = req.params;

  const section = await QuestionSection.findById(id);
  if (!section) {
    throw new ApiError(404, "Section not found");
  }

  if (section.isDefault) {
    throw new ApiError(400, "Cannot delete default sections");
  }

  if (section.createdBy?.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Can only delete your own sections");
  }

  // Check if section has questions or is used in assignments
  const questionsCount = await QuestionTemplate.countDocuments({ 
    sectionId: id, 
    isDeleted: false 
  });

  if (questionsCount > 0) {
    throw new ApiError(400, "Cannot delete section that contains questions. Delete questions first.");
  }

  // Soft delete
  section.isDeleted = true;
  section.deletedAt = new Date();
  await section.save();

  res.status(200).json(
    new ApiResponse(200, null, "Section deleted successfully")
  );
});

// ============================================================================
// QUESTION TEMPLATES MANAGEMENT
// ============================================================================

// Get questions by section
export const getQuestionsBySection = asyncHandler(async (req, res) => {
  const { sectionId } = req.params;
  const { page = 1, limit = 50, search = "" } = req.query;

  // Verify section access
  const section = await QuestionSection.findById(sectionId);
  if (!section) {
    throw new ApiError(404, "Section not found");
  }

  if (!section.isDefault && section.createdBy?.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied to this section");
  }

  // Build query
  const query = { sectionId, isDeleted: false };
  if (search.trim()) {
    query.text = { $regex: search.trim(), $options: "i" };
  }

  const questions = await QuestionTemplate.find(query)
    .sort({ createdAt: 1 })
    .skip((parseInt(page) - 1) * parseInt(limit))
    .limit(parseInt(limit))
    .lean();

  const totalQuestions = await QuestionTemplate.countDocuments(query);

  res.status(200).json(
    new ApiResponse(200, {
      questions,
      section: {
        _id: section._id,
        title: section.title,
        description: section.description,
        isDefault: section.isDefault
      },
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalQuestions / parseInt(limit)),
        totalQuestions,
        hasNextPage: parseInt(page) < Math.ceil(totalQuestions / parseInt(limit)),
        hasPrevPage: parseInt(page) > 1
      }
    }, "Questions retrieved successfully")
  );
});

// Get all questions across sections with filtering
export const getAllQuestions = asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 50, 
    search = "", 
    sectionIds = "",
    includeSection = true 
  } = req.query;

  // Build section access query
  const sectionQuery = {
    $or: [
      { isDefault: true },
      { createdBy: req.user._id }
    ]
  };

  // Filter by specific sections if provided
  if (sectionIds.trim()) {
    const ids = sectionIds.split(",").map(id => id.trim()).filter(Boolean);
    sectionQuery._id = { $in: ids };
  }

  const accessibleSections = await QuestionSection.find(sectionQuery).select("_id");
  const accessibleSectionIds = accessibleSections.map(s => s._id);

  // Build questions query
  const query = { 
    sectionId: { $in: accessibleSectionIds },
    isDeleted: false 
  };

  if (search.trim()) {
    query.text = { $regex: search.trim(), $options: "i" };
  }

  let questionsQuery = QuestionTemplate.find(query);

  if (includeSection) {
    questionsQuery = questionsQuery.populate("sectionId", "title description isDefault");
  }

  const questions = await questionsQuery
    .sort({ createdAt: 1 })
    .skip((parseInt(page) - 1) * parseInt(limit))
    .limit(parseInt(limit))
    .lean();

  const totalQuestions = await QuestionTemplate.countDocuments(query);

  res.status(200).json(
    new ApiResponse(200, {
      questions,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalQuestions / parseInt(limit)),
        totalQuestions,
        hasNextPage: parseInt(page) < Math.ceil(totalQuestions / parseInt(limit)),
        hasPrevPage: parseInt(page) > 1
      }
    }, "Questions retrieved successfully")
  );
});

// Add question to section (Agency only)
export const addQuestionToSection = asyncHandler(async (req, res) => {
  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can add questions");
  }

  const { sectionId } = req.params;
  const { text } = req.body;

  if (!text?.trim()) {
    throw new ApiError(400, "Question text is required");
  }

  // Verify section access
  const section = await QuestionSection.findById(sectionId);
  if (!section) {
    throw new ApiError(404, "Section not found");
  }

  if (section.isDefault) {
    throw new ApiError(400, "Cannot add questions to default sections");
  }

  if (section.createdBy?.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Can only add questions to your own sections");
  }

  // Check for duplicate questions in the same section
  const existingQuestion = await QuestionTemplate.findOne({
    sectionId,
    text: text.trim(),
    isDeleted: false
  });

  if (existingQuestion) {
    throw new ApiError(400, "Question already exists in this section");
  }

  const question = await QuestionTemplate.create({
    text: text.trim(),
    sectionId,
    createdBy: req.user._id
  });

  const populatedQuestion = await QuestionTemplate.findById(question._id)
    .populate("sectionId", "title description isDefault");

  res.status(201).json(
    new ApiResponse(201, populatedQuestion, "Question added successfully")
  );
});

// Update question (Agency only, custom questions only)
export const updateQuestion = asyncHandler(async (req, res) => {
  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can update questions");
  }

  const { id } = req.params;
  const { text } = req.body;

  if (!text?.trim()) {
    throw new ApiError(400, "Question text is required");
  }

  const question = await QuestionTemplate.findById(id);
  if (!question) {
    throw new ApiError(404, "Question not found");
  }

  if (!question.createdBy) {
    throw new ApiError(400, "Cannot update default questions");
  }

  if (question.createdBy?.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Can only update your own questions");
  }

  // Check for duplicate questions in the same section (excluding current question)
  const existingQuestion = await QuestionTemplate.findOne({
    sectionId: question.sectionId,
    text: text.trim(),
    _id: { $ne: id },
    isDeleted: false
  });

  if (existingQuestion) {
    throw new ApiError(400, "Question already exists in this section");
  }

  question.text = text.trim();
  await question.save();

  const populatedQuestion = await QuestionTemplate.findById(question._id)
    .populate("sectionId", "title description isDefault");

  res.status(200).json(
    new ApiResponse(200, populatedQuestion, "Question updated successfully")
  );
});

// Delete question (Agency only, custom questions only)
export const deleteQuestion = asyncHandler(async (req, res) => {
  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can delete questions");
  }

  const { id } = req.params;

  const question = await QuestionTemplate.findById(id);
  if (!question) {
    throw new ApiError(404, "Question not found");
  }

  if (!question.createdBy) {
    throw new ApiError(400, "Cannot delete default questions");
  }

  if (question.createdBy?.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Can only delete your own questions");
  }

  // Check if question is used in any assignments
  const assignmentsCount = await VoiceAssignment.countDocuments({
    questionIds: id,
    isDeleted: false
  });

  if (assignmentsCount > 0) {
    throw new ApiError(400, "Cannot delete question that is used in assignments");
  }

  // Soft delete
  question.isDeleted = true;
  question.deletedAt = new Date();
  await question.save();

  res.status(200).json(
    new ApiResponse(200, null, "Question deleted successfully")
  );
});

// ============================================================================
// STATISTICS AND ANALYTICS
// ============================================================================

// Get questions statistics
export const getQuestionsStats = asyncHandler(async (req, res) => {
  const { timeRange = "all" } = req.query;

  let dateFilter = {};
  if (timeRange !== "all") {
    const now = new Date();
    let startDate;

    switch (timeRange) {
      case "week":
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "month":
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case "year":
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = null;
    }

    if (startDate) {
      dateFilter.createdAt = { $gte: startDate };
    }
  }

  // Build section access query
  const sectionQuery = {
    $or: [
      { isDefault: true },
      { createdBy: req.user._id }
    ],
    isDeleted: false
  };

  const accessibleSections = await QuestionSection.find(sectionQuery);
  const accessibleSectionIds = accessibleSections.map(s => s._id);

  // Get statistics
  const [
    totalSections,
    totalQuestions,
    sectionStats,
    assignmentStats
  ] = await Promise.all([
    QuestionSection.countDocuments({ ...sectionQuery }),
    QuestionTemplate.countDocuments({ 
      sectionId: { $in: accessibleSectionIds },
      isDeleted: false,
      ...dateFilter
    }),
    QuestionTemplate.aggregate([
      { 
        $match: { 
          sectionId: { $in: accessibleSectionIds },
          isDeleted: false,
          ...dateFilter
        }
      },
      {
        $group: {
          _id: "$sectionId",
          questionCount: { $sum: 1 }
        }
      },
      {
        $lookup: {
          from: "questionsections",
          localField: "_id",
          foreignField: "_id",
          as: "section"
        }
      },
      {
        $unwind: "$section"
      },
      {
        $project: {
          sectionTitle: "$section.title",
          questionCount: 1,
          isDefault: "$section.isDefault"
        }
      }
    ]),
    req.user.role === "agency" ? VoiceAssignment.countDocuments({
      agencyId: req.user._id,
      isDeleted: false,
      ...dateFilter
    }) : VoiceAssignment.countDocuments({
      modelId: req.user._id,
      isDeleted: false,
      ...dateFilter
    })
  ]);

  res.status(200).json(
    new ApiResponse(200, {
      totalSections,
      totalQuestions,
      totalAssignments: assignmentStats,
      sectionBreakdown: sectionStats,
      accessibleSections: accessibleSections.length
    }, "Statistics retrieved successfully")
  );
});

export default {
  // Sections
  getSections,
  getSectionById,
  createSection,
  updateSection,
  deleteSection,
  
  // Questions
  getQuestionsBySection,
  getAllQuestions,
  addQuestionToSection,
  updateQuestion,
  deleteQuestion,
  
  // Stats
  getQuestionsStats
};
