import mongoose from "mongoose";

const boardSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, "Board title is required!"],
    },
    description: {
      type: String,
      default: "",
    },
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: [true, "Agency ID is required!"],
    },
    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: [true, "Model ID is required!"],
    },
    background: {
      type: String,
      default: "bg-gradient-to-br from-blue-500 to-cyan-500",
    },
    isTemplate: {
      type: Boolean,
      default: false,
    },
    isArchived: {
      type: Boolean,
      default: false,
    },
    starred: {
      type: Boolean,
      default: false,
    },
    members: [
      {
        user: {
          type: mongoose.Schema.Types.ObjectId,
          refPath: "members.userType",
        },
        userType: {
          type: String,
          enum: ["Agency", "ModelUser"],
        },
        role: {
          type: String,
          enum: ["admin", "member"],
          default: "member",
        },
      },
    ],
    lists: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "List",
      },
    ],
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
);

// Indexes for faster queries
boardSchema.index({ agencyId: 1, isArchived: 1 });
boardSchema.index({ modelId: 1, isArchived: 1 });
boardSchema.index({ "members.user": 1 });

export default mongoose.model("Board", boardSchema);
