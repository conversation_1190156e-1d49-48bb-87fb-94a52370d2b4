import mongoose from "mongoose";

const instagramAccountSchema = new mongoose.Schema(
  {
    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: [true, "modelId is required"],
      unique: true, // one IG per model
    },
    igId: String,
    fbPageId: String,
    pageAccessToken: String,
    accessToken: String,
    tokenExpiresAt: Date,
  },
  { timestamps: true },
);

export default mongoose.model("InstagramAccount", instagramAccountSchema);
