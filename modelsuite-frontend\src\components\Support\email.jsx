import { useState } from "react";
import { Link } from "react-router-dom";

export default function ContactSupport() {
  const [form, setForm] = useState({
    name: "",
    email: "",
    subject: "",
    category: "General Issue",
    urgency: "Normal",
    message: "",
    attachment: null,
  });

  const [status, setStatus] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleChange = (e) => {
    const { name, value, files } = e.target;
    if (name === "attachment") {
      setForm({ ...form, attachment: files[0] });
    } else {
      setForm({ ...form, [name]: value });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!form.name || !form.email || !form.message) {
      setStatus("Please fill in all required fields.");
      return;
    }

    const formData = new FormData();
    formData.append("name", form.name);
    formData.append("email", form.email);
    formData.append("subject", form.subject);
    formData.append("category", form.category);
    formData.append("urgency", form.urgency);
    formData.append("message", form.message);
    if (form.attachment) {
      formData.append("attachment", form.attachment);
    }

    setLoading(true);
    setStatus(null);

    try {
      const auth = JSON.parse(localStorage.getItem("auth"));
      const authToken = auth?.token; // ✅ Correct key
      // Or use cookie/session as needed

      const response = await fetch(
        `${import.meta.env.VITE_API_BASE_URL}/support/submit`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
          body: formData,
        },
      );

      const data = await response.json();

      if (response.ok) {
        setStatus(
          "Thanks! We've received your request and will reply within 24–48 hours.",
        );
        setForm({
          name: "",
          email: "",
          subject: "",
          category: "General Issue",
          urgency: "Normal",
          message: "",
          attachment: null,
        });
      } else {
        setStatus(data.error || "Something went wrong. Please try again.");
      }
    } catch (err) {
      setStatus("Error submitting the form. Please try again.");
    }

    setLoading(false);
  };

  return (
    <div className="min-h-screen bg-[#0b1120] text-white px-4 py-10">
      <div className="max-w-2xl mx-auto bg-[#1e293b] p-6 rounded-xl shadow-lg">
        <h2 className="text-3xl font-bold mb-6 text-center">Contact Support</h2>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid md:grid-cols-2 gap-4">
            <input
              type="text"
              name="name"
              placeholder="Name*"
              value={form.name}
              onChange={handleChange}
              className="p-3 rounded bg-[#0f172a] border border-[#334155] text-white"
              required
            />
            <input
              type="email"
              name="email"
              placeholder="Email*"
              value={form.email}
              onChange={handleChange}
              className="p-3 rounded bg-[#0f172a] border border-[#334155] text-white"
              required
            />
          </div>

          <input
            type="text"
            name="subject"
            placeholder="Subject"
            value={form.subject}
            onChange={handleChange}
            className="w-full p-3 rounded bg-[#0f172a] border border-[#334155] text-white"
          />

          <div className="grid md:grid-cols-2 gap-4">
            <select
              name="category"
              value={form.category}
              onChange={handleChange}
              className="p-3 rounded bg-[#0f172a] border border-[#334155] text-white"
            >
              <option>General Issue</option>
              <option>Account</option>
              <option>Billing</option>
              <option>Model Application</option>
              <option>Bug</option>
              <option>Other</option>
            </select>

            <select
              name="urgency"
              value={form.urgency}
              onChange={handleChange}
              className="p-3 rounded bg-[#0f172a] border border-[#334155] text-white"
            >
              <option>Low</option>
              <option>Normal</option>
              <option>High</option>
            </select>
          </div>

          <textarea
            name="message"
            placeholder="Message*"
            value={form.message}
            onChange={handleChange}
            className="w-full h-32 p-3 rounded bg-[#0f172a] border border-[#334155] text-white"
            required
          />

          <div>
            <label className="text-sm mb-1 block text-gray-300">
              Attach File (optional)
            </label>
            <input
              type="file"
              name="attachment"
              accept="image/*,.pdf"
              onChange={handleChange}
              className="text-white"
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded w-full mt-4 transition"
          >
            {loading ? "Sending..." : "Send Message →"}
          </button>
        </form>

        {status && (
          <div className="mt-4 text-center text-sm text-green-400">
            {status}
          </div>
        )}

        <div className="text-center mt-6">
          <Link
            to="/support/faqs"
            className="text-blue-400 hover:underline text-sm"
          >
            ← Back to FAQ
          </Link>
        </div>
      </div>
    </div>
  );
}
