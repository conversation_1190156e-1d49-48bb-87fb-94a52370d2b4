import Agency from "../models/agency.js";
import bcrypt from "bcryptjs";
import cloudinary from "cloudinary";

// Only allow these fields to be updated
const ALLOWED_PROFILE_FIELDS = [
  "firstName",
  "lastName",
  "agencyName",
  "username",
  "agencyEmail",
  "agencyPhone",
  "profilePhoto",
  "timezone"
];

export const updateAgencyProfile = async (req, res) => {
  try {
    const updates = {};
    for (const key of ALLOWED_PROFILE_FIELDS) {
      if (req.body[key] !== undefined) {
        updates[key] = req.body[key];
      }
    }
    const agency = await Agency.findByIdAndUpdate(req.user._id, updates, { new: true });
    if (!agency) return res.status(404).json({ message: "Agency not found" });
    res.json({ agency });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

export const changeAgencyPassword = async (req, res) => {
  try {
    const { oldPassword, newPassword } = req.body;
    if (!oldPassword || !newPassword) {
      return res.status(400).json({ message: "Old and new password required" });
    }
    const agency = await Agency.findById(req.user._id);
    console.log("Agency old password:", agency.password);
    if (!agency) return res.status(404).json({ message: "Agency not found" });
    const isMatch = await bcrypt.compare(oldPassword, agency.password);
    if (!isMatch) return res.status(400).json({ message: "Old password incorrect" });
    const hashed = await bcrypt.hash(newPassword, 10);
    console.log("Agency new password:", hashed);
    agency.password = hashed;
    await agency.save();
    res.json({ message: "Password updated successfully" });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

export const uploadAgencyAvatar = async (req, res) => {
  try {
    // req.file.path is available via multer
    if (!req.file) return res.status(400).json({ message: "No file uploaded" });
    const result = await cloudinary.v2.uploader.upload(req.file.path, {
      folder: "agency_avatars",
      public_id: req.user._id,
      overwrite: true,
    });
    const agency = await Agency.findByIdAndUpdate(
      req.user._id,
      { profilePhoto: result.secure_url },
      { new: true }
    );
    res.json({ profilePhoto: result.secure_url, agency });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};
