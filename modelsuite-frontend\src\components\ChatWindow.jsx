import {
  useState,
  useRef,
  useEffect,
  useCallback,
  useMemo,
  useId,
} from "react";
import {
  Send,
  Loader2,
  Smile,
  ChevronLeft,
  Info,
  Loader,
  ChevronDown,
} from "lucide-react";
import Picker from "emoji-picker-react";
import { useDispatch, useSelector } from "react-redux";
import { formateTime } from "../utils/functions";
import TypingIndicator from "./ui/TypingIndicator";
import socket from "../utils/socket";
import {
  addNewDmMessage,
  addNewReaction,
  addOlderMessages,
  removeFailedMessage,
  removeReaction,
  setMessageSeen,
  updateUploadForFile,
} from "../globalstate/dmSlice";
import { ObjectId } from "bson";
import ChatMessage from "./ui/ChatMessage";
import axios from "axios";
import { throttle } from "lodash";
import { MessageContextMenu } from "./ui/MessageContextMenu";
import ChatSender from "./ui/ChatSender";
import MessageList from "./ui/MessageList";

const ChatWindow = ({ type, convoId, onBack }) => {
  const user = JSON.parse(localStorage.getItem("auth"))?.user;
  const dispatch = useDispatch();
  let lastDate = null;
  const conversation = useSelector((state) =>
    state[type]?.conversations?.find((c) => c.convoId == convoId),
  );

  // Memoize messages to prevent unnecessary recalculations
  const messages = useMemo(() => {
    return conversation?.messages || [];
  }, [conversation?.messages, conversation?.convoId]); // More specific deps

  // Memoize members
  const members = useMemo(
    () => conversation?.members || [],
    [conversation?.members],
  );
  const dmOpponent = members.find((m) => m.userId !== user._id);

  // State & refs
  const [expandedMessages, setExpandedMessages] = useState({});
  const [showDetails, setShowDetails] = useState(false);
  const messagesEndRef = useRef(null);
  const isNearBottomRef = useRef(true);
  const [isNearBottom, setIsNearBottom] = useState(true);
  // Global audio tracker
  const activeAudios = new Set();

  const isLoading = false;
  const [isFetchingMore, setIsFetchingMore] = useState(false);
  const hasMore = conversation?.hasMore;
  const containerRef = useRef(null);
  const baseURL = import.meta.env.VITE_API_BASE_URL;
  const token = JSON.parse(localStorage.getItem("auth"))?.token;
  const [contextMenu, setContextMenu] = useState({
    isOpen: false,
    message: null,
    isOwn: null,
  });
  const [ReplyData, setReplyData] = useState(null);
  const [longPressTimer, setLongPressTimer] = useState(null);
  const messageRef = useRef(null);
  const [attachments, setAttachments] = useState([]);
  const [previewAttachmentsIds, setPreviewAttachmentsIds] = useState([]);

  // New refs for preventing duplicates and managing scroll
  const lastFetchTimestamp = useRef(null);
  const isScrollingProgrammatically = useRef(false);
  const fetchCooldown = useRef(false);
  const scrollStateRef = useRef();

  // Enhanced fetchOlderDmMessages with duplicate prevention and smooth scrolling
  const fetchOlderDmMessages = useCallback(async () => {
    if (
      !containerRef.current ||
      isFetchingMore ||
      !messages.length ||
      !hasMore
    ) {
      return;
    }

    // Prevent rapid successive calls
    if (fetchCooldown.current) return;
    fetchCooldown.current = true;
    setTimeout(() => {
      fetchCooldown.current = false;
    }, 1000);

    const el = containerRef.current;
    const oldestMessage = messages[0];
    const oldestTimestamp = new Date(oldestMessage.createdAt).toISOString();

    // Prevent duplicate requests with same timestamp
    if (lastFetchTimestamp.current === oldestTimestamp) {
      return;
    }
    lastFetchTimestamp.current = oldestTimestamp;

    // Step 1: Show loader immediately
    setIsFetchingMore(true);

    // Step 2: Wait for loader to render, then calculate positions
    await new Promise((resolve) => {
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          // Now loader is rendered and included in scroll calculations
          const prevScrollHeight = el.scrollHeight;
          const prevScrollTop = el.scrollTop;

          // Store these values for later use
          scrollStateRef.current = {
            prevScrollHeight,
            prevScrollTop,
          };

          resolve();
        });
      });
    });

    // Temporarily disable interactions and smooth scrolling
    el.style.pointerEvents = "none";
    el.style.touchAction = "none";
    el.style.scrollBehavior = "auto";
    isScrollingProgrammatically.current = true;

    try {
      const res = await axios.get(
        `${baseURL}/messanger/dm/fetchOlderDmMessages?convoId=${convoId}&oldestTimestamp=${oldestTimestamp}`,
        {
          headers: { Authorization: `Bearer ${token}` },
          timeout: 10000,
        },
      );

      const { messages: newMessages, hasMore: newHasMore } = res.data;

      if (newMessages && newMessages.length > 0) {
        // Client-side duplicate check as backup
        const existingMessageIds = new Set(
          messages.map((msg) => msg.id || msg._id),
        );
        const uniqueNewMessages = newMessages.filter(
          (msg) => !existingMessageIds.has(msg.id || msg._id),
        );

        if (uniqueNewMessages.length > 0) {
          // Dispatch new messages
          dispatch(
            addOlderMessages({
              convoId,
              messages: uniqueNewMessages,
              hasMore: newHasMore,
            }),
          );

          // Wait for DOM updates with new messages (loader will disappear)
          await new Promise((resolve) => {
            requestAnimationFrame(() => {
              const { prevScrollHeight, prevScrollTop } =
                scrollStateRef.current;
              const newScrollHeight = el.scrollHeight;
              const heightDifference = newScrollHeight - prevScrollHeight;

              // Maintain visual scroll position
              const newScrollTop = prevScrollTop + heightDifference;
              el.scrollTop = Math.max(newScrollTop, 50); // Keep some buffer from top
              resolve();
            });
          });
        }
      }
    } catch (err) {
      console.error("Failed to fetch older messages:", err);
      // Reset timestamp on error to allow retry
      lastFetchTimestamp.current = null;
    } finally {
      // Always restore interaction state
      el.style.pointerEvents = "";
      el.style.touchAction = "";
      el.style.scrollBehavior = "smooth";
      isScrollingProgrammatically.current = false;
      setIsFetchingMore(false);
    }
  }, [convoId, token, messages, hasMore, isFetchingMore, dispatch, baseURL]);

  // Enhanced scroll handler with better threshold detection
  // Replace isNearBottom state with ref

  const handleScroll = useCallback(
    throttle((e) => {
      if (!containerRef.current) return;

      const el = containerRef.current;
      const { scrollTop, scrollHeight, clientHeight } = el;

      // Use ref instead of state to avoid re-renders
      const nearBottom = scrollHeight - scrollTop - clientHeight < 100;

      if (isNearBottomRef.current !== nearBottom) {
        isNearBottomRef.current = nearBottom;
        setIsNearBottom(nearBottom);
      }

      const nearTop = scrollTop < 10;
      if (nearTop && hasMore && !isFetchingMore) {
        fetchOlderDmMessages();
      }
    }, 150),
    [hasMore, isFetchingMore], // Remove fetchOlderDmMessages from deps
  );

  const checkScrollPosition = (e) => {
    handleScroll(e);
  };

  const handleRightClick = (e, message, isOwn) => {
    e.preventDefault();

    setContextMenu({
      isOpen: true,
      message,
      isOwn,
    });
  };
  const handleReactionsClick = (e, message, isOwn) => {
    e.preventDefault();

    setContextMenu({
      isOpen: true,
      message,
      isOwn,
      type: "reactionsList",
    });
  };

  const handleTouchStart = (e, message, isOwn) => {
    const timer = setTimeout(() => {
      setContextMenu({
        isOpen: true,
        message,
        isOwn,
      });

      // Add haptic feedback if supported
      if (navigator.vibrate) {
        navigator.vibrate(50);
      }
    }, 500); // 500ms long press

    setLongPressTimer(timer);
  };

  const handleTouchEnd = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
  };

  const handleTouchMove = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
  };

  const closeContextMenu = () => {
    setContextMenu({
      isOpen: false,
      message: null,
      isOwn: null,
    });
  };

  const uploadFilesToCloudinary = async (attachments, messageId, convoId) => {
    const formData = new FormData();

    attachments.forEach((attachment) => {
      formData.append("files", attachment.file);
      formData.append("fileIds", attachment.fileId);
    });

    try {
      const response = await fetch(
        `${baseURL}/messanger/chat/attachments/upload`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Message-Id": messageId,
          },
          body: formData,
        },
      );

      if (!response.ok) {
        attachments.forEach((a) => {
          dispatch(
            updateUploadForFile({
              convoId,
              messageId,
              attachmentData: {
                id: a.fileId,
                status: "error",
                progress: 100,
                cloudinaryUrl: null,
                publicId: null,
              },
            }),
          );
        });
        throw new Error("Upload failed");
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = "";
      let finalResults = [];

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");
        buffer = lines.pop(); // Keep incomplete chunk

        for (const line of lines) {
          if (!line.trim()) continue;

          try {
            const data = JSON.parse(line);

            if (data.type === "progress") {
              // optional: handle progress
            } else if (data.type === "complete") {
              setAttachments((prev) =>
                prev.filter((a) => a.fileId !== data.fileId),
              );
              dispatch(
                updateUploadForFile({
                  convoId,
                  messageId,
                  attachmentData: {
                    fileId: data.result.fileId,
                    status: "uploaded",
                    progress: data.progress,
                    duration: data.result.duration || 0,
                    cloudinaryUrl: data.result.url,
                    publicId: data.result.cloudinaryId,
                  },
                }),
              );
            } else if (data.type === "error") {
              dispatch(
                updateUploadForFile({
                  convoId,
                  messageId,
                  attachmentData: {
                    fileId: data.fileId,
                    status: "error",
                    progress: 100,
                    cloudinaryUrl: null,
                    publicId: null,
                  },
                }),
              );
            } else if (data.type === "finished") {
              finalResults = data.results || [];
              return data.results || [];
            }
          } catch (e) {
            console.error("Failed to parse upload event:", e, line);
          }
        }
      }
      return finalResults;
    } catch (error) {
      console.error("Upload error:", error);
      throw error;
    }
  };

  const handleSendMessage = async ({ messageId = null, text, setText }) => {
    if (!text.trim() && attachments.length === 0) return;

    if (type === "dm") {
      const opponent = members.find((m) => m.userId !== user._id);
      const tempId = new ObjectId().toString();

      let attachmentData;
      let messageData;

      if (messageId) {
        const existingMessage = messages.find((m) => m._id === messageId);
        messageData = existingMessage;
        attachmentData = existingMessage.attachments;
        dispatch(removeFailedMessage(convoId, messageId));
      } else {
        // Prepare attachments data for Redux (without file objects)
        const filteredAttachments = attachments.filter((a) =>
          previewAttachmentsIds.includes(a.fileId),
        );
        attachmentData = filteredAttachments.map((att) => ({
          fileId: att.fileId,
          file: att.file,
          type: att.type,
          name: att.name,
          size: att.size,
          width: att.naturalWidth || null,
          height: att.naturalHeight || null,
          localUrl: att.localUrl,
          thumbnailUrl: att.thumbnailUrl,
          mimeType: att.mimeType,
          status: "pending",
          progress: 0,
          cloudinaryUrl: null,
          error: null,
        }));

        const SafeAttachmentData = attachmentData.map((att) => {
          const { file, ...safeAtt } = att;
          return safeAtt;
        });

        messageData = {
          _id: tempId,
          convoId: convoId,
          senderId: user._id,
          receiverId: opponent.userId,
          senderName: user.fullName,
          text: text.trim(),
          type:
            attachments.length === 0
              ? "textonly"
              : attachments.length > 0 && !!text
                ? "mixed"
                : "attachmentonly",
          status: null,
          attachments: SafeAttachmentData,
          reactions: [],
          replyTo: ReplyData?._id || null,
          createdAt: new Date().toISOString(),
        };
      }

      setPreviewAttachmentsIds([]);
      // Add message to Redux immediately

      dispatch(addNewDmMessage(messageData));

      requestAnimationFrame(() => {
        scrollToBottom();
      });

      // Clear input and attachments
      setText("");
      const pendingAttachments = attachmentData.filter(
        (a) => a.status === "pending" || a.status === "error",
      );
      const compeletedAttachments = attachmentData.filter(
        (a) => a.status === "uploaded",
      );
      setAttachments([]);
      setReplyData(null);

      try {
        if (pendingAttachments.length > 0) {
          // Upload files to Cloudinary
          const uploadedAttachments = await uploadFilesToCloudinary(
            pendingAttachments,
            tempId,
            convoId,
          );

          const allAttachments = [
            ...compeletedAttachments,
            ...uploadedAttachments,
          ];

          // Update message with uploaded attachment URLs
          const updatedMessageData = {
            ...messageData,
            status: null,
            attachments: allAttachments.map((att) => ({
              fileId: att.fileId,
              type: att.resourceType,
              name: att.originalName,
              size: att.size,
              mimeType: att.mimeType,
              cloudinaryUrl: att.url,
              publicId: att.cloudinaryId,
              duration: att.duration,
              width: att.width,
              height: att.height,
              status: "uploaded",
              progress: 100,
            })),
          };

          // Update Redux with final data

          // Emit socket event with uploaded files
          socket.emit("dm:send_message", updatedMessageData);
        } else {
          // No attachments, just emit the message
          socket.emit("dm:send_message", messageData);
        }
      } catch (error) {
        console.error("Message send failed:", error);

        // Update message status to error
      }
    }
  };

  const handleSendAudio = async (audioAttachment) => {
    if (type === "dm") {
      try {
        const opponent = members.find((m) => m.userId !== user._id);
        const tempId = new ObjectId().toString();
        const { file, ...safeAudioAtt } = audioAttachment;

        const AudiomessageData = {
          _id: tempId,
          convoId: convoId,
          senderId: user._id,
          receiverId: opponent.userId,
          senderName: user.fullName,
          text: "",
          type: "attachmentonly",
          status: null,
          attachments: [safeAudioAtt],
          reactions: [],
          replyTo: ReplyData?._id || null,
          createdAt: new Date().toISOString(),
        };

        setReplyData(null);
        dispatch(addNewDmMessage(AudiomessageData));

        requestAnimationFrame(() => {
          scrollToBottom();
        });

        const uploadedAudio = await uploadFilesToCloudinary(
          [audioAttachment],
          tempId,
          convoId,
        );

        const updatedAudioMessageData = {
          ...AudiomessageData,
          status: null,
          attachments: uploadedAudio.map((att) => ({
            fileId: att.fileId,
            type: att.resourceType,
            name: att.originalName,
            size: att.size,
            mimeType: att.mimeType,
            cloudinaryUrl: att.url,
            publicId: att.cloudinaryId,
            duration: att.duration,
            width: null,
            height: null,
            status: "uploaded",
            progress: 100,
          })),
        };

        socket.emit("dm:send_message", updatedAudioMessageData);
      } catch (err) {
        console.log(err);
      }
    }
  };

  const handleSendGif = (file) => {
    const tempId = new ObjectId().toString();
    const fileId = new ObjectId().toString();
    const opponent = members.find((m) => m.userId !== user._id);

    if (type == "dm") {
      const messageData = {
        _id: tempId,
        convoId: convoId,
        senderId: user._id,
        receiverId: opponent.userId,
        senderName: user.fullName,
        text: "",
        type: "attachmentonly",
        status: null,
        attachments: [
          {
            fileId: fileId,
            type: file.type,
            name: file.title,
            size: file.size,
            mimeType: file.type,
            cloudinaryUrl: file.url,
            publicId: file.id,
            width: file.width,
            height: file.height,
            status: "uploaded",
            progress: 100,
          },
        ],
        reactions: [],
        replyTo: ReplyData?._id || null,
        createdAt: new Date().toISOString(),
      };

      setReplyData(null);
      dispatch(addNewDmMessage(messageData));

      requestAnimationFrame(() => {
        scrollToBottom();
      });

      socket.emit("dm:send_message", messageData);
    }
  };

  const handleReact = (messageId, emoji) => {
    const userId = user._id;
    const avatar = members.find((m) => m.userId === user._id).avatar;
    const fullName = user.fullName ? user.fullName : user.agencyName;
    const message = messages.find((m) => m._id === messageId);

    // Create new reactions array
    const reactions = message.reactions.filter((r) => r.userId !== userId);
    reactions.push({ userId, avatar, fullName, emoji });

    // Optimistic update
    dispatch(addNewReaction({ convoId, messageId, reactions }));

    // Emit to server
    socket.emit("dm:add_reaction", {
      convoId,
      messageId,
      data: { userId, avatar, fullName, emoji },
    });
  };

  const handleRemoveReaction = (userId) => {
    const messageId = contextMenu.message._id;

    // Optimistic update
    dispatch(removeReaction({ convoId, messageId, userId }));

    // Emit to server
    socket.emit("dm:remove_reaction", {
      convoId,
      messageId,
      userId,
    });

    closeContextMenu();
  };

  const handleReply = (message) => {
    setReplyData(message);
  };

  const handleEdit = (messageId) => {
    // Implement edit logic
  };

  const handlePin = (messageId) => {
    console.log("Pin:", messageId);
    // Implement pin logic
  };

  const handleCopyText = (messageId) => {
    console.log("Copy text:", messageId);
    // Implement copy logic
    const message = messages.find((msg) => msg._id === messageId);
    if (message && navigator.clipboard) {
      navigator.clipboard.writeText(message.content || message.text);
    }
  };

  const handleDelete = (messageId) => {
    console.log("Delete:", messageId);
    // Implement delete logic
  };

  // Helper function to get formatted date label
  const getDateLabel = (msgDate, today, yesterday) => {
    if (msgDate.toDateString() === today.toDateString()) {
      return "Today";
    } else if (msgDate.toDateString() === yesterday.toDateString()) {
      return "Yesterday";
    } else {
      return msgDate.toLocaleDateString(undefined, {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    }
  };

  // Enhanced scroll to bottom that respects user intent
  const scrollToBottom = () => {
    const container = containerRef.current;
    setTimeout(() => {
      if (container) {
        // Fallback to scrollHeight
        container.scrollTop = container.scrollHeight;

        // Still attempt scrollIntoView for smoother animation
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            messagesEndRef.current?.scrollIntoView({
              behavior: "auto",
              block: "end",
            });
          });
        });
      }
    }, 50);
  };

  // Auto-scroll effect for new messages (only when user is near bottom)
  useEffect(() => {
    if (isNearBottomRef.current && messages.length > 0) {
      setTimeout(() => {
        requestAnimationFrame(() => {
          scrollToBottom();
        });
      }, 80);
    }
  }, [messages.length]);

  // Reset fetch timestamp when conversation changes
  useEffect(() => {
    lastFetchTimestamp.current = null;
    fetchCooldown.current = false;
  }, [convoId]);

  const toggleReadMore = (index) =>
    setExpandedMessages((prev) => ({ ...prev, [index]: !prev[index] }));

  const handleMessageSeen = (msg) => {
    if (!msg || msg.senderId === user._id || msg.status === "seen") return;

    socket.emit("dm:message_seen", {
      convoId: convoId,
      messageId: msg._id,
    });

    dispatch(setMessageSeen({ convoId, messageId: msg._id }));
  };

  // Header components (keeping original)
  const DMHeader = () => {
    const opponent = members.find((m) => m.userId !== user._id);
    const chatLogo = opponent.avatar;
    const chatName = opponent.name;
    const status = opponent.status;
    return (
      <div
        className="flex items-center gap-3 px-8 py-4 bg-[#232e3c] border-b border-[#232e3c] relative cursor-pointer"
        onClick={() => setShowDetails(true)}
      >
        <button
          className="mr-4 text-gray-400 hover:text-white"
          onClick={(e) => {
            e.stopPropagation();
            onBack();
          }}
        >
          <ChevronLeft className="w-7 h-7" />
        </button>
        <div className="w-10 h-10 rounded-full bg-gray-700 overflow-hidden flex justify-center items-center">
          {chatLogo ? (
            <img
              src={chatLogo}
              alt="chat logo"
              className="w-full h-full object-cover"
            />
          ) : (
            chatName?.[0]?.toUpperCase() || "U"
          )}
        </div>
        <div className="flex flex-col">
          <div className="font-semibold text-white text-lg">{chatName}</div>
          <p className="text-xs text-gray-400 mt-0">
            {status.isTyping ? (
              <TypingIndicator />
            ) : status.isOnline ? (
              <span className="text-green-500 tracking-wider font-bold">
                {" "}
                ● online
              </span>
            ) : status.lastOnline ? (
              `Last online: ${formateTime(status.lastOnline)}`
            ) : (
              "Direct Messages"
            )}
          </p>
        </div>
        <Info className="absolute right-8 top-1/2 -translate-y-1/2 w-6 h-6 text-blue-400" />
      </div>
    );
  };

  const GroupHeader = () => {
    return (
      <div
        className="flex items-center gap-3 px-8 py-5 bg-[#232e3c] border-b border-[#232e3c] relative cursor-pointer"
        onClick={() => setShowDetails(true)}
      >
        <button
          className="mr-4 text-gray-400 hover:text-white"
          onClick={(e) => {
            e.stopPropagation();
            onBack();
          }}
        >
          <ChevronLeft className="w-7 h-7" />
        </button>
        <div className="w-10 h-10 rounded-full bg-gray-700 overflow-hidden flex justify-center items-center">
          {/* chatLogo and chatName are not defined in this scope - need to be passed from props or state */}
          G
        </div>
        <div className="flex flex-col">
          <div className="font-semibold text-white text-lg">Group Chat</div>
          <p className="text-xs text-gray-400 mt-1">Group Chat</p>
        </div>
        <Info className="absolute right-8 top-1/2 -translate-y-1/2 w-6 h-6 text-blue-400" />
      </div>
    );
  };

  const ChannelHeader = () => {
    return (
      <div
        className="flex items-center gap-3 px-8 py-5 bg-[#232e3c] border-b border-[#232e3c] relative cursor-pointer"
        onClick={() => setShowDetails(true)}
      >
        <button
          className="mr-4 text-gray-400 hover:text-white"
          onClick={(e) => {
            e.stopPropagation();
            onBack();
          }}
        >
          <ChevronLeft className="w-7 h-7" />
        </button>
        <div className="w-10 h-10 rounded-full bg-gray-700 overflow-hidden flex justify-center items-center">
          C
        </div>
        <div className="flex flex-col">
          <div className="font-semibold text-white text-lg">Channel</div>
          <p className="text-xs text-gray-400 mt-1">Channel</p>
        </div>
        <Info className="absolute right-8 top-1/2 -translate-y-1/2 w-6 h-6 text-blue-400" />
      </div>
    );
  };

  // Info slider components (keeping original structure)
  const DMInfoSlider = () => {
    const opponent = members.find((m) => m.userId !== user._id);
    const avatar = opponent.avatar;
    const name = opponent.name;
    const status = opponent.status;
    const createdAtFormatted = formateTime(conversation.createdAt);

    return (
      <div
        className={`absolute top-0 right-0 h-full w-[400px] bg-[#181f29] shadow-2xl z-40 transition-transform duration-300 ${
          showDetails ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div className="flex items-center justify-between px-6 py-5 border-b border-gray-800">
          <div className="text-xl font-bold text-white">DM Details</div>
          <button
            className="text-gray-400 hover:text-white"
            onClick={() => setShowDetails(false)}
          >
            <ChevronLeft className="w-7 h-7 rotate-180" />
          </button>
        </div>

        <div className="p-6 flex flex-col items-center text-gray-300">
          <div className="w-24 h-24 rounded-full overflow-hidden bg-gray-700 mb-4 shadow-md">
            {avatar ? (
              <img
                src={avatar}
                alt={name}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="flex items-center justify-center h-full text-4xl font-bold text-white">
                {name?.[0]?.toUpperCase() || "U"}
              </div>
            )}
          </div>

          <div className="text-xl font-semibold text-white">{name}</div>

          <div className="text-xs text-gray-400 mt-1">
            {status?.isTyping ? (
              <TypingIndicator />
            ) : status?.isOnline ? (
              <span>Online</span>
            ) : status?.lastOnline ? (
              `Last online: ${formateTime(status.lastOnline)}`
            ) : (
              "Offline"
            )}
          </div>

          <div className="mt-6 text-sm text-gray-400 text-center">
            Conversation created on:
            <div className="text-white font-medium">{createdAtFormatted}</div>
          </div>
        </div>
      </div>
    );
  };

  const GroupInfoSlider = () => {
    return (
      <div
        className={`absolute top-0 right-0 h-full w-[400px] bg-[#181f29] shadow-2xl z-40 transition-transform duration-300 ${
          showDetails ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div className="flex items-center justify-between px-6 py-5 border-b border-gray-800">
          <div className="text-xl font-bold text-white">Group Details</div>
          <button
            className="text-gray-400 hover:text-white"
            onClick={() => setShowDetails(false)}
          >
            <ChevronLeft className="w-7 h-7 rotate-180" />
          </button>
        </div>
        <div className="p-6 text-gray-400">
          Placeholder group info content here...
        </div>
      </div>
    );
  };

  const ChannelInfoSlider = () => {
    return (
      <div
        className={`absolute top-0 right-0 h-full w-[400px] bg-[#181f29] shadow-2xl z-40 transition-transform duration-300 ${
          showDetails ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div className="flex items-center justify-between px-6 py-5 border-b border-gray-800">
          <div className="text-xl font-bold text-white">Channel Details</div>
          <button
            className="text-gray-400 hover:text-white"
            onClick={() => setShowDetails(false)}
          >
            <ChevronLeft className="w-7 h-7 rotate-180" />
          </button>
        </div>
        <div className="p-6 text-gray-400">
          Placeholder channel info content here...
        </div>
      </div>
    );
  };

  const renderHeader = () => {
    switch (type) {
      case "dm":
        return <DMHeader />;
      case "group":
        return <GroupHeader />;
      case "channel":
        return <ChannelHeader />;
      default:
        return null;
    }
  };

  const renderInfoSlider = () => {
    switch (type) {
      case "dm":
        return <DMInfoSlider />;
      case "group":
        return <GroupInfoSlider />;
      case "channel":
        return <ChannelInfoSlider />;
      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col h-full  w-full bg-[#222e35] text-white  relative   overflow-hidden">
      {renderHeader()}
      {renderInfoSlider()}
      {/* Messages */}
      <div className="flex-1 px-0 py-0  h-max-[100%] scroll-smooth relative overflow-hidden   bg-contain bg-center  ">
        <div className="absolute inset-0 bg-[url(../public\chat-bg-dark.png)] bg-cover bg-center opacity-5 pointer-events-none z-0" />
        <div
          className="flex overflow-y-auto  h-full scroll-smooth   custom-scrollbar flex-col gap-1  w-full max-w-full"
          onScroll={() => checkScrollPosition()}
          ref={containerRef}
          style={{ minHeight: 0 }}
        >
          {/* Loading indicator at top */}
          <MessageList
            messages={messages}
            user={user}
            type={type}
            hasMore={hasMore}
            expandedMessages={expandedMessages}
            toggleReadMore={toggleReadMore}
            handleMessageSeen={handleMessageSeen}
            handleRightClick={handleRightClick}
            handleSendMessage={handleSendMessage}
            handleTouchEnd={handleTouchEnd}
            handleTouchStart={handleTouchStart}
            handleTouchMove={handleTouchMove}
            messageRef={messageRef}
            isFetchingMore={isFetchingMore}
            activeAudios={activeAudios}
            handleReactionsClick={handleReactionsClick}
          />
          <MessageContextMenu
            isOpen={contextMenu.isOpen}
            onClose={closeContextMenu}
            message={contextMenu.message}
            isOwn={contextMenu.isOwn}
            containerRef={containerRef} // Pass the container reference
            // Context menu handlers
            onReact={handleReact}
            onReply={handleReply}
            onEdit={handleEdit}
            onPin={handlePin}
            onCopyText={handleCopyText}
            onDelete={handleDelete}
            handleRemoveReaction={handleRemoveReaction}
            type={contextMenu?.type || "messageOptions"}
          />
          <div ref={messagesEndRef} className="mt-3" />
        </div>
        {!isNearBottom && (
          <div
            className="scrolltobottom flex items-center justify-center absolute bottom-4 right-5 w-11 h-11 shadow-lg rounded-full bg-green-600 hover:bg-green-700 hover:cursor-pointer active:scale-95"
            onClick={() => scrollToBottom()}
          >
            <ChevronDown className="w-8 h-8 inline-block" />
          </div>
        )}
      </div>
      {/* Input */}
      <ChatSender
        chatType={type}
        convoId={convoId}
        members={members}
        ReplyData={ReplyData}
        setReplyData={setReplyData}
        attachments={attachments}
        setAttachments={setAttachments}
        previewAttachmentsIds={previewAttachmentsIds}
        setPreviewAttachmentsIds={setPreviewAttachmentsIds}
        handleSendMessage={handleSendMessage}
        handleSendGif={handleSendGif}
        handleSendAudio={handleSendAudio}
      />
    </div>
  );
};

export default ChatWindow;
