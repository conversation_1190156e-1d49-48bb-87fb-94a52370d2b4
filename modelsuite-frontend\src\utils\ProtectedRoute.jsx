import { Navigate, Outlet, useLocation } from "react-router-dom";

const ProtectedRoute = ({ allowedRole }) => {
  const authData = JSON.parse(localStorage.getItem("auth"));
  const token = authData?.token;
  const user = authData?.user;
  const location = useLocation();

  // Not logged in → redirect to login
  if (!token || !user) {
    return <Navigate to="/agency/login" state={{ from: location }} replace />;
  }

  // Logged in but wrong role → redirect to correct dashboard
  if (user.role !== allowedRole) {
    const redirectMap = {
      agency: "/agency/dashboard",
      model: "/model/dashboard",
      employee: "/employee/dashboard",
    };
    return <Navigate to={redirectMap[user.role] || "/"} replace />;
  }

  // ✅ Authorized → render route
  return <Outlet />;
};

export default ProtectedRoute;
