import express from "express";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import {
  disconnectTikTok,
  getTikTokProfile,
  getTikTokStats,
  handleTikTokCallback,
  isTikTokConnected,
} from "../../controllers/socialMedia/tiktokController.js";

const router = express.Router();

router.get("/callback", handleTikTokCallback);
router.get("/profile", verifyToken, getTikTokProfile);
router.get("/stats", verifyToken, getTikTokStats);
router.delete("/disconnect", verifyToken, disconnectTikTok);
router.get("/is-connected", verifyToken, isTikTokConnected);

export default router;
