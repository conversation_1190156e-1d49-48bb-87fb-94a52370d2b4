import ModelUser from "../models/model.js";
import Agency from "../models/agency.js";
import crypto from "crypto";
import nodemailer from "nodemailer";
const options = {
  httpOnly: true,
  secure: true,
  sameSite: "strict", // or 'lax' for usability
  maxAge: 1000 * 60 * 60, // 1 hour
};

const sendMagicLink = async (email, magicLinkUrl) => {
  const transporter = nodemailer.createTransport({
    service: "gmail",
    host: "smtp.gmail.com",
    port: 465,
    secure: true,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  });
  const mailOptions = {
    from: process.env.EMAIL_USER,
    to: email,
    subject: "ModelSuite-ai Magic Link",
    text: `Hi!
      Click this link to login to ModelSuite-ai : ${magicLinkUrl}
      This link expires in 15 minutes.`,
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log(`Magic Link has been sent to ${email}.`);
  } catch (error) {
    console.error("Email sending failed: ", error);
    return res.status(500).json({ message: "Magic Link could not be sent." });
  }
};

export const magicLogin = async (req, res) => {
  try {
    const { email, role } = req.body;

    if (!email) return res.status(404).json("Email is required.");

    if (role === "model") {
      const user = await ModelUser.findOne({ email });
      if (!user)
        return res.status(401).json({ message: "Unauthorized access." });

      const token = crypto.randomBytes(32).toString("hex");
      const expiresAt = new Date(Date.now() + 15 * 60 * 1000); //expires in 15 minutes

      await ModelUser.findByIdAndUpdate(user._id, {
        magicLinkToken: token,
        magicLinkExpiresAt: expiresAt,
        magicLinkUsed: false,
      });

      const magicLinkUrl = `${process.env.FRONTEND_HOSTING_BASEURL}/magic-verify?token=${token}&role=model`;
      await sendMagicLink(email, magicLinkUrl);

      return res
        .status(200)
        .json({ message: "Magic-Link has been sent successfully." });
    } else {
      const user = await Agency.findOne({ agencyEmail: email });
      if (user) {
        const token = crypto.randomBytes(32).toString("hex");
        const expiresAt = new Date(Date.now() + 15 * 60 * 1000); //expires in 15 minutes

        await Agency.findByIdAndUpdate(user._id, {
          magicLinkToken: token,
          magicLinkExpiresAt: expiresAt,
          magicLinkUsed: false,
        });

        const magicLinkUrl = `${process.env.FRONTEND_HOSTING_BASEURL}/magic-verify?token=${token}&role=agency`;
        await sendMagicLink(email, magicLinkUrl);

        return res
          .status(200)
          .json({ message: "Magic-Link has been sent successfully." });
      } else {
        return res
          .status(401)
          .json({ message: "User not found. Please register." });
      }
    }
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: "Server error" });
  }
};

export const magicVerify = async (req, res) => {
  try {
    const { role, token } = req.query;

    if (!token || !role) {
      return res.status(401).json({ message: "Unauthorized access." });
    }
    let user;
    if (role === "model") {
      user = await ModelUser.findOne({
        magicLinkToken: token,
        magicLinkUsed: false,
        magicLinkExpiresAt: { $gt: new Date() },
      });
    } else if (role === "agency") {
      user = await Agency.findOne({
        magicLinkToken: token,
        magicLinkUsed: false,
        magicLinkExpiresAt: { $gt: new Date() },
      });
    }
    if (user) {
      const refreshToken = user.generateRefreshToken();
      const accessToken = user.generateAccessToken();

      user.loginRefreshToken = refreshToken;
      user.magicLinkUsed = true; // Mark as used
      await user.save({ validateBeforeSave: false });

      const safeUser = {
        _id: user._id,
        fullName: user.fullName,
        username: user.username,
        role: user.role,
      };

      res
        .cookie("accessToken", accessToken, options)
        .cookie("refreshToken", refreshToken, options);

      return res.status(200).json({
        message: "Login successful",
        user: safeUser,
        token: accessToken,
      });
    } else {
      return res.status(401).json({ message: "Unauthorized access." });
    }
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: "Server error" });
  }
};
