import puppeteer from "puppeteer";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

// Get __dirname equivalent in ES modules
const __dirname = path.dirname(fileURLToPath(import.meta.url));

class PDFGenerator {
  constructor() {
    this.browser = null;
    this.pageQueue = Promise.resolve(); // For concurrency control
  }

  async initialize() {
    try {
      if (this.browser) return; // Already initialized

      this.browser = await puppeteer.launch({
        executablePath: process.env.PUPPETEER_EXECUTABLE_PATH || undefined,
        headless: "new",
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-dev-shm-usage",
          "--disable-accelerated-2d-canvas",
          "--disable-gpu",
          "--no-first-run",
          "--no-zygote",
        ],
      });
      console.log("PDF Generator: Browser initialized successfully");
      console.log(
        "Using Chromium path:",
        process.env.PUPPETEER_EXECUTABLE_PATH || "default",
      );
    } catch (error) {
      console.error("PDF Generator: Failed to initialize browser:", error);
      throw error;
    }
  }

  async generateQuestionnaireReport(
    modelData,
    answers,
    templateData,
    options = {},
  ) {
    // Use a queue to ensure thread-safety for concurrent calls
    return this.pageQueue
      .then(async () => {
        let page = null;

        try {
          await this.initialize();

          page = await this.browser.newPage();

          // Set viewport to match chosen paper format
          const format = options.format || "A4";
          const dimensions = this.getFormatDimensions(format);

          await page.setViewport({
            width: dimensions.width,
            height: dimensions.height,
          });

          console.log(
            `PDF Generator: Generating ${format} report for model:`,
            modelData.fullName,
          );

          // Default contact information (can be overridden via options)
          const contactInfo = {
            email: options.email || "<EMAIL>",
            phone: options.phone || "+****************",
            website: options.website || "www.modelsuite.ai",
            ...options.contactInfo,
          };

          // Get logo
          const logoBase64 = await this.getLogoBase64(options.logoPath);

          // Define Inter font as base64 to avoid external dependencies
          const interFontBase64 = await this.getInterFontBase64();

          // Get timestamp for footer
          const timestamp = new Date().toLocaleString();

          // Create HTML content with improved structure and embedded font
          const htmlContent = this.generateHTML({
            logoBase64,
            modelData,
            answers,
            templateData,
            contactInfo,
            interFontBase64,
            timestamp,
            showPageNumbers: options.showPageNumbers !== false,
          });

          // Set content with timeout and wait for fonts
          await page.setContent(htmlContent, {
            waitUntil: "networkidle0",
            timeout: options.timeout || 30000,
          });

          // Wait for fonts to load
          await page.evaluateHandle("document.fonts.ready");

          // Generate PDF with dimensions based on format
          const pdf = await page.pdf({
            format,
            printBackground: true,
            margin: {
              top: "0",
              bottom: "0",
              left: "0",
              right: "0",
            },
            preferCSSPageSize: true,
            displayHeaderFooter: options.showPageNumbers !== false,
            headerTemplate: "<span></span>", // Empty header
            footerTemplate:
              options.showPageNumbers !== false
                ? '<div style="width: 100%; font-size: 10px; text-align: center; color: #999;">Page <span class="pageNumber"></span> of <span class="totalPages"></span></div>'
                : "<span></span>", // Empty footer if page numbers disabled
          });

          // Optionally save to file
          if (options.outputPath) {
            fs.writeFileSync(options.outputPath, pdf);
            console.log(`PDF Generator: Saved PDF to ${options.outputPath}`);
          }

          console.log("PDF Generator: Report generated successfully");
          return pdf;
        } catch (error) {
          console.error("PDF Generator: Error generating report:", error);
          throw error;
        } finally {
          if (page) await page.close();
        }
      })
      .catch((error) => {
        console.error("PDF Generator: Queue error:", error);
        throw error;
      });
  }

  generateHTML({
    logoBase64,
    modelData,
    answers,
    templateData,
    contactInfo,
    interFontBase64,
    timestamp,
    showPageNumbers,
  }) {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <title>ModelSuite.ai Questionnaire Report</title>
          <style>
            /* Embedded Inter font to avoid external dependencies */
            @font-face {
              font-family: 'Inter';
              font-style: normal;
              font-weight: 400;
              src: url(${interFontBase64}) format('woff2');
            }
            
            @font-face {
              font-family: 'Inter';
              font-style: normal;
              font-weight: 500;
              src: url(${interFontBase64}) format('woff2');
            }
            
            @font-face {
              font-family: 'Inter';
              font-style: normal;
              font-weight: 600;
              src: url(${interFontBase64}) format('woff2');
            }
            
            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
              font-family: 'Inter', system-ui, -apple-system, sans-serif;
            }

            body {
              min-height: 100vh;
              background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
              color: #ffffff;
              position: relative;
              line-height: 1.6;
              letter-spacing: 0.2px;
            }

            /* Curved background effect - more subtle */
            .background-curve {
              position: absolute;
              top: 0;
              right: 0;
              width: 100%;
              height: 100%;
              z-index: 0;
              opacity: 0.3;
            }

            .curve {
              position: absolute;
              top: 60%;
              right: -15%;
              width: 80%;
              height: 400px;
              background: linear-gradient(45deg, rgba(43, 57, 144, 0.3), rgba(102, 45, 145, 0.2));
              transform: rotate(-15deg);
              border-radius: 50% 50% 0 0;
            }

            /* Content layout */
            .container {
              position: relative;
              z-index: 1;
              padding: 40px;
              padding-bottom: 150px; /* Extra padding to ensure footer doesn't overlap */
              max-width: 1200px;
              margin: 0 auto;
            }

            /* Header section */
            .header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 50px;
              border-bottom: 1px solid rgba(255, 255, 255, 0.1);
              padding-bottom: 20px;
              page-break-after: avoid;
            }

            .logo-section {
              display: flex;
              align-items: center;
              gap: 15px;
            }

            .logo {
              width: 100px;
              height: 100px;
              border-radius: 12px;
              object-fit: contain;
            }

            .logo-fallback {
              width: 100px;
              height: 100px;
              background: linear-gradient(45deg, #64ffda, #2B3990);
              border-radius: 12px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 32px;
              font-weight: bold;
              color: white;
            }
            
            .logo-text {
              font-size: 24px;
              font-weight: 600;
              color: #ffffff;
              letter-spacing: 1px;
              text-transform: uppercase;
              background: linear-gradient(90deg, #ffffff, #64ffda);
              -webkit-background-clip: text;
              background-clip: text;
              -webkit-text-fill-color: transparent;
            }

            /* Content section */
            .content {
              background: rgba(255, 255, 255, 0.05);
              backdrop-filter: blur(10px);
              border: 1px solid rgba(255, 255, 255, 0.1);
              border-radius: 15px;
              padding: 30px;
              margin: 30px 0 80px 0;
              min-height: 400px;
            }

            /* Footer contact info */
            .contact-info {
              margin-top: 50px;
              display: flex;
              justify-content: space-around;
              padding: 20px 25px;
              background: rgba(255, 255, 255, 0.08);
              backdrop-filter: blur(5px);
              border-radius: 12px;
              border: 1px solid rgba(255, 255, 255, 0.15);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
              page-break-inside: avoid;
            }

            .contact-item {
              display: flex;
              align-items: center;
              gap: 12px;
              color: #ffffff;
              font-size: 15px;
              letter-spacing: 0.3px;
            }

            .contact-item svg {
              width: 22px;
              height: 22px;
              fill: #64ffda;
              opacity: 0.9;
            }

            /* Question and Answer styling */
            .question {
              margin-bottom: 30px;
              page-break-inside: avoid;
            }

            .question-text {
              font-weight: 600;
              color: #64ffda;
              margin-bottom: 12px;
              font-size: 16px;
              letter-spacing: 0.2px;
            }

            .answer {
              color: #ffffff;
              background: rgba(255, 255, 255, 0.08);
              padding: 16px;
              border-radius: 10px;
              line-height: 1.5;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            /* Model info section */
            .model-info {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              gap: 20px;
              margin-bottom: 40px;
              page-break-inside: avoid;
              page-break-after: avoid;
            }

            .info-item {
              background: rgba(255, 255, 255, 0.08);
              padding: 18px;
              border-radius: 12px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
              transition: transform 0.2s ease;
            }

            .info-label {
              font-size: 13px;
              color: #64ffda;
              margin-bottom: 6px;
              text-transform: uppercase;
              letter-spacing: 0.5px;
              font-weight: 500;
            }

            .info-value {
              font-size: 17px;
              font-weight: 500;
              letter-spacing: 0.3px;
            }

            .section {
              margin-bottom: 40px;
              page-break-inside: avoid;
              border-bottom: 1px solid rgba(255, 255, 255, 0.1);
              padding-bottom: 30px;
            }
            
            .section:last-child {
              border-bottom: none;
            }

            /* New section - prevent orphan section title */
            .section-break {
              page-break-before: always;
            }

            .section-title {
              color: #64ffda;
              margin-bottom: 24px;
              font-size: 20px;
              font-weight: 600;
              letter-spacing: 0.5px;
              position: relative;
              padding-bottom: 8px;
            }
            
            .section-title:after {
              content: "";
              position: absolute;
              bottom: 0;
              left: 0;
              width: 60px;
              height: 3px;
              background: linear-gradient(90deg, #64ffda, transparent);
              border-radius: 3px;
            }

            .timestamp {
              text-align: center;
              font-size: 12px;
              color: rgba(255, 255, 255, 0.5);
              margin-top: 20px;
            }

            /* For long reports that may need page breaks */
            @media print {
              .page-break {
                page-break-before: always;
              }
            }
          </style>
        </head>
        <body>
          <div class="background-curve">
            <div class="curve"></div>
          </div>

          <div class="container">
            <header class="header">
              <div class="logo-section">
                ${
                  logoBase64
                    ? `<img src="${logoBase64}" class="logo" alt="ModelSuite.ai" />`
                    : `<div class="logo-fallback">MS</div>`
                }
              </div>
              <div class="logo-text">MODEL QUESTIONNAIRE REPORT</div>
            </header>

            <section class="model-info">
              <div class="info-item">
                <div class="info-label">Model Name</div>
                <div class="info-value">${modelData.fullName || "N/A"}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Email</div>
                <div class="info-value">${modelData.email || "N/A"}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Phone</div>
                <div class="info-value">${modelData.phone || "N/A"}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Date Generated</div>
                <div class="info-value">${new Date().toLocaleDateString()}</div>
              </div>
            </section>

            <section class="content">
              ${this.generateQuestionAnswerHTML(answers, templateData)}
            </section>

            <footer class="contact-info">
              <div class="contact-item">
                <svg viewBox="0 0 24 24">
                  <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                </svg>
                ${contactInfo.email}
              </div>
              <div class="contact-item">
                <svg viewBox="0 0 24 24">
                  <path d="M6.62 10.79c1.44 2.83 3.76 5.15 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                </svg>
                ${contactInfo.phone}
              </div>
              <div class="contact-item">
                <svg viewBox="0 0 24 24">
                  <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                </svg>
                ${contactInfo.website}
              </div>
            </footer>
            
            <div class="timestamp">Generated on ${timestamp}</div>
          </div>
        </body>
      </html>
    `;
  }

  generateQuestionAnswerHTML(answers, templateData) {
    let html = "";

    if (!templateData || !templateData.sections) {
      return '<div class="section"><div class="section-title">No sections found</div></div>';
    }

    let sectionCounter = 0;

    templateData.sections.forEach((section, sectionIndex) => {
      // Add a page break class for long reports (except first section)
      const sectionBreakClass =
        sectionIndex > 0 && sectionIndex % 3 === 0 ? "section-break" : "";

      sectionCounter++;

      html += `
        <div class="section ${sectionBreakClass}" ${sectionIndex > 0 ? 'style="margin-top: 40px;"' : ""}>
          <h3 class="section-title">
            ${section.title}
          </h3>
          
          ${section.questions
            .map((question, qIndex) => {
              const answer = answers.find(
                (a) => a.questionId?.toString() === question._id?.toString(),
              );
              return `
              <div class="question" ${qIndex === section.questions.length - 1 ? 'style="margin-bottom: 0;"' : ""}>
                <div class="question-text">${question.label}</div>
                <div class="answer">
                  ${answer ? this.formatAnswer(answer.answer, question.type) : "No response provided"}
                </div>
              </div>
            `;
            })
            .join("")}
        </div>
      `;
    });

    return html;
  }

  async getLogoBase64(customPath = null) {
    try {
      // Check custom path first if provided
      if (customPath && fs.existsSync(customPath)) {
        const logoBuffer = fs.readFileSync(customPath);
        const ext = path.extname(customPath).toLowerCase();
        const mimeType = this.getMimeType(ext);
        const base64 = logoBuffer.toString("base64");
        console.log(`PDF Generator: Found logo at custom path: ${customPath}`);
        return `data:${mimeType};base64,${base64}`;
      }

      // Try multiple logo locations with absolute paths
      const logoLocations = [
        path.join(__dirname, "..", "public", "assets", "logo.png"),
        path.join(__dirname, "..", "public", "assets", "logo.webp"),
        path.join(__dirname, "..", "public", "assets", "logo.jpg"),
        path.join(
          __dirname,
          "..",
          "..",
          "modelsuite-frontend",
          "public",
          "logo.webp",
        ),
        path.join(
          __dirname,
          "..",
          "..",
          "modelsuite-frontend",
          "public",
          "logo.png",
        ),
      ];

      for (const logoPath of logoLocations) {
        if (fs.existsSync(logoPath)) {
          const logoBuffer = fs.readFileSync(logoPath);
          const ext = path.extname(logoPath).toLowerCase();
          const mimeType = this.getMimeType(ext);
          const base64 = logoBuffer.toString("base64");
          console.log(`PDF Generator: Found logo at ${logoPath}`);
          return `data:${mimeType};base64,${base64}`;
        }
      }

      console.log("PDF Generator: No logo found, using fallback");
      return null; // Will use CSS fallback
    } catch (error) {
      console.error("PDF Generator: Error loading logo:", error);
      return null;
    }
  }

  getMimeType(extension) {
    switch (extension) {
      case ".webp":
        return "image/webp";
      case ".jpg":
      case ".jpeg":
        return "image/jpeg";
      case ".png":
      default:
        return "image/png";
    }
  }

  // Embedded Inter font to avoid external dependencies
  async getInterFontBase64() {
    try {
      // Path to embedded Inter font file (woff2 format)
      const fontPath = path.join(
        __dirname,
        "..",
        "assets",
        "Inter-Regular.woff2",
      );

      // Check if the font exists
      if (fs.existsSync(fontPath)) {
        const fontBuffer = fs.readFileSync(fontPath);
        return `data:font/woff2;base64,${fontBuffer.toString("base64")}`;
      }

      console.log(
        "PDF Generator: Inter font not found, falling back to system fonts",
      );
      return "";
    } catch (error) {
      console.error("PDF Generator: Error loading Inter font:", error);
      return "";
    }
  }

  // Get dimensions for different paper formats
  getFormatDimensions(format) {
    const formats = {
      A4: { width: 794, height: 1123 },
      Letter: { width: 816, height: 1056 },
      Legal: { width: 816, height: 1344 },
    };

    return formats[format] || formats["A4"];
  }

  formatAnswer(answer, questionType) {
    if (answer === null || answer === undefined) return "No response provided";

    try {
      switch (questionType) {
        case "multi-select":
          return Array.isArray(answer) ? answer.join(", ") : answer;
        case "boolean":
          return answer ? "Yes" : "No";
        case "date":
          return new Date(answer).toLocaleDateString();
        case "number":
          return Number(answer).toLocaleString();
        case "textarea":
          // Handle paragraphs nicely
          return String(answer)
            .split("\n")
            .map((line) => `<p>${line}</p>`)
            .join("");
        default:
          return String(answer);
      }
    } catch (error) {
      console.error("PDF Generator: Error formatting answer:", error);
      return String(answer || "Error formatting answer");
    }
  }

  async cleanup() {
    try {
      if (this.browser) {
        await this.browser.close();
        this.browser = null;
        console.log("PDF Generator: Browser closed successfully");
      }
    } catch (error) {
      console.error("PDF Generator: Error during cleanup:", error);
    }
  }
}

export default new PDFGenerator();
