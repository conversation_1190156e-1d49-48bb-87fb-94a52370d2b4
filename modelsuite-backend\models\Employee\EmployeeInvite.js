import mongoose from "mongoose";

const employeeInviteSchema = new mongoose.Schema(
  {
    email: {
      type: String,
      required: true,
      lowercase: true,
      trim: true,
    },
    userType: {
      type: String,
      required: true,
      enum: ["creator", "manager", "viewer"],
    },
    token: {
      type: String,
      required: true,
    },
    expiresAt: {
      type: Date,
      required: true,
    },
    status: {
      type: String,
      enum: ["pending", "used", "expired"],
      default: "pending",
    },
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: true,
    },
  },
  {
    timestamps: true, // Adds createdAt and updatedAt fields automatically
  },
);

// Index for faster lookups and to ensure uniqueness
employeeInviteSchema.index({ token: 1 }, { unique: true });
employeeInviteSchema.index({ email: 1, agencyId: 1 }, { unique: true });

// Add index on expiresAt for cleanup tasks
employeeInviteSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

export default mongoose.model("EmployeeInvite", employeeInviteSchema);
