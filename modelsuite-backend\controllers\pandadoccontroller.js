import axios from "axios";
import ModelUser from "../models/model.js";
import ModelContract from "../models/ModelsContract.js";

const API =
  process.env.PANDADOC_API_URL || "https://api.pandadoc.com/public/v1";
const TOKEN = process.env.PANDADOC_API_KEY;

export const getTemplates = async (req, res) => {
  try {
    const response = await axios.get(`${API}/templates`, {
      headers: { Authorization: `API-Key ${TOKEN}` },
    });
    res.json(response.data);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

export const createDocument = async (req, res) => {
  const { recipientName, recipientEmail, templateId, modelId } = req.body;

  if (!recipientName || !recipientEmail || !templateId || !modelId) {
    return res.status(400).json({ error: "Missing required fields" });
  }

  const nameParts = recipientName.trim().split(" ");
  const firstName = nameParts[0];
  const lastName = nameParts[1] || "User";

  try {
    // 🔍 Get Model with Agency
    const modelUser = await ModelUser.findById(modelId).populate("agencyId");
    if (!modelUser) {
      return res.status(404).json({ error: "Model not found" });
    }

    const templateName = "Investor Proposal Template"; // Or fetch dynamically

    const payload = {
      name: `Proposal for ${recipientName}`,
      template_uuid: templateId,
      recipients: [
        {
          email: recipientEmail,
          first_name: firstName,
          last_name: lastName,
          role: "Client",
        },
      ],
      send_email: false,
    };

    // 📄 Create PandaDoc document
    const createRes = await axios.post(`${API}/documents`, payload, {
      headers: {
        Authorization: `API-Key ${TOKEN}`,
        "Content-Type": "application/json",
      },
    });

    const docId = createRes.data.id;
    console.log("✅ PandaDoc Document ID:", docId);

    // 💾 Save in MongoDB
    await ModelContract.create({
      modelId: modelUser._id,
      agencyId: modelUser.agencyId,
      templateName,
      pandaDocId: docId,
      status: "sent",
    });

    res.json({
      message: "📄 Document created and stored in database.",
      documentId: docId,
    });

    // 🕒 Background Polling to send
    (async function pollAndSend() {
      let attempts = 0;
      const maxAttempts = 10;

      while (attempts < maxAttempts) {
        try {
          const statusRes = await axios.get(`${API}/documents/${docId}`, {
            headers: { Authorization: `API-Key ${TOKEN}` },
          });

          const docStatus = statusRes.data.status;
          console.log(`📡 Polling [${attempts + 1}]: ${docStatus}`);

          if (docStatus === "document.draft") {
            await axios.post(
              `${API}/documents/${docId}/send`,
              {
                silent: false,
                subject: "Please review your proposal",
                message: "Prince bhai, sign kar do jaldi se ✅",
              },
              {
                headers: {
                  Authorization: `API-Key ${TOKEN}`,
                  "Content-Type": "application/json",
                },
              },
            );
            console.log("📧 Email sent to", recipientEmail);
            break;
          }
        } catch (err) {
          console.error("❌ Polling error:", err.response?.data || err.message);
        }

        await new Promise((resolve) => setTimeout(resolve, 3000));
        attempts++;
      }
    })();
  } catch (err) {
    console.error(
      "❌ Error in createDocument:",
      err.response?.data || err.message,
    );
    res.status(500).json({
      error: err.response?.data || err.message,
    });
  }
};

export const getDocumentById = async (req, res) => {
  try {
    const response = await axios.get(`${API}/documents/${req.params.id}`, {
      headers: { Authorization: `API-Key ${TOKEN}` },
    });
    res.json(response.data);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

export const handleWebhook = async (req, res) => {
  try {
    const events = Array.isArray(req.body) ? req.body : [req.body];

    for (const eventItem of events) {
      const eventType = eventItem.event;
      const data = eventItem.data;
      const docId = data?.id;

      if (!docId) continue;

      let newStatus = null;

      switch (eventType) {
        case "document_state_changed":
          if (data.status === "document.sent") newStatus = "sent";
          else if (data.status === "document.completed") newStatus = "signed";
          else if (data.status === "document.viewed") newStatus = "viewed";
          break;
        default:
          console.log("⚠️ Ignored event:", eventType);
          continue;
      }

      if (newStatus) {
        await ModelContract.findOneAndUpdate(
          { pandaDocId: docId },
          { status: newStatus },
          { new: true },
        );
        console.log(`✅ ${docId} updated to ${newStatus}`);
      }
    }

    res.status(200).send("Webhook processed");
  } catch (err) {
    console.error("❌ Webhook error:", err.message);
    res.status(500).send("Internal error");
  }
};

export const getContractsByAgency = async (req, res) => {
  const { agencyId } = req.params;

  try {
    const contracts = await ModelContract.find({ agencyId })
      .populate("modelId", "fullName")
      .populate("agencyId", "agencyName")
      .sort({ createdAt: -1 });

    const formatted = contracts.map((contract) => ({
      modelFullName: contract.modelId?.fullName || "N/A",
      agencyName: contract.agencyId?.agencyName || "N/A",
      templateName: contract.templateName,
      status: contract.status,
      contracts,
    }));

    res.json({ total: formatted.length, contracts: formatted });
  } catch (err) {
    console.error("❌ Error fetching contracts:", err.message);
    res.status(500).json({ error: "Internal server error" });
  }
};

export const getContractsByModelAndAgency = async (req, res) => {
  const { agencyId, modelId } = req.params;

  try {
    const contracts = await ModelContract.find({ agencyId, modelId })
      .populate("modelId", "fullName")
      .populate("agencyId", "agencyName")
      .sort({ createdAt: -1 });

    const formatted = contracts.map((contract) => ({
      modelFullName: contract.modelId?.fullName || "N/A",
      agencyName: contract.agencyId?.agencyName || "N/A",
      templateName: contract.templateName,
      status: contract.status,
      pandaDocId: contract.pandaDocId,
      createdAt: contract.createdAt,
      _id: contract._id,
    }));

    res.json({ total: formatted.length, contracts: formatted });
  } catch (err) {
    console.error("❌ Error fetching model-agency contracts:", err.message);
    res.status(500).json({ error: "Internal server error" });
  }
};
