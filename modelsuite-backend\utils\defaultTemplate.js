const defaultTemplate = {
  title: "Default Model Onboarding Questionnaire",
  description:
    "Standard questionnaire agencies use to get to know new models better",
  sections: [
    {
      title: "General",
      questions: [
        { label: "Name", type: "text", required: true },
        { label: "Age", type: "number" },
        { label: "City", type: "text" },
        { label: "Origin", type: "text" },
        { label: "Hometown", type: "text" },
        { label: "Relationship", type: "text" },
        { label: "Allergies", type: "text" },
        { label: "Sexual Orientation", type: "text" },
        { label: "Tattoos", type: "text" },
        { label: "Pets", type: "text" },
        { label: "Spiritual", type: "boolean" },
        { label: "Religion", type: "text" },
        { label: "Smoking", type: "boolean" },
        { label: "Drugs", type: "boolean" },
        { label: "Alcohol", type: "boolean" },
      ],
    },
    {
      title: "Personal",
      questions: [
        { label: "Dealing with stress and overwhelm (routines)", type: "text" },
        { label: "Long-term goal(s) (professional & personal)", type: "text" },
        { label: "Philosophy of life", type: "text" },
        { label: "The three most formative events in your life", type: "text" },
        { label: "What do you love most about yourself?", type: "text" },
        { label: "What you dislike about yourself", type: "text" },
        { label: "Happy childhood memories", type: "text" },
        { label: "Strengths", type: "text" },
        { label: "Weaknesses", type: "text" },
        { label: "Deepest inner desire", type: "text" },
        { label: "Dream man idea", type: "text" },
        { label: "Do you sing?", type: "boolean" },
        { label: "What do you like most about other people?", type: "text" },
        {
          label: "What is the first thing you do in the morning?",
          type: "text",
        },
        { label: "What are your most important values?", type: "text" },
        { label: "What turns you off most about men?", type: "text" },
        { label: "What do you do when you're home alone?", type: "text" },
        { label: "Which countries would you like to travel to?", type: "text" },
        { label: "What is your favorite state (feeling/mood)?", type: "text" },
        {
          label:
            "If you could travel spontaneously, what would be your destination?",
          type: "text",
        },
        { label: "What do you spend the most money on?", type: "text" },
        {
          label: "Best date you've ever had? Please provide details.",
          type: "text",
        },
      ],
    },
    {
      title: "Family",
      questions: [
        { label: "Siblings", type: "text" },
        { label: "Father, Mother", type: "text" },
        { label: "Contact with family?", type: "text" },
      ],
    },
    {
      title: "Work/Career",
      questions: [
        { label: "Greatest and most important achievements", type: "text" },
        { label: "Dealing with haters and hate", type: "text" },
        { label: "Previous professions", type: "text" },
        { label: "High school diploma", type: "text" },
        { label: "Most important contacts in public spaces", type: "text" },
      ],
    },
    {
      title: "Passions and favorite things",
      questions: [
        { label: "Vacation, favorite place", type: "text" },
        { label: "Favorite color(s)", type: "text" },
        { label: "Favorite place", type: "text" },
        { label: "Personal favorite emoji", type: "text" },
        { label: "Sport", type: "text" },
        { label: "Books", type: "text" },
        { label: "Favorite club", type: "text" },
        { label: "Favorite movie", type: "text" },
        { label: "Favorite animal", type: "text" },
        { label: "Favorite candy", type: "text" },
        { label: "Favorite food", type: "text" },
      ],
    },
    {
      title: "Hobbies and leisure activities",
      questions: [
        { label: "Sport", type: "text" },
        { label: "Meditation, yoga", type: "text" },
        { label: "Sports as a child", type: "text" },
        { label: "Instrument", type: "text" },
      ],
    },
    {
      title: "Musical taste",
      questions: [
        { label: "Favorite band", type: "text" },
        { label: "Favorite genre", type: "text" },
        { label: "Music you don't dislike", type: "text" },
      ],
    },
  ],
};

export default defaultTemplate;
