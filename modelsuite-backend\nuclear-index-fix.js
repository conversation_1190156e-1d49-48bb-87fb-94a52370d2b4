import mongoose from 'mongoose';

async function nuclearIndexFix() {
  try {
    // Use the actual MongoDB URI from .env (MongoDB Atlas)
    const mongoUri = 'mongodb+srv://devanand:<EMAIL>/modelsuite?retryWrites=true&w=majority&appName=Cluster0';
    await mongoose.connect(mongoUri);
    console.log('🔍 Connected to MongoDB Atlas database');

    const db = mongoose.connection.db;
    const collection = db.collection('voiceassignments');
    
    console.log('\n🗂️ BEFORE - Current indexes:');
    const beforeIndexes = await collection.indexes();
    beforeIndexes.forEach((index, i) => {
      console.log(`${i + 1}. ${index.name}: ${JSON.stringify(index.key)} ${index.unique ? '(UNIQUE)' : ''}`);
    });

    // Nuclear option: Drop ALL indexes except _id
    console.log('\n💥 NUCLEAR OPTION: Dropping all indexes except _id...');
    const indexesToDrop = beforeIndexes.filter(idx => idx.name !== '_id_');
    
    for (const index of indexesToDrop) {
      try {
        console.log(`🗑️ Dropping index: ${index.name}`);
        await collection.dropIndex(index.name);
        console.log(`✅ Dropped: ${index.name}`);
      } catch (error) {
        console.log(`❌ Failed to drop ${index.name}:`, error.message);
      }
    }

    console.log('\n🗂️ AFTER - Remaining indexes:');
    const afterIndexes = await collection.indexes();
    afterIndexes.forEach((index, i) => {
      console.log(`${i + 1}. ${index.name}: ${JSON.stringify(index.key)} ${index.unique ? '(UNIQUE)' : ''}`);
    });

    // Recreate only the essential unique index
    console.log('\n🔨 Recreating essential unique index...');
    try {
      await collection.createIndex(
        { agencyId: 1, modelId: 1, title: 1 },
        { 
          unique: true, 
          partialFilterExpression: { isDeleted: false },
          name: 'unique_assignment_per_model'
        }
      );
      console.log('✅ Recreated unique_assignment_per_model index');
    } catch (error) {
      console.log('❌ Failed to recreate unique index:', error.message);
    }

    console.log('\n🗂️ FINAL - All indexes:');
    const finalIndexes = await collection.indexes();
    finalIndexes.forEach((index, i) => {
      console.log(`${i + 1}. ${index.name}: ${JSON.stringify(index.key)} ${index.unique ? '(UNIQUE)' : ''}`);
    });

    // Test assignment creation
    console.log('\n🧪 Testing assignment creation...');
    try {
      const testResult = await collection.insertOne({
        questionIds: [new mongoose.Types.ObjectId()],
        title: 'Test Assignment After Nuclear Fix',
        modelId: new mongoose.Types.ObjectId('6877d1d1d4660031e907a8eb'),
        agencyId: new mongoose.Types.ObjectId('68723da81257ef228b214073'),
        status: 'assigned',
        createdAt: new Date()
      });
      console.log('✅ Test assignment created successfully:', testResult.insertedId);
      
      // Clean up test assignment
      await collection.deleteOne({ _id: testResult.insertedId });
      console.log('🧹 Test assignment cleaned up');
    } catch (error) {
      console.log('❌ Test assignment creation failed:', error.message);
    }

    await mongoose.disconnect();

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

nuclearIndexFix();
