import Invoice from "../../models/billing/invoice.js";
import fs from "fs";
import csv from "fast-csv";
import {
  sendInvoiceEmailToClient,
  sendPaymentStatusEmail,
} from "../../utils/sendOtpToEmail.js"; // update path as per your structure

export const uploadInvoice = async (req, res) => {
  try {
    const { client, campaign, amount, currency, dueDate, status, note } =
      req.body;

    // File validation
    if (!req.file) {
      return res
        .status(400)
        .json({ success: false, message: "Invoice file is required." });
    }

    // Required field validation
    const missingFields = [];
    if (!client) missingFields.push("client");
    if (!amount) missingFields.push("amount");
    if (!currency) missingFields.push("currency");
    if (!dueDate) missingFields.push("dueDate");
    if (!status) missingFields.push("status");

    if (missingFields.length > 0) {
      return res.status(400).json({
        success: false,
        message: `Missing required fields: ${missingFields.join(", ")}`,
      });
    }

    // Make file downloadable
    const downloadableFileUrl = req.file.path.replace(
      "/upload/",
      "/upload/fl_attachment/",
    );

    const invoice = new Invoice({
      client,
      campaign,
      amount,
      currency,
      dueDate,
      status,
      note,
      fileUrl: downloadableFileUrl,
      uploadedBy: req.user.id,
    });

    await invoice.save();
    await invoice.populate([
      {
        path: "client",
        select: "fullName email profilePhoto username _id",
      },
      {
        path: "uploadedBy",
        select: "agencyName agencyEmail username _id",
      },
    ]);

    // ✅ Send Email in Background (Don't wait with await)
    if (invoice.client?.email && invoice.client?.fullName) {
      sendInvoiceEmailToClient({
        clientEmail: invoice.client.email,
        clientName: invoice.client.fullName,
        invoiceFileUrl: invoice.fileUrl,
      }).catch((err) => {
        console.error("Email send failed:", err.message);
      });
    }

    res.status(201).json({
      success: true,
      message: "Invoice uploaded successfully.",
      invoice,
    });
  } catch (err) {
    console.error("Upload Invoice Error:", err);
    res.status(500).json({
      success: false,
      message: "Server error while uploading invoice.",
    });
  }
};

export const getInvoices = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const filter = { uploadedBy: req.user.id };

    // ✅ Optional status filter
    if (req.query.status) {
      filter.status = req.query.status; // should be "Paid", "Unpaid", or "Overdue"
    }

    const totalInvoices = await Invoice.countDocuments(filter);

    const invoices = await Invoice.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate({
        path: "client",
        select: "fullName profilePhoto username _id",
      })
      .populate({
        path: "uploadedBy",
        select: "agencyName agencyEmail username _id",
      });

    res.status(200).json({
      success: true,
      page,
      totalPages: Math.ceil(totalInvoices / limit),
      totalInvoices,
      invoices,
    });
  } catch (err) {
    console.error("Get Invoices Error:", err);
    res.status(500).json({
      success: false,
      message: "Server error while fetching invoices.",
    });
  }
};
export const updateInvoice = async (req, res) => {
  try {
    const invoiceId = req.params.id;
    const agencyId = req.user._id; // Logged-in user's ID

    // Allowed fields to update
    const allowedUpdates = [
      "client",
      "campaign",
      "amount",
      "currency",
      "dueDate",
      "status",
      "note",
    ];
    const updateData = {};

    // Only include fields present in request
    allowedUpdates.forEach((field) => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });

    // Now perform secure update
    const updatedInvoice = await Invoice.findOneAndUpdate(
      { _id: invoiceId, uploadedBy: agencyId }, // Only update if agency matches
      updateData,
      { new: true }, // Return updated document (optional, can be false if you don't need it)
    );

    if (!updatedInvoice) {
      return res.status(404).json({
        success: false,
        message: "Invoice not found or unauthorized.",
      });
    }

    res.status(200).json({
      success: true,
      message: "Invoice updated successfully.",
    });
  } catch (error) {
    console.error("Update Invoice Error:", error);
    res.status(500).json({
      success: false,
      message: "Something went wrong while updating invoice.",
    });
  }
};

export const deleteInvoice = async (req, res) => {
  try {
    const invoiceId = req.params.id;
    const userId = req.user._id;

    const deletedInvoice = await Invoice.findOneAndDelete({
      _id: invoiceId,
      uploadedBy: userId,
    });

    if (!deletedInvoice) {
      return res.status(404).json({
        success: false,
        message: "Invoice not found or not authorized to delete.",
      });
    }

    res.status(200).json({
      success: true,
      message: "Invoice deleted successfully.",
    });
  } catch (error) {
    console.error("Delete Invoice Error:", error);
    res.status(500).json({
      success: false,
      message: "Something went wrong while deleting invoice.",
    });
  }
};

export const getInvoiceById = async (req, res) => {
  try {
    const invoiceId = req.params.id;
    const userId = req.user._id; // From auth middleware

    const invoice = await Invoice.findOne({
      _id: invoiceId,
      uploadedBy: userId,
    })
      .populate({
        path: "client",
        select: "fullName profilePhoto username _id",
      })
      .populate({
        path: "uploadedBy",
        select: "agencyName agencyEmail username _id",
      });

    if (!invoice) {
      return res.status(404).json({
        success: false,
        message: "Invoice not found or not authorized to access.",
      });
    }

    res.status(200).json({
      success: true,
      data: invoice,
    });
  } catch (error) {
    console.error("Get Invoice Error:", error);
    res.status(500).json({
      success: false,
      message: "Something went wrong while fetching invoice.",
    });
  }
};

export const exportInvoices = async (req, res) => {
  try {
    const userId = req.user._id;

    const invoices = await Invoice.find({ uploadedBy: userId })
      .populate({
        path: "client",
        select: "fullName profilePhoto username _id phone email",
      })
      .populate({
        path: "uploadedBy",
        select: "agencyName agencyEmail username _id agencyPhone",
      });

    const csvStream = csv.format({ headers: true });
    const exportDir = "public/files/export";

    if (!fs.existsSync(exportDir)) {
      fs.mkdirSync(exportDir, { recursive: true });
    }

    const filePath = `${exportDir}/invoices.csv`;
    const writableStream = fs.createWriteStream(filePath);
    csvStream.pipe(writableStream);

    invoices.forEach((inv) => {
      const firstAttempt =
        inv.paymentAttempts && inv.paymentAttempts.length > 0
          ? inv.paymentAttempts[0]
          : null;

      csvStream.write({
        InvoiceID: inv._id,
        ClientName: inv.client?.fullName || "-",
        ClientUsername: inv.client?.username || "-",
        ClientEmail: inv.client?.email || "-",
        Campaign: inv.campaign || "-",
        Amount: `${inv.currency}${inv.amount}` || "-",
        Status: inv.status || "-",
        DueDate: inv.dueDate ? new Date(inv.dueDate).toLocaleDateString() : "-",
        UploadedBy: inv.uploadedBy?.agencyName || "-",
        UploaderEmail: inv.uploadedBy?.agencyEmail || "-",
        CreatedAt: inv.createdAt
          ? new Date(inv.createdAt).toLocaleDateString()
          : "-",
        UpdatedAt: inv.updatedAt
          ? new Date(inv.updatedAt).toLocaleDateString()
          : "-",
        AttemptNumber:
          inv.status === "Paid" ? "-" : inv.paymentAttempts?.length || 0,
        AttemptAt:
          inv.status === "Paid"
            ? "-"
            : firstAttempt
              ? new Date(firstAttempt.attemptAt).toLocaleString()
              : "-",
        Success:
          inv.status === "Paid"
            ? "-"
            : firstAttempt
              ? firstAttempt.success
                ? "Yes"
                : "No"
              : "-",
        FailureReason:
          inv.status === "Paid"
            ? "-"
            : firstAttempt && !firstAttempt.success
              ? firstAttempt.reason || "N/A"
              : "-",
      });
    });

    csvStream.end();

    writableStream.on("finish", () => {
      res.status(200).json({
        success: true,
        downloadUrl: `${process.env.BASE_URL}/files/export/invoices.csv`,
      });
    });
  } catch (err) {
    console.error("CSV Export Error:", err);
    res.status(500).json({
      success: false,
      message: "Failed to export invoices",
    });
  }
};

export const mass_payment_status = async (req, res) => {
  try {
    const updates = JSON.parse(req.body.updates);
    const files = req.files;

    const updatedInvoices = [];

    for (const update of updates) {
      const {
        invoiceId,
        transactionMethod,
        transactionStatus,
        screenshotName,
        reason, // ✅ Accept reason from frontend
      } = update;

      // ✅ Validate reason if status is Failed
      if (transactionStatus === "Failed" && (!reason || reason.trim() === "")) {
        return res.status(400).json({
          message: `Failure reason is required for failed transaction on invoice ${invoiceId}`,
        });
      }

      const file = files.find((f) => f.originalname === screenshotName);

      // ✅ Build update object
      const updateData = {
        status: transactionStatus,
        transactionMethod,
        screenshotUrl: file ? file.path : undefined,
        $push: {
          paymentAttempts: {
            success: transactionStatus !== "Failed",
            reason: transactionStatus === "Failed" ? reason : undefined,
            attemptAt: new Date(),
          },
        },
      };

      // ✅ Update invoice
      await Invoice.findByIdAndUpdate(invoiceId, updateData);

      // ✅ Fetch populated invoice
      const populatedInvoice = await Invoice.findById(invoiceId).populate([
        {
          path: "client",
          select: "fullName email profilePhoto username _id",
        },
        {
          path: "uploadedBy",
          select: "agencyName agencyEmail username _id",
        },
      ]);

      updatedInvoices.push(populatedInvoice);

      // ✅ Send email alert in background
      if (populatedInvoice?.client?.email) {
        sendPaymentStatusEmail({
          email: populatedInvoice.client.email,
          name: populatedInvoice.client.fullName,
          status: transactionStatus,
          method: transactionMethod,
          reason,
          invoiceId,
          fileUrl: populatedInvoice.fileUrl,
          screenshotUrl: file ? file.path : undefined,
        }).catch((err) => console.error("Email alert failed:", err.message));
      }
    }

    res.status(200).json({
      message: "Mass update successful",
      data: updatedInvoices,
    });
  } catch (err) {
    console.error("Mass update error:", err);
    res.status(500).json({ message: "Something went wrong" });
  }
};

export const updateSingleInvoiceWithFile = async (req, res) => {
  try {
    const invoiceId = req.params.id;
    const agencyId = req.user._id;

    const allowedUpdates = [
      "client",
      "campaign",
      "amount",
      "currency",
      "dueDate",
      "status",
      "note",
      "transactionMethod",
    ];

    const updateData = {};

    allowedUpdates.forEach((field) => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });

    // Optional screenshot
    if (req.file) {
      updateData.screenshotUrl = req.file.path;
    }

    const updatedInvoice = await Invoice.findOneAndUpdate(
      { _id: invoiceId, uploadedBy: agencyId },
      updateData,
      { new: true },
    );

    if (!updatedInvoice) {
      return res.status(404).json({
        success: false,
        message: "Invoice not found or unauthorized.",
      });
    }

    res.status(200).json({
      success: true,
      message: "Invoice updated successfully.",
      data: updatedInvoice,
    });
  } catch (error) {
    console.error("Update Invoice Error:", error);
    res.status(500).json({
      success: false,
      message: "Something went wrong while updating invoice.",
    });
  }
};

export const getInvoices_dash = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const filter = { uploadedBy: req.user.id };

    if (req.query.status) {
      filter.status = req.query.status;
    }

    const totalInvoices = await Invoice.countDocuments(filter);

    const invoices = await Invoice.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate({
        path: "client",
        select: "fullName profilePhoto username _id",
      })
      .populate({
        path: "uploadedBy",
        select: "agencyName agencyEmail username _id",
      });

    // 👉 Exchange rates map
    const exchangeRates = {
      INR: 0.012,
      USD: 1,
      EUR: 1.09,
      GBP: 1.27,
      AED: 0.27,
    };

    // 👉 Append USD-converted amount
    const convertedInvoices = invoices.map((invoice) => {
      const rate = exchangeRates[invoice.currency] || 1;
      const amountInUSD = invoice.amount * rate;

      return {
        ...invoice._doc,
        amountInUSD: Number(amountInUSD.toFixed(2)), // USD converted value
      };
    });

    res.status(200).json({
      success: true,
      page,
      totalPages: Math.ceil(totalInvoices / limit),
      totalInvoices,
      invoices: convertedInvoices,
    });
  } catch (err) {
    console.error("Get Invoices Error:", err);
    res.status(500).json({
      success: false,
      message: "Server error while fetching invoices.",
    });
  }
};
