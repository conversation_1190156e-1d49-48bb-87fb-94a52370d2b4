import React, { useEffect, useRef, useState } from "react";
import {
  TrendingUp,
  Play,
  User,
  Music,
  RefreshCw,
  AlertCircle,
  ExternalLink,
} from "lucide-react";
import GoogleTrend from "./GoogleTrend";
import sounddata from "../../../data/sound.json";
import videodata from "../../../data/video.json";
import authordata from "../../../data/author.json";
import ViralTrendsDashboard from "./ViralTrendsDashboard.jsx";

const fetchTrendingData = async (endpoint, params) => {
  try {
    const options = {
      method: "GET",
      url: `https://tiktok-most-trending-and-viral-content.p.rapidapi.com/${endpoint}`,
      params,
      headers: {
        "x-rapidapi-key": "**************************************************",
        "x-rapidapi-host":
          "tiktok-most-trending-and-viral-content.p.rapidapi.com",
      },
    };

    const response = await fetch(
      options.url + "?" + new URLSearchParams(options.params),
      {
        method: options.method,
        headers: options.headers,
      },
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log("Fetched data:", data.data.stats);
    return data.data.stats || [];

    // eslint-disable-next-line no-unused-vars
  } catch (error) {
    // Fallback logic
    switch (endpoint) {
      case "music":
        return sounddata.data.stats;
      case "video":
        return videodata.data.stats;
      case "bloggers":
        return authordata.data.stats;
      default:
        return [];
    }
  }
};

const LoadingCard = () => (
  <div className="bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-2xl shadow-lg overflow-hidden animate-pulse">
    <div className="w-full h-48 bg-gray-700"></div>
    <div className="p-4">
      <div className="h-4 bg-gray-600 rounded mb-2"></div>
      <div className="h-3 bg-gray-600 rounded w-3/4"></div>
    </div>
  </div>
);

const ErrorCard = ({ onRetry }) => (
  <div className="bg-gradient-to-br from-red-900/50 to-red-800/50 border border-red-600/30 rounded-2xl p-6 text-center backdrop-blur-sm">
    <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
    <h3 className="text-lg font-semibold text-red-300 mb-2">
      Failed to load content
    </h3>
    <p className="text-red-200 mb-4">
      Unable to fetch trending data. Please try again.
    </p>
    <button
      onClick={onRetry}
      className="bg-gradient-to-r from-red-600 to-red-700 text-white px-4 py-2 rounded-lg hover:from-red-700 hover:to-red-800 transition-all duration-300 shadow-lg"
    >
      Try Again
    </button>
  </div>
);
const VideoCard = ({ item, index }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [videoError, setVideoError] = useState(false);
  const videoRef = useRef(null);

  const handleMouseEnter = () => {
    setIsHovered(true);
    if (videoRef.current && !videoError) {
      videoRef.current.play().catch(() => setVideoError(true));
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    if (videoRef.current) {
      videoRef.current.pause();
      videoRef.current.currentTime = 0;
    }
  };

  return (
    <div
      className="bg-gradient-to-br w-[320px] from-gray-900 to-gray-800 border border-gray-700 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl hover:scale-105 transition-all duration-300 group"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="relative aspect-[9/16] bg-black">
        {/* 🎬 Video Preview */}
        {item.videoUrl && !videoError && (
          <video
            ref={videoRef}
            className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-300 ${isHovered ? "opacity-100" : "opacity-0"}`}
            src={item.videoUrl}
            muted
            loop
            playsInline
            preload="metadata"
            onError={() => setVideoError(true)}
          />
        )}

        {/* 🖼️ Thumbnail */}
        <img
          src={item.cover || item.avatar}
          alt={item.title || item.nickname}
          className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-300 ${isHovered && !videoError ? "opacity-0" : "opacity-100"}`}
        />

        {/* 🏆 Ranking */}
        <div className="absolute top-2 left-2 bg-gradient-to-r from-pink-600 to-pink-500 text-white px-2 py-1 rounded-full text-xs font-bold z-10">
          🏆 #{index + 1}
        </div>

        {/* ▶️ Play Button */}
        {!isHovered && (
          <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
            <div className="bg-white bg-opacity-90 rounded-full p-3 transform group-hover:scale-110 transition-transform duration-300">
              <Play className="w-6 h-6 text-pink-600 ml-1" />
            </div>
          </div>
        )}

        {/* 🔴 LIVE Badge */}
        {isHovered && !videoError && (
          <div className="absolute bottom-2 right-2 flex items-center gap-2 bg-black bg-opacity-50 rounded-full px-3 py-1">
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            <span className="text-white text-xs">LIVE</span>
          </div>
        )}

        {/* ⏱️ Duration */}
        {item.videoDuration && (
          <div className="absolute bottom-2 left-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs">
            ⏱️ {item.videoDuration}s
          </div>
        )}
      </div>

      <div className="p-4 text-sm text-gray-300 space-y-1">
        <h3 className="text-base font-bold text-white truncate">
          {item.videoTitle}
        </h3>
        <p>
          👤{" "}
          <span className="font-medium text-gray-200">{item.authorName}</span>
        </p>
        <p>
          🎵 Download Link:{" "}
          <span
            className="text-pink-400 cursor-pointer hover:underline"
            onClick={() => {
              navigator.clipboard.writeText(item?.videoUrl);
              alert("Link copied to clipboard!");
              window.open("https://ssstik.io/in", "_blank");
            }}
          >
            {item?.videoUrl}
          </span>
        </p>

        <div className="flex flex-wrap gap-3 mt-2 text-xs">
          <span>👁️ {item.playCount?.toLocaleString()} views</span>
          <span>❤️ {item.likes?.toLocaleString()} likes</span>
          <span>💬 {item.commentsCount?.toLocaleString()} comments</span>
          <span>🔁 {item.shares?.toLocaleString()} shares</span>
          <span>
            📈 +{item.playCountDailyRise?.toLocaleString()} views today
          </span>
          <span>🎵 {item.musicTitle}</span>
        </div>

        <div className="flex justify-between items-center pt-2">
          <span className="text-gray-400 text-xs">
            📅 {new Date(item.videoCreateTime).toLocaleDateString()}
          </span>
          {item.videoUrl && (
            <a
              href={item.videoUrl}
              target="_blank"
              rel="noopener noreferrer"
              onClick={(e) => e.stopPropagation()}
              className="text-pink-400 hover:text-pink-300"
            >
              <ExternalLink className="w-4 h-4" />
            </a>
          )}
        </div>
      </div>
    </div>
  );
};
const CreatorCard = ({ item, index }) => (
  <div className="bg-gradient-to-br w-[320px] from-gray-900 to-gray-800 border border-gray-700 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl hover:scale-105 transition-all duration-300 group max-w-md mx-auto">
    <div className="relative">
      <img
        src={item.avatar}
        alt={item.nickname}
        className="w-full h-48 object-cover"
      />
      <div className="absolute top-2 left-2 bg-gradient-to-r from-pink-600 to-pink-500 text-white px-3 py-1 rounded-full text-xs font-bold">
        👑 #{index + 1}
      </div>
      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-300 flex items-center justify-center">
        <User className="w-12 h-12 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      </div>
    </div>

    <div className="p-4 text-sm text-gray-300 space-y-2 capitalize">
      <h3 className="text-lg font-bold text-white">{item.nickname} ✅</h3>
      <p>📍 Region: {item.region || "N/A"}</p>
      <p>🧠 Unique ID: {item.uniqueId}</p>
      <p>
        💬 Signature: <span className="italic">"{item.signature}"</span>
      </p>
      <p>🎥 Clips: {item.clips}</p>
      <p>👍 Likes: {item.likes?.toLocaleString() || 0}</p>
      <p>📈 Daily Subs Rise: {item.subsDailyRise?.toLocaleString()}</p>
      <p>
        📊 Forecasted Subs Rise: {item.subsDailyRiseForecast?.toLocaleString()}
      </p>
      <p>🎯 Accuracy: {item.subsDailyRiseForecastError}</p>
      <p>🚀 Subscribers: {item.subscribers?.toLocaleString()}</p>
      <p>📅 Updated: {new Date(item.updateDate).toLocaleDateString()}</p>

      <div className="flex items-center justify-between pt-2">
        <span className="text-xs text-gray-400">
          🧲 Engagement Score: {item.subsGraphWeight}
        </span>
        {item.url && (
          <a href={item.url} target="_blank" rel="noopener noreferrer">
            <ExternalLink className="w-5 h-5 text-gray-400 hover:text-white" />
          </a>
        )}
      </div>
    </div>
  </div>
);
const MusicCard = ({ item, index }) => {
  const musicData = item.music || item;

  return (
    <div className="bg-gradient-to-br w-[320px] from-gray-900 to-gray-800 border border-gray-700 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl hover:scale-105 transition-all duration-300 group max-w-md mx-auto">
      <div className="relative">
        <img
          src={musicData.cover}
          alt={musicData.title}
          className="w-full h-72 object-cover"
        />
        <div className="absolute top-2 left-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-1 rounded-full text-xs font-bold">
          🔥 #{index + 1}
        </div>
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-opacity duration-300 flex items-center justify-center">
          <Music className="w-12 h-12 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>
      </div>

      <div className="p-4 text-sm text-gray-300 space-y-2 capitalize">
        <h3 className="text-lg font-bold capitalize text-pink-400">
          {musicData.title} 🎶
        </h3>
        <p>
          🎤{" "}
          <span className="font-medium capitalize text-gray-200">
            {musicData.creator || "Unknown Artist"}
          </span>
        </p>
        <div className="flex justify-center items-center">
          <button
            onClick={() => {
              if (musicData.downloadLink) {
                window.open(musicData.downloadLink, "_blank");
              } else {
                alert("No download link available!");
              }
            }}
            className="cursor-pointer mt-2 text-center bg-pink-500 text-white py-1 rounded mx-2 w-full"
          >
            Download
          </button>
        </div>
        <p>🌍 Region: {musicData.artistRegion || "N/A"}</p>
        <p>🕒 Duration: {musicData.duration}s</p>
        <p>💥 Daily Rise: {musicData.dailyRise}</p>
        <p>📈 Forecast: {musicData.dailyRiseForecasting}</p>
        <p>🎯 Accuracy: {musicData.dailyRiseForecastingError}</p>
        <p>
          🎧 Recognition: {musicData.recognitionTitle} by{" "}
          {musicData.recognitionAuthor}
        </p>
        <p>
          🔗 Music Link:{" "}
          <a
            href={musicData.musicUrl}
            target="_blank"
            className="text-pink-400 hover:underline"
          >
            TikTok
          </a>
        </p>
        {musicData.recognitionLink && (
          <p>
            🧠 Shazam:{" "}
            <a
              href={musicData.recognitionLink}
              target="_blank"
              className="text-pink-400 hover:underline"
            >
              Listen
            </a>
          </p>
        )}
        <p>🎁 Original: {musicData.musicOriginal ? "Yes" : "No"}</p>
        <p>🔁 Reposts: {musicData.reposts}</p>
        <p>📅 Updated: {new Date(musicData.updateDate).toLocaleDateString()}</p>

        <div className="flex items-center justify-between pt-2">
          <span className="text-xs text-gray-400">
            📊 Graph Weight: {musicData.graphWeight}
          </span>
          {musicData.url && (
            <a href={musicData.url} target="_blank" rel="noopener noreferrer">
              <ExternalLink className="w-5 h-5 text-gray-400 hover:text-white" />
            </a>
          )}
        </div>
      </div>
    </div>
  );
};

// eslint-disable-next-line no-unused-vars
const Section = ({
  title,
  items,
  type,
  loading,
  error,
  onRetry,
  icon: Icon,
}) => (
  <section className="my-12">
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-3">
        <Icon className="w-8 h-8 text-pink-400" />
        <h2 className="text-3xl font-bold text-white">{title}</h2>
      </div>
      <span className="text-sm text-gray-400 bg-gray-800/50 px-3 py-1 rounded-full border border-gray-600">
        {loading ? "Loading..." : `${items.length} items`}
      </span>
    </div>

    {error ? (
      <ErrorCard onRetry={onRetry} />
    ) : (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        {loading
          ? Array(10)
              .fill(0)
              .map((_, idx) => <LoadingCard key={idx} />)
          : items.map((item, idx) => {
              switch (type) {
                case "video":
                  return <VideoCard key={idx} item={item} index={idx} />;
                case "creator":
                  return <CreatorCard key={idx} item={item} index={idx} />;
                case "music":
                  return <MusicCard key={idx} item={item} index={idx} />;
                default:
                  return null;
              }
            })}
      </div>
    )}
  </section>
);

const ViralTrendGoogleTrend = () => {
  const [videos, setVideos] = useState([]);
  const [creators, setCreators] = useState([]);
  const [musics, setMusics] = useState([]);
  const [loading, setLoading] = useState({
    videos: true,
    creators: true,
    musics: true,
  });
  const [errors, setErrors] = useState({
    videos: false,
    creators: false,
    musics: false,
  });
  const [lastUpdated, setLastUpdated] = useState(null);

  const fetchVideos = async () => {
    setLoading((prev) => ({ ...prev, videos: true }));
    setErrors((prev) => ({ ...prev, videos: false }));

    const videoData = await fetchTrendingData("video", {
      take: "10",
      sorting: "rise",
      days: "1",
      order: "desc",
    });

    if (videoData.length > 0) {
      setVideos(videoData);
    } else {
      setErrors((prev) => ({ ...prev, videos: true }));
    }
    setLoading((prev) => ({ ...prev, videos: false }));
  };

  const fetchCreators = async () => {
    setLoading((prev) => ({ ...prev, creators: true }));
    setErrors((prev) => ({ ...prev, creators: false }));

    const creatorData = await fetchTrendingData("bloggers", {
      take: "10",
      sorting: "rise",
      days: "1",
      order: "desc",
    });

    if (creatorData.length > 0) {
      setCreators(creatorData);
    } else {
      setErrors((prev) => ({ ...prev, creators: true }));
    }
    setLoading((prev) => ({ ...prev, creators: false }));
  };

  const fetchMusics = async () => {
    setLoading((prev) => ({ ...prev, musics: true }));
    setErrors((prev) => ({ ...prev, musics: false }));

    const musicData = await fetchTrendingData("music", {
      take: "10",
      soundType: "Original",
      days: "1",
      sorting: "rise",
      artistLocation: "US",
      category: "90",
      order: "desc",
    });

    if (musicData.length > 0) {
      setMusics(musicData);
    } else {
      setErrors((prev) => ({ ...prev, musics: true }));
    }
    setLoading((prev) => ({ ...prev, musics: false }));
  };

  const fetchAll = async () => {
    await Promise.all([fetchVideos(), fetchCreators(), fetchMusics()]);
    setLastUpdated(new Date());
  };

  useEffect(() => {
    fetchAll();
  }, []);

  const refreshAll = () => {
    fetchAll();
  };

  return (
    <div>
      <ViralTrendsDashboard />
      <GoogleTrend />
      <div className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-950 to-slate-900">
        {/* Header */}
        <header className="bg-gradient-to-br from-gray-900 to-gray-800 shadow-lg border border-gray-700 rounded-xl mx-4 mt-4">
          <div className="max-w-6xl mx-auto px-6 py-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="bg-gradient-to-r from-pink-600 to-pink-500 p-3 rounded-2xl shadow-lg">
                  <TrendingUp className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl font-extrabold text-pink-400 mb-1 tracking-tight">
                    TikTok Viral Trending
                  </h1>
                  <p className="text-lg text-gray-300">
                    Discover what's trending right now
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-4">
                {lastUpdated && (
                  <span className="text-sm text-gray-400">
                    Last updated: {lastUpdated.toLocaleTimeString()}
                  </span>
                )}
                <button
                  onClick={refreshAll}
                  className="px-7 py-3 bg-gradient-to-r from-pink-600 to-pink-500 text-white rounded-lg hover:from-pink-700 hover:to-pink-600 transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl"
                >
                  <RefreshCw className="w-5 h-5" />
                  Refresh
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-6xl mx-auto px-4 py-8">
          <Section
            title="Trending Music"
            items={musics}
            type="music"
            loading={loading.musics}
            error={errors.musics}
            onRetry={fetchMusics}
            icon={Music}
          />
          <Section
            title="Trending Videos"
            items={videos}
            type="video"
            loading={loading.videos}
            error={errors.videos}
            onRetry={fetchVideos}
            icon={Play}
          />

          <Section
            title="Top Creators"
            items={creators}
            type="creator"
            loading={loading.creators}
            error={errors.creators}
            onRetry={fetchCreators}
            icon={User}
          />
        </main>

        {/* Footer */}
        <footer className="bg-gradient-to-br from-gray-950 via-gray-900 to-gray-950 border-t border-gray-800 mt-16">
          <div className="max-w-7xl mx-auto px-4 py-8">
            <div className="text-center text-gray-600">
              <p className="mb-2 font-semibold italic">
                Stay updated with the latest TikTok trends
              </p>
              <div className="text-center text-gray-400">
                <p className="mb-2">
                  Stay updated with the latest TikTok trends
                </p>
                <p className="text-sm">
                  Data refreshes every hour • Built for ModelSuite.ai
                </p>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default ViralTrendGoogleTrend;
