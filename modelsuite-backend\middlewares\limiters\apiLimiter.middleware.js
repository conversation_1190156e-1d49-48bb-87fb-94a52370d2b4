import rateLimit from "express-rate-limit";

export const apiLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 30, // 30 requests per minute
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    error: "Too many requests. Please slow down.",
  },
  handler: (req, res, next, options) => {
    console.log(`[⚠️ API RATE LIMITED] IP ${req.ip} exceeded API limit`);
    res.status(options.statusCode).json(options.message);
  },
});
