import mongoose from "mongoose";

const vacationSchema = new mongoose.Schema(
  {
    active: { type: Boolean, default: false },
    from: { type: Date },
    to: { type: Date },
  },
  { _id: false },
);

const availabilitySchema = new mongoose.Schema(
  {
    monday: [{ type: String, enum: ["am", "pm"] }],
    tuesday: [{ type: String, enum: ["am", "pm"] }],
    wednesday: [{ type: String, enum: ["am", "pm"] }],
    thursday: [{ type: String, enum: ["am", "pm"] }],
    friday: [{ type: String, enum: ["am", "pm"] }],
    saturday: [{ type: String, enum: ["am", "pm"] }],
    sunday: [{ type: String, enum: ["am", "pm"] }],
    vacation: vacationSchema,
  },
  { _id: false },
);

const socialLinksSchema = new mongoose.Schema(
  {
    instagram: { type: String, default: null },
    tiktok: { type: String, default: null },
    youtube: { type: String, default: null },
  },
  { _id: false },
);

const portfolioImageSchema = new mongoose.Schema(
  {
    url: { type: String, required: true },
    order: { type: Number, default: 0 },
  },
  { _id: false },
);

const userProfileSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "ModelUser",
    required: true,
    unique: true,
  },
  display_name: { type: String },
  role: { type: String, default: "model" },
  email: { type: String },
  phone: { type: String },
  country: { type: String },
  city: { type: String }, // Added city field
  bio: { type: String },
  job_title: { type: String }, // Added job title field
  role_description: { type: String }, // Added role description field
  status: { type: String }, // Added real-time status field
  avatar_url: { type: String },
  cover_url: { type: String }, // Added cover photo field
  "2fa_enabled": { type: Boolean, default: false },
  availability: availabilitySchema,
  availability_text: { type: String }, // User-friendly working hours/availability
  reachability: { type: String }, // Best time to reach the user
  social_links: socialLinksSchema,
  portfolio: [portfolioImageSchema],
});

export default mongoose.model("UserProfile", userProfileSchema);
