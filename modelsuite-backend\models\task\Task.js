import mongoose from "mongoose";

const taskSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, "Task title is required!"],
    },
    description: {
      type: String,
      default: "",
    },
    priority: {
      type: String,
      enum: ["critical", "high", "medium", "low"],
      default: "medium",
    },
    dueDate: {
      type: Date,
    },
    status: {
      type: String,
      enum: ["todo", "in_progress", "completed", "on_hold"],
      default: "todo",
    },
    isComplete: {
      type: Boolean,
      default: false,
    },
    isOnHold: {
      type: Boolean,
      default: false,
    },
    onHoldReason: {
      type: String,
      default: "",
    },
    onHoldBy: {
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        refPath: "onHoldBy.userType",
      },
      userType: {
        type: String,
        enum: ["ModelUser", "Agency"],
      },
      timestamp: {
        type: Date,
      },
    },
    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: true,
    },
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: true,
    },
    createdBy: {
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        refPath: "createdBy.userType",
      },
      userType: {
        type: String,
        enum: ["ModelUser", "Agency"],
      },
    },
    assignedTo: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "ModelUser",
      },
    ],
    labels: [
      {
        name: String,
        color: String,
      },
    ],
    attachments: [
      {
        url: { type: String, required: true },
        publicId: { type: String },
        type: { type: String, required: true },
        originalName: { type: String },
        uploadedBy: {
          type: mongoose.Schema.Types.ObjectId,
          refPath: "attachments.uploadedByType",
        },
        uploadedByType: {
          type: String,
          enum: ["ModelUser", "Agency"],
        },
        uploadedAt: { type: Date, default: Date.now },
      },
    ],
    comments: [
      {
        text: {
          type: String,
          required: true,
        },
        author: {
          type: mongoose.Schema.Types.ObjectId,
          refPath: "comments.authorType",
        },
        authorType: {
          type: String,
          enum: ["ModelUser", "Agency"],
        },
        createdAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    isArchived: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
);

export default mongoose.model("Task", taskSchema);
