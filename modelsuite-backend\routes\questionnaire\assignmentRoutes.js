import express from "express";
import {
  assignTemplateToModel,
  getAssignedTemplatesForModel,
  getAssignmentsForAgency,
  updateAssignmentStatus,
} from "../../controllers/questionnaire/assignmentController.js";
import { verifyToken, verifyRole } from "../../middlewares/authMiddleware.js";

const router = express.Router();

// Assign template to a model (agency)
router.post("/", verifyToken, assignTemplateToModel);
// Bulk assign templates to multiple models (agency)
router.post("/bulk", verifyToken, verifyRole("agency"), assignTemplateToModel);

// Get all assignments created by this agency
router.get("/", verifyToken, verifyRole("agency"), getAssignmentsForAgency);

// Get all templates assigned to logged-in model
router.get("/my", verifyToken, getAssignedTemplatesForModel);

// Update assignment status
router.patch("/:assignmentId/status", verifyToken, updateAssignmentStatus);

export default router;
