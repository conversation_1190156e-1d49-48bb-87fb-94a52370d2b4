import axios from "axios";
import { configDotenv } from "dotenv";
configDotenv();
// Your API key (move this to environment variable in production)
const API_KEY = process.env.SERP_API_KEY;

const getTrendsData = async (req, res) => {
  console.log("Entered the viral trending google api route");
  try {
    // Extract all query parameters from frontend
    const {
      q,
      geo = "IN",
      date = "today 12-m",
      data_type = "TIMESERIES",
      hl = "en",
      tz = "330",
    } = req.query;

    // Validate required parameter
    if (!q) {
      return res.status(400).json({
        error: "Query parameter (q) is required",
      });
    }

    // Build parameters exactly like your frontend
    const params = {
      engine: "google_trends",
      q: q,
      geo: geo,
      date: date,
      data_type: data_type,
      hl: hl,
      tz: tz,
      api_key: API_KEY,
      output: "json",
      csv: "false",
      include_low_search_volume: "true",
      no_cache: "true",
      async: "false",
    };

    // Make the API call to SerpAPI
    const response = await axios.get("https://serpapi.com/search.json", {
      params: params,
      timeout: 30000,
    });

    // Return the data exactly as SerpAPI returns it
    return res.json(response.data);
  } catch (error) {
    console.error("SerpAPI Error:", error.message);

    // Handle different error types
    if (error.response) {
      // API responded with error status

      return res.status(error.response.status).json({
        error: error.response.data?.error || "API Error",
        message: error.response.statusText,
      });
    } else if (error.code === "ECONNABORTED") {
      // Timeout error

      return res.status(408).json({
        error: "Request timeout",
        message: "Request took too long",
      });
    } else {
      // Other errors
      return res.status(500).json({
        error: "Internal server error",
        message: "Failed to fetch trends data",
      });
    }
  }
};

export default getTrendsData;
