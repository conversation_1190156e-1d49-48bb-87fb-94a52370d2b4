import SkillCategory from "../models/SkillCategory.js";
import Skill from "../models/Skill.js";
import UserSkill from "../models/UserSkill.js";
import SkillRatingAudit from "../models/SkillRatingAudit.js";
import UserProfile from "../models/UserProfile.js";
import mongoose from "mongoose";
import { asyncHandler } from "../utils/asyncHandler.js";
import { ApiError } from "../utils/ApiError.js";
import { ApiResponse } from "../utils/ApiResponse.js";

// Skill Categories Management
export const createSkillCategory = asyncHandler(async (req, res) => {
  const { name, parent_id, description, order } = req.body;

  const existingCategory = await SkillCategory.findOne({ name });
  if (existingCategory) {
    throw new ApiError(400, "Skill category already exists");
  }

  const category = await SkillCategory.create({
    name,
    parent_id,
    description,
    order,
  });

  res.status(201).json(new ApiResponse(201, category, "Skill category created successfully"));
});

export const getSkillCategories = asyncHandler(async (req, res) => {
  const categories = await SkillCategory.find({ isActive: true })
    .populate("parent_id", "name")
    .sort({ order: 1, name: 1 });

  res.status(200).json(new ApiResponse(200, categories, "Skill categories retrieved successfully"));
});

export const updateSkillCategory = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updates = req.body;

  const category = await SkillCategory.findByIdAndUpdate(id, updates, { new: true });
  if (!category) {
    throw new ApiError(404, "Skill category not found");
  }

  res.status(200).json(new ApiResponse(200, category, "Skill category updated successfully"));
});

export const deleteSkillCategory = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Check if category has skills
  const skillsCount = await Skill.countDocuments({ category_id: id, isActive: true });
  if (skillsCount > 0) {
    throw new ApiError(400, "Cannot delete category with active skills");
  }

  await SkillCategory.findByIdAndUpdate(id, { isActive: false });

  res.status(200).json(new ApiResponse(200, null, "Skill category deleted successfully"));
});

// Skills Management
export const createSkill = asyncHandler(async (req, res) => {
  const { name, category_id, type, description, weight, required_levels_by_role } = req.body;

  const skill = await Skill.create({
    name,
    category_id,
    type,
    description,
    weight,
    required_levels_by_role,
  });

  await skill.populate("category_id", "name");

  res.status(201).json(new ApiResponse(201, skill, "Skill created successfully"));
});

export const getSkills = asyncHandler(async (req, res) => {
  const { category_id, type } = req.query;
  
  const filter = { isActive: true };
  if (category_id) filter.category_id = category_id;
  if (type) filter.type = type;

  const skills = await Skill.find(filter)
    .populate("category_id", "name")
    .sort({ name: 1 });

  res.status(200).json(new ApiResponse(200, skills, "Skills retrieved successfully"));
});

export const updateSkill = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updates = req.body;

  const skill = await Skill.findByIdAndUpdate(id, updates, { new: true })
    .populate("category_id", "name");
  
  if (!skill) {
    throw new ApiError(404, "Skill not found");
  }

  res.status(200).json(new ApiResponse(200, skill, "Skill updated successfully"));
});

export const deleteSkill = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Check if skill has user ratings
  const ratingsCount = await UserSkill.countDocuments({ skill_id: id });
  if (ratingsCount > 0) {
    throw new ApiError(400, "Cannot delete skill with existing user ratings");
  }

  await Skill.findByIdAndUpdate(id, { isActive: false });

  res.status(200).json(new ApiResponse(200, null, "Skill deleted successfully"));
});

// User Skills Management
export const updateUserSkill = asyncHandler(async (req, res) => {
  const { user_id, skill_id, level, source, evidence_url, evidence_file } = req.body;
  const updated_by = req.user.id;

  // Get the old skill level for audit
  const existingUserSkill = await UserSkill.findOne({ user_id, skill_id });
  const old_level = existingUserSkill ? existingUserSkill.level : 0;

  const userSkill = await UserSkill.findOneAndUpdate(
    { user_id, skill_id },
    {
      level,
      source,
      evidence_url,
      evidence_file,
      updated_by,
      updated_at: new Date(),
    },
    { upsert: true, new: true }
  ).populate([
    { path: "skill_id", select: "name type weight" },
    { path: "user_id", select: "display_name" },
  ]);

  // Create audit log
  if (old_level !== level) {
    await SkillRatingAudit.create({
      user_skill_id: userSkill._id,
      old_level,
      new_level: level,
      rated_by: updated_by,
      rating_reason: `Updated via ${source}`,
    });
  }

  res.status(200).json(new ApiResponse(200, userSkill, "User skill updated successfully"));
});

export const getUserSkills = asyncHandler(async (req, res) => {
  const { user_id } = req.params;
  const { category_id, type } = req.query;

  const matchConditions = { user_id };
  
  const pipeline = [
    { $match: matchConditions },
    {
      $lookup: {
        from: "skills",
        localField: "skill_id",
        foreignField: "_id",
        as: "skill",
      },
    },
    { $unwind: "$skill" },
    {
      $lookup: {
        from: "skillcategories",
        localField: "skill.category_id",
        foreignField: "_id",
        as: "category",
      },
    },
    { $unwind: "$category" },
  ];

  if (category_id) {
    pipeline.push({ $match: { "skill.category_id": new mongoose.Types.ObjectId(category_id) } });
  }
  
  if (type) {
    pipeline.push({ $match: { "skill.type": type } });
  }

  pipeline.push({
    $project: {
      _id: 1,
      level: 1,
      source: 1,
      evidence_url: 1,
      evidence_file: 1,
      endorsed_by: 1,
      updated_at: 1,
      skill: {
        _id: "$skill._id",
        name: "$skill.name",
        type: "$skill.type",
        weight: "$skill.weight",
        description: "$skill.description",
      },
      category: {
        _id: "$category._id",
        name: "$category.name",
      },
    },
  });

  const userSkills = await UserSkill.aggregate(pipeline);

  res.status(200).json(new ApiResponse(200, userSkills, "User skills retrieved successfully"));
});

export const getSkillMatrix = asyncHandler(async (req, res) => {
  const { agency_id } = req.params;
  const { role, department, level_range } = req.query;

  // Get all users in the specific agency
  const userFilter = { agency_id: agency_id };
  if (role) userFilter.role = role;

  const users = await UserProfile.find(userFilter).select("_id display_name role job_title email agency_id");

  const userIds = users.map(user => user._id);

  // If no users found for this agency, return empty array
  if (users.length === 0) {
    return res.status(200).json(new ApiResponse(200, [], "No users found for this agency"));
  }

  // Get skills for all users
  const userSkills = await UserSkill.find({ user_id: { $in: userIds } })
    .populate("skill_id", "name type weight category_id")
    .populate("user_id", "display_name role job_title");

  // Group skills by user
  const skillMatrix = {};
  users.forEach(user => {
    skillMatrix[user._id] = {
      user: user,
      skills: {},
    };
  });

  userSkills.forEach(userSkill => {
    const userId = userSkill.user_id._id;
    const skillId = userSkill.skill_id._id;
    
    if (skillMatrix[userId]) {
      skillMatrix[userId].skills[skillId] = {
        skill: userSkill.skill_id,
        level: userSkill.level,
        source: userSkill.source,
        updated_at: userSkill.updated_at,
      };
    }
  });

  res.status(200).json(new ApiResponse(200, Object.values(skillMatrix), "Skill matrix retrieved successfully"));
});

export const endorseUserSkill = asyncHandler(async (req, res) => {
  const { user_skill_id } = req.params;
  const endorser_id = req.user.id;

  const userSkill = await UserSkill.findById(user_skill_id);
  if (!userSkill) {
    throw new ApiError(404, "User skill not found");
  }

  // Check if already endorsed by this user
  if (userSkill.endorsed_by.includes(endorser_id)) {
    throw new ApiError(400, "Already endorsed by this user");
  }

  userSkill.endorsed_by.push(endorser_id);
  await userSkill.save();

  res.status(200).json(new ApiResponse(200, userSkill, "Skill endorsed successfully"));
});

export const getSkillGaps = asyncHandler(async (req, res) => {
  const { user_id } = req.params;
  const { target_role } = req.query;

  const user = await UserProfile.findById(user_id);
  if (!user) {
    throw new ApiError(404, "User not found");
  }

  const currentRole = target_role || user.job_title;

  // Get all skills with required levels for the role
  const skillsWithRequirements = await Skill.find({ 
    isActive: true,
    [`required_levels_by_role.${currentRole}`]: { $exists: true, $gt: 0 }
  });

  // Get user's current skills
  const userSkills = await UserSkill.find({ user_id }).populate("skill_id");
  const userSkillMap = {};
  userSkills.forEach(us => {
    userSkillMap[us.skill_id._id] = us.level;
  });

  // Calculate gaps
  const gaps = [];
  skillsWithRequirements.forEach(skill => {
    const requiredLevel = skill.required_levels_by_role.get(currentRole);
    const currentLevel = userSkillMap[skill._id] || 0;
    
    if (currentLevel < requiredLevel) {
      gaps.push({
        skill: skill,
        current_level: currentLevel,
        required_level: requiredLevel,
        gap: requiredLevel - currentLevel,
      });
    }
  });

  // Sort by gap size (descending)
  gaps.sort((a, b) => b.gap - a.gap);

  res.status(200).json(new ApiResponse(200, gaps, "Skill gaps calculated successfully"));
});


