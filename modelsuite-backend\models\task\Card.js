import mongoose from "mongoose";

const cardSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, "Card title is required!"],
    },
    description: {
      type: String,
      default: "",
    },
    priority: {
      type: String,
      enum: ["critical", "high", "medium", "low"],
      default: "medium",
    },
    isOnHold: {
      type: Boolean,
      default: false,
    },
    onHoldReason: {
      type: String,
      default: "",
    },
    onHoldBy: {
      user: {
        type: mongoose.Schema.Types.ObjectId,
        refPath: "onHoldBy.userType",
      },
      userType: {
        type: String,
        enum: ["ModelUser", "Agency"],
      },
      timestamp: {
        type: Date,
      },
    },
    // Fields for individual tasks
    isIndividualTask: {
      type: Boolean,
      default: false,
    },
    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      refPath: "createdByRole",
    },
    createdByRole: {
      type: String,
      enum: ["ModelUser", "Agency"],
    },
    // Optional board/list reference for individual tasks
    listId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "List",
      required: function () {
        return !this.isIndividualTask;
      },
    },
    boardId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Board",
      required: function () {
        return !this.isIndividualTask;
      },
    },
    assignedTo: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "ModelUser",
      },
    ],
    dueDate: {
      type: Date,
    },
    isComplete: {
      type: Boolean,
      default: false,
    },
    position: {
      type: Number,
      default: 0,
    },
    isArchived: {
      type: Boolean,
      default: false,
    },
    attachments: [
      {
        url: { type: String, required: true },
        publicId: { type: String }, // Cloudinary public_id
        type: { type: String, required: true },
        originalName: { type: String },
        uploadedBy: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "ModelUser",
          required: true,
        },
        uploadedAt: { type: Date, default: Date.now },
      },
    ],
    labels: [
      {
        name: String,
        color: String,
      },
    ],
    comments: [
      {
        text: {
          type: String,
          required: true,
        },
        author: {
          type: mongoose.Schema.Types.ObjectId,
          refPath: "comments.authorType",
          required: true,
        },
        authorType: {
          type: String,
          required: true,
          enum: ["ModelUser", "Agency"],
        },
        createdAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
);

// Indexes for faster queries
cardSchema.index({ listId: 1, position: 1 });
cardSchema.index({ boardId: 1 });
cardSchema.index({ listId: 1, isArchived: 1 });
cardSchema.index({ assignedTo: 1 });
cardSchema.index({ dueDate: 1 });

export default mongoose.model("Card", cardSchema);
