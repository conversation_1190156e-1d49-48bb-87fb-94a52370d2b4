import { v2 as cloudinary } from "cloudinary";
import fs from "fs";
import dotenv from "dotenv";

dotenv.config();

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

/**
 * Upload file to Cloudinary
 * @param {string} localFilePath - Path to the local file
 * @param {string} folder - Cloudinary folder path
 * @param {object} options - Additional upload options
 * @returns {object|null} - Cloudinary upload result or null if failed
 */
export const uploadOnCloudinary = async (localFilePath, folder = "uploads", options = {}) => {
  try {
    if (!localFilePath) return null;

    // Upload file to Cloudinary
    const response = await cloudinary.uploader.upload(localFilePath, {
      folder: folder,
      resource_type: "auto", // Automatically detect file type
      ...options
    });

    // File uploaded successfully, remove local file
    if (fs.existsSync(localFilePath)) {
      fs.unlinkSync(localFilePath);
    }

    return response;
  } catch (error) {
    console.error("Cloudinary upload error:", error);
    
    // Remove local file if upload failed
    if (fs.existsSync(localFilePath)) {
      fs.unlinkSync(localFilePath);
    }
    
    return null;
  }
};

/**
 * Delete file from Cloudinary
 * @param {string} publicId - Cloudinary public ID of the file
 * @param {string} resourceType - Type of resource (image, video, raw)
 * @returns {object|null} - Cloudinary deletion result or null if failed
 */
export const deleteFromCloudinary = async (publicId, resourceType = "image") => {
  try {
    if (!publicId) return null;

    const response = await cloudinary.uploader.destroy(publicId, {
      resource_type: resourceType
    });

    return response;
  } catch (error) {
    console.error("Cloudinary deletion error:", error);
    return null;
  }
};

/**
 * Generate a signed URL for secure file access
 * @param {string} publicId - Cloudinary public ID
 * @param {object} options - Transformation and other options
 * @returns {string} - Signed URL
 */
export const generateSignedUrl = (publicId, options = {}) => {
  try {
    return cloudinary.url(publicId, {
      sign_url: true,
      type: "authenticated",
      ...options
    });
  } catch (error) {
    console.error("Cloudinary signed URL generation error:", error);
    return null;
  }
};

/**
 * Get file details from Cloudinary
 * @param {string} publicId - Cloudinary public ID
 * @param {string} resourceType - Type of resource
 * @returns {object|null} - File details or null if failed
 */
export const getFileDetails = async (publicId, resourceType = "image") => {
  try {
    const response = await cloudinary.api.resource(publicId, {
      resource_type: resourceType
    });
    
    return response;
  } catch (error) {
    console.error("Cloudinary get file details error:", error);
    return null;
  }
};

export default cloudinary;
