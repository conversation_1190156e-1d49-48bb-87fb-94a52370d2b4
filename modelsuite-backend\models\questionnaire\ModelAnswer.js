import mongoose from "mongoose";

// One answer to a question
const answerSchema = new mongoose.Schema({
  questionId: { type: mongoose.Schema.Types.ObjectId, required: true }, // refers to question inside Template
  answer: mongoose.Schema.Types.Mixed,
});

// Stores a model's answers to a specific template
const modelAnswerSchema = new mongoose.Schema({
  modelId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "ModelUser",
    required: true,
  },
  templateId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Template",
    required: true,
  },
  answers: [answerSchema],
  submittedAt: { type: Date, default: Date.now },
});

export default mongoose.model("ModelAnswer", modelAnswerSchema);
