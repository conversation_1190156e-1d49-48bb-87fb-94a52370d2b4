version: "3.8"
services:
  backend:
    build: ./modelsuite-backend
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=development
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
      - PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
    env_file:
      - ./modelsuite-backend/.env
    volumes:
      - ./modelsuite-backend/uploads:/app/uploads
      - ./modelsuite-backend:/app # For development hot reload
      - /app/node_modules # Prevent node_modules override
    restart: unless-stopped
    command: npm run dev # Use dev for hot reload

  frontend:
    build:
      context: ./modelsuite-frontend
      dockerfile: Dockerfile
    ports:
      - "4000:4000"
    env_file:
      - ./modelsuite-frontend/.env
    volumes:
      - ./modelsuite-frontend:/app
      - /app/node_modules
    restart: unless-stopped
    command: npm run dev

  mongo:
    image: mongo:6
    restart: always
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password

volumes:
  mongo-data:
