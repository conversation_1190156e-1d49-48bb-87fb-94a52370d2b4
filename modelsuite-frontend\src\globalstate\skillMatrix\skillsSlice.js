import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { skillService } from "../../utils/skillMatrixAPI";

// Async thunks for skills
export const fetchSkills = createAsyncThunk(
  "skills/fetchSkills",
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await skillService.getAll(params);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to fetch skills",
      );
    }
  },
);

export const createSkill = createAsyncThunk(
  "skills/createSkill",
  async (skillData, { rejectWithValue }) => {
    try {
      const response = await skillService.create(skillData);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to create skill",
      );
    }
  },
);

export const updateSkill = createAsyncThunk(
  "skills/updateSkill",
  async ({ id, ...skillData }, { rejectWithValue }) => {
    try {
      const response = await skillService.update(id, skillData);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to update skill",
      );
    }
  },
);

export const deleteSkill = createAsyncThunk(
  "skills/deleteSkill",
  async (skillId, { rejectWithValue }) => {
    try {
      await skillService.delete(skillId);
      return skillId;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to delete skill",
      );
    }
  },
);

const initialState = {
  list: [],
  loading: false,
  error: null,
  searchTerm: "",
};

const skillsSlice = createSlice({
  name: "skills",
  initialState,
  reducers: {
    setSearchTerm: (state, action) => {
      state.searchTerm = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch skills
      .addCase(fetchSkills.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSkills.fulfilled, (state, action) => {
        state.loading = false;
        state.list = action.payload;
      })
      .addCase(fetchSkills.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Create skill
      .addCase(createSkill.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createSkill.fulfilled, (state, action) => {
        state.loading = false;
        state.list.push(action.payload);
      })
      .addCase(createSkill.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update skill
      .addCase(updateSkill.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateSkill.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.list.findIndex(
          (skill) => skill._id === action.payload._id,
        );
        if (index !== -1) {
          state.list[index] = action.payload;
        }
      })
      .addCase(updateSkill.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete skill
      .addCase(deleteSkill.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteSkill.fulfilled, (state, action) => {
        state.loading = false;
        state.list = state.list.filter((skill) => skill._id !== action.payload);
      })
      .addCase(deleteSkill.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { setSearchTerm, clearError } = skillsSlice.actions;

// Selectors
export const selectSkills = (state) => state.skills.list;
export const selectSkillsLoading = (state) => state.skills.loading;
export const selectSkillsError = (state) => state.skills.error;
export const selectSkillsSearchTerm = (state) => state.skills.searchTerm;

// Filtered selectors
export const selectFilteredSkills = (state) => {
  const { list, searchTerm } = state.skills;
  if (!searchTerm) return list;

  return list.filter(
    (skill) =>
      skill.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      skill.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      skill.category_id?.name?.toLowerCase().includes(searchTerm.toLowerCase()),
  );
};

export default skillsSlice.reducer;
