import mongoose from "mongoose";

/**
 * Check if a value is a valid MongoDB ObjectId.
  @param {string} id
  @returns {boolean}
 */
export const isValidObjectId = (id) => {
  return mongoose.Types.ObjectId.isValid(id);
};

/**
 * Check if a value is a non-empty string.
 * @param {any} value
 * @returns {boolean}
 */
export const isNonEmptyString = (value) => {
  return typeof value === "string" && value.trim().length > 0;
};

/**
 * Check if an array is valid and has at least one item.
 * @param {any} arr
 * @returns {boolean}
 */
export const isValidArray = (arr) => {
  return Array.isArray(arr) && arr.length > 0;
};
