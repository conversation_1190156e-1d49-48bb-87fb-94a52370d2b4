import DmMessage from "../../models/messanger/dmMessages.js";

const handleDmSockets = (io, socket, connectedUsers) => {
  socket.on("dm:addNewConversation", (data) => {
    const targetedUserSocket = connectedUsers.get(data.targetUserId);
    io.to(targetedUserSocket).emit("dm:add_incoming_dm", data.dmConvo);
  });

  socket.on("dm:send_message", async (messageData) => {
    try {
      // Create and save message to DB
      const newMessage = await DmMessage.create({
        _id: messageData._id,
        convoId: messageData.convoId,
        senderId: messageData.senderId,
        receiverId: messageData.receiverId,
        text: messageData.text,
        type: messageData.type,
        status: "sent",
        attachments: messageData.attachments,
        reactions: messageData.reactions,
        replyTo: messageData.replyTo,
        createdAt: messageData.createdAt,
      });

      // Prepare message to send back
      const messageToSend = newMessage.toObject();

      // get receiver's socket id
      const receiverSocketId = connectedUsers.get(
        messageData.receiverId.toString(),
      );

      // emit only if online
      if (receiverSocketId) {
        io.to(receiverSocketId).emit("dm:new_message", messageToSend);
      }

      // also emit back to sender so sender's UI updates immediately
      const senderSocketId = connectedUsers.get(
        messageData.senderId.toString(),
      );
      if (senderSocketId) {
        io.to(senderSocketId).emit("dm:message_sent", messageToSend);
      }
    } catch (err) {
      console.error("Error saving DM message:", err);
    }
  });

  socket.on("dm:typing", ({ convoId, userId, state }) => {
    socket.broadcast.emit("dm:set_typing", {
      convoId,
      userId, // who is typing
      state, // true or false
    });
  });

  // For batching seen updates
  const seenMessageBuffer = new Map(); // userId => [messageId]
  const seenTimers = new Map(); // userId => timeoutId

  socket.on("dm:message_seen", async ({ convoId, messageId, userId }) => {
    try {
      const message = await DmMessage.findById(messageId);

      if (!message) return;

      const senderId = message.senderId?.toString();

      // Don't notify self
      if (senderId && senderId !== userId) {
        const senderSocketId = connectedUsers.get(senderId);
        if (senderSocketId) {
          io.to(senderSocketId).emit("dm:message_seen", {
            convoId,
            messageId,
          });
        }
      }

      // Batch update buffer
      if (!seenMessageBuffer.has(userId)) {
        seenMessageBuffer.set(userId, []);
      }
      seenMessageBuffer.get(userId).push(messageId);

      // Reset debounce timer
      if (seenTimers.has(userId)) {
        clearTimeout(seenTimers.get(userId));
      }

      const timer = setTimeout(async () => {
        const messageIds = seenMessageBuffer.get(userId);
        if (messageIds && messageIds.length > 0) {
          await DmMessage.updateMany(
            { _id: { $in: messageIds } },
            { $set: { status: "seen" } },
          );

          seenMessageBuffer.delete(userId);
          seenTimers.delete(userId);
        }
      }, 2000);

      seenTimers.set(userId, timer);
    } catch (err) {
      console.error("❌ Error in dm:message_seen:", err);
    }
  });

  socket.on("dm:add_reaction", async ({ convoId, messageId, data }) => {
    try {
      const { userId, avatar, fullName, emoji } = data;

      // Step 1: Remove existing reaction by this user (regardless of emoji)
      await DmMessage.findByIdAndUpdate(messageId, {
        $pull: {
          reactions: { userId },
        },
      });

      // Step 2: Add the new reaction
      const updatedMessage = await DmMessage.findByIdAndUpdate(
        messageId,
        {
          $push: {
            reactions: { userId, avatar, fullName, emoji },
          },
        },
        { new: true },
      );

      if (!updatedMessage) return;

      // Step 3: Emit to receiver only
      const receiverId =
        updatedMessage.senderId.toString() === userId.toString()
          ? updatedMessage.receiverId.toString()
          : updatedMessage.senderId.toString();

      const receiverSocketId = connectedUsers.get(receiverId);

      if (receiverSocketId) {
        io.to(receiverSocketId).emit("dm:new_reaction", {
          convoId,
          messageId,
          reactions: updatedMessage.reactions,
        });
      }
    } catch (err) {
      console.error("Error in dm:add_reaction:", err.message);
    }
  });

  socket.on("dm:remove_reaction", async ({ convoId, messageId, userId }) => {
    try {
      // Step 1: Remove user's reaction from message
      const updatedMessage = await DmMessage.findByIdAndUpdate(
        messageId,
        {
          $pull: { reactions: { userId } },
        },
        { new: true },
      );

      if (!updatedMessage) return;

      // Step 2: Identify receiver
      const receiverId =
        updatedMessage.senderId.toString() === userId.toString()
          ? updatedMessage.receiverId.toString()
          : updatedMessage.senderId.toString();

      const receiverSocketId = connectedUsers.get(receiverId);

      // Step 3: Emit to receiver only
      if (receiverSocketId) {
        io.to(receiverSocketId).emit("dm:removed_reaction", {
          convoId,
          messageId,
          reactions: updatedMessage.reactions,
        });
      }
    } catch (err) {
      console.error("Error in dm:remove_reaction:", err.message);
    }
  });

  socket.on("dm:edit_message", async (data) => {
    const { convoId, messageId, receiverId, newText } = data;

    try {
      const currentMsg = await DmMessage.findOne({ _id: messageId, convoId });

      if (!currentMsg) {
        socket.emit("dm:edit_failed", {
          messageId,
          reason: "Message not found",
        });
        return;
      }

      // Determine new type based on updated text and existing attachments
      const hasText = newText.trim().length > 0;
      const hasAttachments =
        currentMsg.attachments && currentMsg.attachments.length > 0;

      let newType = "textonly";
      if (hasText && hasAttachments) newType = "mixed";
      else if (!hasText && hasAttachments) newType = "attachmentonly";
      else if (hasText && !hasAttachments) newType = "textonly";

      const updatedMsg = await DmMessage.findOneAndUpdate(
        { _id: messageId, convoId },
        {
          text: newText,
          edited: true,
          type: newType,
        },
        { new: true },
      );

      if (!updatedMsg) {
        socket.emit("dm:edit_failed", { messageId, reason: "Update failed" });
        return;
      }

      // Emit to receiver if online
      const receiverSocketId = connectedUsers.get(receiverId.toString());
      if (receiverSocketId) {
        io.to(receiverSocketId).emit("dm:message_edited", {
          messageId,
          convoId,
          newText,
          type: newType,
        });
      }

      // Emit to sender as well for local UI sync
      socket.emit("dm:message_edited", {
        messageId,
        convoId,
        newText,
        type: newType,
      });
    } catch (err) {
      console.error("Error editing message:", err);
      socket.emit("dm:edit_failed", { messageId, reason: "Server error" });
    }
  });

  socket.on(
    "dm:update_message_pin",
    async ({ convoId, messageId, receiverId, pinned }) => {
      try {
        const updatedMsg = await DmMessage.findOneAndUpdate(
          { _id: messageId, convoId },
          { pinned: pinned },
          { new: true },
        );

        const receiverSocketId = connectedUsers.get(receiverId);

        if (receiverSocketId) {
          io.to(receiverSocketId).emit("dm:updated_message_pin", {
            convoId,
            messageId,
            pinned,
          });
        }
      } catch (err) {
        console.err("an error occured while updating pin");
      }
    },
  );

  socket.on(
    "dm:delete_message",
    async ({ userId, receiverId, messageId, convoId, typeOfDelete }) => {
      try {
        const message = await DmMessage.findOne({ _id: messageId, convoId });
        if (!message) return;

        if (typeOfDelete === "forMe") {
          if (!message.deletedFor.includes(userId)) {
            message.deletedFor.push(userId);
          }
        } else if (typeOfDelete === "forAll") {
          //  Check if the requester is sender
          if (message.senderId.toString() !== userId.toString()) return;

          message.deletedFor = ["everyone"];

          // Emit update to receiver
          const receiverSocketId = connectedUsers.get(receiverId);

          io.to(receiverSocketId).emit("dm:message_deleted", {
            messageId,
            convoId,
            typeOfDelete,
            userId,
          });
        }

        await message.save();
      } catch (error) {
        console.error("Error deleting message:", error.message);
      }
    },
  );
};

export default handleDmSockets;
