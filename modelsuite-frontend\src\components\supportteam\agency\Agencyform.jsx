// import React, { useState } from "react";

// // --- Helper Components & Data ---
//   const user = JSON.parse(localStorage.getItem("auth"))?.user;
//   const token = JSON.parse(localStorage.getItem("auth"))?.token;
// // Dummy data for select inputs
// const dummyData = {
//   supportContacts: [
//     { id: 1, name: "<PERSON>", title: "Lead Advisor", timezone: "GMT-5" },
//     { id: 2, name: "<PERSON>", title: "Client Success", timezone: "GMT+1" },
//     { id: 3, name: "<PERSON>", title: "Technical Expert", timezone: "GMT+9" },
//   ],
//   internalDepts: [
//     "Retention Team",
//     "Onboarding Team",
//     "Technical Support",
//     "Creative Services",
//   ],
//   supportAreas: [
//     "Account Management",
//     "Content Strategy",
//     "Technical Help",
//     "Billing Inquiry",
//     "Creative Direction",
//   ],
//   languages: ["English", "Spanish", "French", "German", "Mandarin"],
//   commChannels: ["Platform Chat", "Video Call", "Email", "Phone"],
// };
// import Select from "react-select";

// const languageOptions = dummyData.languages.map((lang) => ({
//   label: lang,
//   value: lang,
// }));

// // A reusable component for form sections to keep the main component clean
// const FormSection = ({ title, number, children }) => (
//   <div className="space-y-6">
//     <h2 className="text-xl font-semibold text-[#ccd6f6] border-b border-gray-700 pb-2">
//       {number}. {title}
//     </h2>
//     {children}
//   </div>
// );

// // A reusable component for input fields
// const InputField = ({ label, id, children, description }) => (
//   <div>
//     <label
//       htmlFor={id}
//       className="block text-sm font-medium text-[#8892b0] mb-1"
//     >
//       {label}
//     </label>
//     {children}
//     {description && (
//       <p className="text-xs text-[#8892b0] mt-1">{description}</p>
//     )}
//   </div>
// );

// // --- Main Component ---

// function AssignContactModal() {
//   const [modelid, setModelid] = useState(user._id);
//   // State to manage the working hours schedule
//   const [schedule, setSchedule] = useState([
//     { day: "Monday", enabled: true, start: "09:00", end: "18:00" },
//     { day: "Tuesday", enabled: true, start: "09:00", end: "18:00" },
//     { day: "Wednesday", enabled: true, start: "09:00", end: "18:00" },
//     { day: "Thursday", enabled: true, start: "09:00", end: "18:00" },
//     { day: "Friday", enabled: true, start: "09:00", end: "17:00" },
//     { day: "Saturday", enabled: false, start: "10:00", end: "15:00" },
//     { day: "Sunday", enabled: false, start: "10:00", end: "15:00" },
//   ]);

//   // Handler to update the schedule state
//   const handleScheduleChange = (index, field, value) => {
//     const newSchedule = [...schedule];
//     newSchedule[index][field] = value;
//     setSchedule(newSchedule);
//   };

//   // A generic class for all input-like elements for consistency
//   const inputStyles =
//     "w-full bg-gray-900 border border-gray-600 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 text-[#ccd6f6]";

//   return (
//     // You would typically have a parent controlling the visibility of this modal
//     // For this example, we render it directly in a styled body.
//     <div className="bg-gray-800 flex items-center justify-center min-h-screen p-4">
//       <div className="bg-[#0A192F] text-white w-full max-w-4xl rounded-lg shadow-2xl p-8 m-4">
//         {/* Modal Header */}
//         <div className="flex justify-between items-center mb-6 pb-4 border-b border-gray-700">
//           <h1 className="text-2xl font-bold text-[#ccd6f6]">
//             Assign Support Contact
//           </h1>
//           <button
//             onClick={() => {
//               /* Add close logic here */
//             }}
//             className="text-[#8892b0] hover:text-white text-3xl font-light"
//           >
//             ×
//           </button>
//         </div>

//         <form onSubmit={(e) => e.preventDefault()} className="space-y-8">
//           {/* --- Section 1: Assignment Details --- */}
//           <FormSection number="1" title="Assignment Details">
//             <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//               {/* Model Selection */}
//               <InputField label="Model Selection *" id="model-selection">
//                 <input
//                   type="text"
//                   id="model-selection"
//                   value={modelid}
//                   // placeholder="Search by name or ID (e.g., Jane Doe, #12345)"
//                   className={inputStyles}
//                   readOnly
//                 />
//               </InputField>

//               {/* Support Contact Selection */}
//               <InputField
//                 label="Support Contact Selection *"
//                 id="support-contact"
//               >
//                 <select id="support-contact" className={inputStyles}>
//                   <option disabled selected>
//                     Select a contact...
//                   </option>
//                   {dummyData.supportContacts.map((c) => (
//                     <option
//                       key={c.id}
//                     >{`${c.name} (${c.title}, ${c.timezone})`}</option>
//                   ))}
//                 </select>
//               </InputField>

//               {/* Custom Role Title */}
//               <InputField label="Custom Role Title *" id="custom-role">
//                 <input
//                   type="text"
//                   id="custom-role"
//                   placeholder="e.g., Senior Account Coach"
//                   className={inputStyles}
//                 />
//               </InputField>

//               {/* Internal Department */}
//               <InputField
//                 label="Internal Department (Optional)"
//                 id="internal-dept"
//               >
//                 <select id="internal-dept" className={inputStyles}>
//                   <option disabled selected>
//                     Select a department...
//                   </option>
//                   {dummyData.internalDepts.map((dept) => (
//                     <option key={dept}>{dept}</option>
//                   ))}
//                 </select>
//               </InputField>

//               {/* Short Bio / Description */}
//               <div className="md:col-span-2">
//                 <InputField
//                   label="Short Bio / Description"
//                   id="short-bio"
//                   description="300 character limit"
//                 >
//                   <textarea
//                     id="short-bio"
//                     rows="3"
//                     maxLength="300"
//                     placeholder="A brief, friendly bio displayed on the model's dashboard."
//                     className={inputStyles}
//                   ></textarea>
//                 </InputField>
//               </div>

//               {/* Support Areas (Tags) */}
//               <div className="md:col-span-2">
//                 <InputField
//                   label="Support Areas (Tags)"
//                   id="support-areas"
//                   description="Hold Ctrl/Cmd to select multiple."
//                 >
//                   <select
//                     multiple
//                     id="support-areas"
//                     className={`${inputStyles} h-32`}
//                   >
//                     {dummyData.supportAreas.map((area) => (
//                       <option key={area}>{area}</option>
//                     ))}
//                   </select>
//                 </InputField>
//               </div>
//             </div>
//           </FormSection>

//           {/* --- Section 2: Availability & Localization --- */}
//           <FormSection number="2" title="Availability & Localization">
//             <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-6">
//               {/* Working Hours */}
//               <div className="md:col-span-2">
//                 <label className="block text-sm font-medium text-[#8892b0] mb-2">
//                   Working Hours
//                 </label>
//                 <div className="space-y-3">
//                   {schedule.map((item, index) => (
//                     <div
//                       key={item.day}
//                       className="grid grid-cols-3 md:grid-cols-4 items-center gap-2 text-sm"
//                     >
//                       <label className="flex items-center space-x-2 text-[#8892b0] md:col-span-2">
//                         <input
//                           type="checkbox"
//                           checked={item.enabled}
//                           onChange={(e) =>
//                             handleScheduleChange(
//                               index,
//                               "enabled",
//                               e.target.checked,
//                             )
//                           }
//                           className="h-4 w-4 rounded bg-gray-700 border-gray-600 text-cyan-600 focus:ring-cyan-500"
//                         />
//                         <span>{item.day}</span>
//                       </label>
//                       <input
//                         type="time"
//                         value={item.start}
//                         disabled={!item.enabled}
//                         onChange={(e) =>
//                           handleScheduleChange(index, "start", e.target.value)
//                         }
//                         className={`${inputStyles} py-1 text-sm ${!item.enabled ? "bg-gray-700 text-gray-400" : ""}`}
//                       />
//                       <input
//                         type="time"
//                         value={item.end}
//                         disabled={!item.enabled}
//                         onChange={(e) =>
//                           handleScheduleChange(index, "end", e.target.value)
//                         }
//                         className={`${inputStyles} py-1 text-sm ${!item.enabled ? "bg-gray-700 text-gray-400" : ""}`}
//                       />
//                     </div>
//                   ))}
//                 </div>
//               </div>

//               {/* Timezone */}
//               <InputField label="Timezone" id="timezone">
//                 <select id="timezone" className={inputStyles}>
//                   <option>Auto-detected (UTC-5:00)</option>
//                   <option>UTC-8:00 Pacific Time</option>
//                   <option>UTC-5:00 Eastern Time</option>
//                   <option>UTC+1:00 Central European Time</option>
//                 </select>
//               </InputField>

//               {/* Preferred Communication Channel */}
//               <InputField
//                 label="Preferred Communication Channel"
//                 id="comm-channel"
//               >
//                 <select id="comm-channel" className={inputStyles}>
//                   <option>No preference</option>
//                   {dummyData.commChannels.map((ch) => (
//                     <option key={ch}>{ch}</option>
//                   ))}
//                 </select>
//               </InputField>

//               {/* Languages Spoken */}

//               <div className="md:col-span-2">
//                 <InputField
//                   label="Languages Spoken"
//                   id="languages"
//                   description="Search and select multiple languages."
//                 >
//                   <Select
//                     id="languages"
//                     isMulti
//                     options={languageOptions}
//                     className="text-black"
//                     classNamePrefix="select"
//                     styles={{
//                       control: (base) => ({
//                         ...base,
//                         backgroundColor: "#1e293b", // bluish-black (Tailwind: bg-[#8892b0]-800)
//                         borderColor: "#334155", // Tailwind: border-[#8892b0]-700
//                         color: "#fff",
//                       }),
//                       menu: (base) => ({
//                         ...base,
//                         backgroundColor: "#1e293b",
//                         color: "#fff",
//                       }),
//                       option: (base, { isFocused, isSelected }) => ({
//                         ...base,
//                         backgroundColor: isSelected
//                           ? "#0ea5e9" // selected: cyan
//                           : isFocused
//                             ? "#334155" // hover: [#8892b0]
//                             : "#1e293b", // default
//                         color: "#fff",
//                       }),
//                       multiValue: (base) => ({
//                         ...base,
//                         backgroundColor: "#0ea5e9", // tag color
//                         color: "#000",
//                       }),
//                       multiValueLabel: (base) => ({
//                         ...base,
//                         color: "#000",
//                       }),
//                       input: (base) => ({
//                         ...base,
//                         color: "#fff",
//                       }),
//                       placeholder: (base) => ({
//                         ...base,
//                         color: "#94a3b8", // Tailwind [#8892b0]-400
//                       }),
//                     }}
//                   />
//                 </InputField>
//               </div>
//             </div>
//           </FormSection>

//           {/* --- Section 3: Assignment Settings (Optional) --- */}
//           {/* <FormSection number="3" title="Assignment Settings (Optional)">
//                      <div className="space-y-4">
//                          <label className="flex items-center space-x-3 cursor-pointer">
//                             <input type="checkbox" className="h-5 w-5 rounded bg-gray-700 border-gray-600 text-cyan-600 focus:ring-cyan-500"/>
//                             <span className="text-[#8892b0]">Automatically create a chat thread if one does not exist</span>
//                         </label>
//                         <label className="flex items-center space-x-3 cursor-pointer">
//                             <input type="checkbox" className="h-5 w-5 rounded bg-gray-700 border-gray-600 text-cyan-600 focus:ring-cyan-500"/>
//                             <span className="text-[#8892b0]">Send a welcome message immediately after assignment</span>
//                         </label>
//                         <label className="flex items-center space-x-3 cursor-pointer">
//                             <input type="checkbox" className="h-5 w-5 rounded bg-gray-700 border-gray-600 text-cyan-600 focus:ring-cyan-500"/>
//                             <span className="text-[#8892b0]">Assign onboarding checklist or other tasks</span>
//                         </label>

//                         <InputField label="Internal Reporting Tags" id="internal-tags">
//                              <input type="text" id="internal-tags" placeholder="e.g., Onboarding Phase, Creative Coach" className={inputStyles} />
//                         </InputField>
//                      </div>
//                 </FormSection> */}

//           {/* --- Action Buttons --- */}
//           <div className="flex flex-col-reverse md:flex-row md:items-center gap-3 pt-6 border-t border-gray-700">
//             <button
//               type="button"
//               onClick={() => window.location.reload()}
//               className="w-full md:w-auto md:mr-auto text-[#8892b0] hover:text-white py-2 px-4 transition-colors"
//             >
//               Cancel
//             </button>
//             {/* <button type="button" className="w-full md:w-auto inline-flex justify-center rounded-md border border-gray-600 bg-gray-800 py-2 px-4 text-sm font-medium text-[#ccd6f6] shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-2 focus:ring-offset-[#0A192F] transition-colors">
//               Assign + Send Welcome Message
//             </button> */}
//             <button
//               type="submit"
//               className="w-full md:w-auto inline-flex justify-center rounded-md border border-transparent bg-cyan-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-2 focus:ring-offset-[#0A192F] transition-colors"
//             >
//               Assign The Employee
//             </button>
//           </div>
//         </form>
//       </div>
//     </div>
//   );
// }
// const Agencyform = () => {

//   return (
//     <div>
//       <AssignContactModal />
//     </div>
//   );
// };

// export default Agencyform;

import React, { useState } from "react";
import Select from "react-select";

const user = JSON.parse(localStorage.getItem("auth"))?.user;
// const token = JSON.parse(localStorage.getItem("auth"))?.token;

const dummyData = {
  supportContacts: [
    { id: 1, name: "John Smith", title: "Lead Advisor", timezone: "GMT-5" },
    { id: 2, name: "Maria Garcia", title: "Client Success", timezone: "GMT+1" },
    { id: 3, name: "Ken Tanaka", title: "Technical Expert", timezone: "GMT+9" },
  ],
  internalDepts: [
    "Retention Team",
    "Onboarding Team",
    "Technical Support",
    "Creative Services",
  ],
  supportAreas: [
    "Account Management",
    "Content Strategy",
    "Technical Help",
    "Billing Inquiry",
    "Creative Direction",
  ],
  languages: ["English", "Spanish", "French", "German", "Mandarin"],
  commChannels: ["Platform Chat", "Video Call", "Email", "Phone"],
};

const languageOptions = dummyData.languages.map((lang) => ({
  label: lang,
  value: lang,
}));
const supportAreaOptions = dummyData.supportAreas.map((area) => ({
  label: area,
  value: area,
}));

const FormSection = ({ title, number, children }) => (
  <div className="space-y-6">
    <h2 className="text-xl font-semibold text-[#ccd6f6] border-b border-gray-700 pb-2">
      {number}. {title}
    </h2>
    {children}
  </div>
);

const InputField = ({ label, id, children, description }) => (
  <div>
    <label
      htmlFor={id}
      className="block text-sm font-medium text-[#8892b0] mb-1"
    >
      {label}
    </label>
    {children}
    {description && (
      <p className="text-xs text-[#8892b0] mt-1">{description}</p>
    )}
  </div>
);

function AssignContactModal() {
  const [modelid] = useState(user._id);
  const [selectedLanguages, setSelectedLanguages] = useState([]);
  const [supportareas, setSupportareas] = useState([]);
  const [schedule, setSchedule] = useState([
    { day: "Monday", enabled: true, start: "09:00", end: "18:00" },
    { day: "Tuesday", enabled: true, start: "09:00", end: "18:00" },
    { day: "Wednesday", enabled: true, start: "09:00", end: "18:00" },
    { day: "Thursday", enabled: true, start: "09:00", end: "18:00" },
    { day: "Friday", enabled: true, start: "09:00", end: "17:00" },
    { day: "Saturday", enabled: false, start: "10:00", end: "15:00" },
    { day: "Sunday", enabled: false, start: "10:00", end: "15:00" },
  ]);

  const inputStyles =
    "w-full bg-gray-900 border border-gray-600 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 text-[#ccd6f6]";

  const handleScheduleChange = (index, field, value) => {
    const newSchedule = [...schedule];
    newSchedule[index][field] = value;
    setSchedule(newSchedule);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const plainData = Object.fromEntries(formData.entries());

    const finalPayload = {
      ...plainData,
      modelid,
      supportareas: supportareas.map((area) => area.value),
      languages: selectedLanguages.map((lang) => lang.value),
      schedule: schedule.filter((item) => item.enabled),
    };

    console.log("Form Submitted Data:", finalPayload);
  };

  return (
    <div className="bg-gray-800 flex items-center justify-center min-h-screen p-4">
      <div className="bg-[#0A192F] text-white w-full max-w-4xl rounded-lg shadow-2xl p-8 m-4">
        <div className="flex justify-between items-center mb-6 pb-4 border-b border-gray-700">
          <h1 className="text-2xl font-bold text-[#ccd6f6]">
            Assign Support Contact
          </h1>
          <button
            onClick={() => window.location.reload()}
            className="text-[#8892b0] hover:text-white text-3xl font-light"
          >
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          <FormSection number="1" title="Assignment Details">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <InputField label="Model Selection *" id="model-selection">
                <input
                  type="text"
                  name="modelid"
                  value={modelid}
                  readOnly
                  className={inputStyles}
                />
              </InputField>

              <InputField
                label="Support Contact Selection *"
                id="support-contact"
              >
                <select name="supportContact" className={inputStyles} required>
                  <option value="" disabled selected>
                    Select a contact...
                  </option>
                  {dummyData.supportContacts.map((c) => (
                    <option
                      key={c.id}
                      value={c.name}
                    >{`${c.name} (${c.title}, ${c.timezone})`}</option>
                  ))}
                </select>
              </InputField>

              <InputField label="Custom Role Title *" id="custom-role">
                <input
                  type="text"
                  name="customRole"
                  placeholder="e.g., Senior Account Coach"
                  className={inputStyles}
                  required
                />
              </InputField>

              <InputField
                label="Internal Department (Optional)"
                id="internal-dept"
              >
                <select name="internalDept" className={inputStyles}>
                  <option value="" disabled selected>
                    Select a department...
                  </option>
                  {dummyData.internalDepts.map((dept) => (
                    <option key={dept}>{dept}</option>
                  ))}
                </select>
              </InputField>

              <div className="md:col-span-2">
                <InputField
                  label="Short Bio / Description"
                  id="short-bio"
                  description="300 character limit"
                >
                  <textarea
                    name="bio"
                    rows="3"
                    maxLength="300"
                    placeholder="A brief, friendly bio"
                    className={inputStyles}
                  ></textarea>
                </InputField>
              </div>

              <div className="md:col-span-2">
                <InputField
                  label="Support Areas (Tags)"
                  id="support-areas"
                  description="Hold Ctrl/Cmd to select multiple."
                >
                  <Select
                    isMulti
                    options={supportAreaOptions}
                    value={supportareas}
                    onChange={setSupportareas}
                    className="text-black"
                    classNamePrefix="select"
                  />
                </InputField>
              </div>
            </div>
          </FormSection>

          <FormSection number="2" title="Availability & Localization">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-6">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-[#8892b0] mb-2">
                  Working Hours
                </label>
                <div className="space-y-3">
                  {schedule.map((item, index) => (
                    <div
                      key={item.day}
                      className="grid grid-cols-3 md:grid-cols-4 items-center gap-2 text-sm"
                    >
                      <label className="flex items-center space-x-2 text-[#8892b0] md:col-span-2">
                        <input
                          type="checkbox"
                          checked={item.enabled}
                          onChange={(e) =>
                            handleScheduleChange(
                              index,
                              "enabled",
                              e.target.checked,
                            )
                          }
                          className="h-4 w-4 rounded bg-gray-700 border-gray-600 text-cyan-600 focus:ring-cyan-500"
                        />
                        <span>{item.day}</span>
                      </label>
                      <input
                        type="time"
                        value={item.start}
                        disabled={!item.enabled}
                        onChange={(e) =>
                          handleScheduleChange(index, "start", e.target.value)
                        }
                        className={`${inputStyles} py-1 text-sm ${!item.enabled ? "bg-gray-700 text-gray-400" : ""}`}
                      />
                      <input
                        type="time"
                        value={item.end}
                        disabled={!item.enabled}
                        onChange={(e) =>
                          handleScheduleChange(index, "end", e.target.value)
                        }
                        className={`${inputStyles} py-1 text-sm ${!item.enabled ? "bg-gray-700 text-gray-400" : ""}`}
                      />
                    </div>
                  ))}
                </div>
              </div>

              <InputField label="Timezone" id="timezone">
                <select name="timezone" className={inputStyles}>
                  <option>Auto-detected (UTC-5:00)</option>
                  <option>UTC-8:00 Pacific Time</option>
                  <option>UTC-5:00 Eastern Time</option>
                  <option>UTC+1:00 Central European Time</option>
                </select>
              </InputField>

              <InputField
                label="Preferred Communication Channel"
                id="comm-channel"
              >
                <select name="commChannel" className={inputStyles}>
                  <option>No preference</option>
                  {dummyData.commChannels.map((ch) => (
                    <option key={ch}>{ch}</option>
                  ))}
                </select>
              </InputField>

              <div className="md:col-span-2">
                <InputField
                  label="Languages Spoken"
                  id="languages"
                  description="Search and select multiple languages."
                >
                  <Select
                    isMulti
                    options={languageOptions}
                    value={selectedLanguages}
                    onChange={setSelectedLanguages}
                    className="text-black"
                    classNamePrefix="select"
                  />
                </InputField>
              </div>
            </div>
          </FormSection>

          <div className="flex flex-col-reverse md:flex-row md:items-center gap-3 pt-6 border-t border-gray-700">
            <button
              type="button"
              onClick={() => window.location.reload()}
              className="w-full md:w-auto md:mr-auto text-[#8892b0] hover:text-white py-2 px-4 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="w-full md:w-auto inline-flex justify-center rounded-md border border-transparent bg-cyan-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-2 focus:ring-offset-[#0A192F] transition-colors"
            >
              Assign The Employee
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

const Agencyform = () => {
  return (
    <div>
      <AssignContactModal />
    </div>
  );
};

export default Agencyform;
