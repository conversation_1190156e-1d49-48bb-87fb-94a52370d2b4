import Agency from "../models/agency.js";
import AgencyToModelMessage from "../models/AgencytoModelMessage.js";
import ModelUser from "../models/model.js";

const handleAgencyToModelSocket = (io, socket, connectedUsers) => {
  socket.on("fetch_isUserOnline", async ({ userId }) => {
    try {
      const isOnline = connectedUsers.has(userId);
      let lastOnline = null;

      if (!isOnline) {
        const user = await ModelUser.findById(userId).select("lastOnline");
        if (user) {
          lastOnline = user.lastOnline;
        } else {
          const agency = await Agency.findById(userId).select("lastOnline");
          if (agency) {
            lastOnline = agency.lastOnline;
          }
        }
      }

      socket.emit("update_isUserOnline", {
        userId,
        status: isOnline,
        lastOnline,
      });
    } catch (err) {
      console.error("❌ Error in fetch_isUserOnline:", err.message);
    }
  });

  socket.on("typing", ({ userId, isTyping }) => {
    socket.broadcast.emit("update_typing_status", {
      userId, // who is typing
      isTyping, // true or false
    });
  });

  socket.on("send_message_agency_model", async (data) => {
    try {
      const newMessage = await AgencyToModelMessage.create({
        senderId: data.senderId,
        receiverId: data.receiverId,
        senderModel: data.senderModel,
        receiverModel: data.receiverModel,
        text: data.text,
      });

      const receiverSocketId = connectedUsers.get(data.receiverId);
      if (receiverSocketId) {
        io.to(receiverSocketId).emit(
          "receive_message_agency_model",
          newMessage.toObject(),
        );
      }
    } catch (err) {
      console.error("❌ Error in send_message_agency_model:", err.message);
    }
  });
};

export default handleAgencyToModelSocket;
