import mongoose from "mongoose";

// Connects a Template to a Model
const templateAssignmentSchema = new mongoose.Schema({
  templateId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Template",
    required: true,
  },
  modelId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "ModelUser",
    required: true,
  }, // model who must fill
  agencyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Agency",
    required: true,
  }, // owner agency
  status: {
    type: String,
    enum: ["Not started", "In progress", "Submitted"],
    default: "Not started",
  },
  assignedAt: { type: Date, default: Date.now },
  submittedAt: { type: Date }, // set when model finishes and submits answers
});

export default mongoose.model("TemplateAssignment", templateAssignmentSchema);
