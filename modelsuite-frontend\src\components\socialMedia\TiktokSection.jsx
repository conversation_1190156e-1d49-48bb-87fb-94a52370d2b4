import { useEffect, useState } from "react";
import axios from "axios";

const TikTokSection = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [tiktokProfile, setTikTokProfile] = useState(null);
  const [loading, setLoading] = useState(true);

  const user = JSON.parse(localStorage.getItem("auth"))?.user;
  const token = JSON.parse(localStorage.getItem("auth"))?.token;

  const checkConnectionStatus = async () => {
    try {
      const res = await axios.get(
        `${import.meta.env.VITE_API_BASE_URL}/tiktok/is-connected`,
        {
          headers: { Authorization: `Bearer ${token}` },
        },
      );
      const connected = res.data.connected;
      setIsConnected(connected);

      if (connected) (await fetchTikTokProfile(), fetchTikTokInsights());
    } catch (err) {
      console.error("Connection check failed:", err.message);
      setIsConnected(false);
    } finally {
      setLoading(false);
    }
  };

  const fetchTikTokProfile = async () => {
    try {
      const res = await axios.get(
        `${import.meta.env.VITE_API_BASE_URL}/tiktok/profile`,
        {
          headers: { Authorization: `Bearer ${token}` },
        },
      );
      setTikTokProfile(res.data.profile.userInfo);
    } catch (err) {
      console.error("Failed to fetch TikTok profile:", err.message);
      setTikTokProfile(null);
    }
  };
  const fetchTikTokInsights = async () => {
    try {
      const res = await axios.get(
        `${import.meta.env.VITE_API_BASE_URL}/tiktok/stats`,
        {
          headers: { Authorization: `Bearer ${token}` },
        },
      );
      console.log(res.data);
    } catch (err) {
      console.error("Failed to fetch TikTok profile:", err.message);
      setTikTokProfile(null);
    }
  };

  const handleConnect = () => {
    const clientId = import.meta.env.VITE_TIKAPI_CLIENT_ID;
    const redirectUri = import.meta.env.VITE_TIKAPI_REDIRECT_URI;
    const scope = [
      "view_profile",
      "view_analytics",
      "explore",
      "view_notifications",
      "view_followers",
      "view_collections",
      "live",
      "view_messages",
      "send_messages",
      "conversation_requests",
      "media_actions",
    ].join("+");

    const authUrl = `https://tikapi.io/account/authorize?client_id=${clientId}&redirect_uri=${encodeURIComponent(
      redirectUri,
    )}&scope=${scope}&state=${user._id}`;

    window.open(authUrl, "_blank");
  };

  const handleDisconnect = async () => {
    try {
      await axios.delete(
        `${import.meta.env.VITE_API_BASE_URL}/tiktok/disconnect`,
        {
          headers: { Authorization: `Bearer ${token}` },
        },
      );
      setIsConnected(false);
      setTikTokProfile(null);
    } catch (err) {
      console.error("Failed to disconnect TikTok:", err.message);
    }
  };

  useEffect(() => {
    checkConnectionStatus();
  }, []);

  if (loading) return null;

  return (
    <div className="bg-white rounded-xl shadow p-4 w-full mt-4">
      <h2 className="text-lg font-semibold mb-3 text-gray-800">
        TikTok Integration
      </h2>

      {isConnected && tiktokProfile ? (
        <div className="flex flex-col gap-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <img
                src={tiktokProfile.user.avatarThumb}
                alt="TikTok Avatar"
                className="w-12 h-12 rounded-full object-cover"
              />
              <div>
                <p className="font-medium text-gray-900">
                  {tiktokProfile.user.nickname}
                </p>
                <p className="text-sm text-gray-500">
                  @{tiktokProfile.user.uniqueId}
                </p>
              </div>
            </div>
            <button
              onClick={handleDisconnect}
              className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-md"
            >
              Disconnect
            </button>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm text-gray-700">
            <div>
              <p className="text-gray-500">Followers</p>
              <p className="font-semibold">
                {tiktokProfile.stats.followerCount}
              </p>
            </div>
            <div>
              <p className="text-gray-500">Following</p>
              <p className="font-semibold">
                {tiktokProfile.stats.followingCount}
              </p>
            </div>
            <div>
              <p className="text-gray-500">Likes</p>
              <p className="font-semibold">{tiktokProfile.stats.heartCount}</p>
            </div>
            <div>
              <p className="text-gray-500">Videos</p>
              <p className="font-semibold">{tiktokProfile.stats.videoCount}</p>
            </div>
            <div>
              <p className="text-gray-500">Friends</p>
              <p className="font-semibold">{tiktokProfile.stats.friendCount}</p>
            </div>
          </div>
        </div>
      ) : (
        <button
          onClick={handleConnect}
          className="px-4 py-2 bg-black hover:bg-gray-900 text-white rounded-md"
        >
          Connect TikTok
        </button>
      )}
    </div>
  );
};

export default TikTokSection;
