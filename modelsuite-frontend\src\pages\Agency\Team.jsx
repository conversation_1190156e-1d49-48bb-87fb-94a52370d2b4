import { useState, useEffect, useCallback } from "react";
import {
  Search,
  UserPlus,
  MoreVertical,
  Mail,
  User,
  Shield,
  Trash2,
  Edit2,
} from "lucide-react";
import axios from "axios";
import { toast } from "react-hot-toast";
import debounce from "lodash/debounce";

const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
});

const TeamMemberCard = ({ member, onEdit, onDelete }) => (
  <div className="flex items-center justify-between py-4 px-6 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
    {/* Name Column */}
    <div className="w-1/4 flex items-center space-x-3">
      <div className="h-9 w-9 rounded-full bg-indigo-50 dark:bg-indigo-500/10 flex items-center justify-center">
        <span className="text-indigo-600 dark:text-indigo-400 font-medium text-sm">
          {member.name.charAt(0).toUpperCase()}
        </span>
      </div>
      <span className="font-medium text-gray-900 dark:text-white text-sm truncate">
        {member.name}
      </span>
    </div>

    {/* Email Column */}
    <div className="w-1/4">
      <span className="text-sm text-gray-600 dark:text-gray-300 truncate">
        {member.email}
      </span>
    </div>

    {/* Role Column */}
    <div className="w-1/6">
      <span className="text-sm text-gray-600 dark:text-gray-300 capitalize">
        {member.userType}
      </span>
    </div>

    {/* Status Column */}
    <div className="w-1/6">
      <span
        className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
          member.status === "active"
            ? "bg-emerald-50 text-emerald-700 dark:bg-emerald-500/10 dark:text-emerald-400"
            : "bg-amber-50 text-amber-700 dark:bg-amber-500/10 dark:text-amber-400"
        }`}
      >
        {member.status}
      </span>
    </div>

    {/* Actions Column */}
    <div className="w-1/6 flex justify-end space-x-2">
      <button
        onClick={() => onEdit(member)}
        className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
        title="Edit member"
      >
        <Edit2 className="h-4 w-4" />
      </button>
      <button
        onClick={() => onDelete(member)}
        className="p-1.5 text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400 transition-colors"
        title="Delete member"
      >
        <Trash2 className="h-4 w-4" />
      </button>
    </div>
  </div>
);

const InviteMemberModal = ({ isOpen, onClose }) => {
  const [formData, setFormData] = useState({
    email: "",
    userType: "creator", // Changed back to original
    memberType: "member", // New field for frontend
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await api.post(
        "/employee/invite",
        {
          email: formData.email,
          userType: formData.userType, // Send only the backend userType
        },
        {
          headers: {
            Authorization: `Bearer ${JSON.parse(localStorage.getItem("auth"))?.token}`,
          },
        },
      );

      toast.success("Invitation sent successfully!");
      onClose();
      setFormData({ email: "", userType: "creator", memberType: "member" });
    } catch (err) {
      toast.error(err.response?.data?.message || "Failed to send invitation");
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-900/50 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-900 rounded-xl p-6 w-full max-w-md shadow-xl">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
            <UserPlus className="h-5 w-5 text-indigo-500" />
            Invite Team Member
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
          >
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-5">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Email Address
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="email"
                value={formData.email}
                onChange={(e) =>
                  setFormData({ ...formData, email: e.target.value })
                }
                className="pl-9 w-full h-10 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                placeholder="Enter email address"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              User Type
            </label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <select
                value={formData.memberType}
                onChange={(e) =>
                  setFormData({ ...formData, memberType: e.target.value })
                }
                className="pl-9 w-full h-10 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              >
                <option value="member">Member</option>
                <option value="leader">Leader</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Role
            </label>
            <div className="relative">
              <Shield className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <select
                value={formData.userType}
                onChange={(e) =>
                  setFormData({ ...formData, userType: e.target.value })
                }
                className="pl-9 w-full h-10 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              >
                <option value="creator">Creator</option>
                <option value="manager">Manager</option>
                <option value="viewer">Viewer</option>
              </select>
            </div>
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full h-10 flex justify-center items-center bg-indigo-600 hover:bg-indigo-700 text-white text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {loading ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                    fill="none"
                  />
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                Sending Invite...
              </>
            ) : (
              <>
                <UserPlus className="h-4 w-4 mr-2" />
                Send Invitation
              </>
            )}
          </button>
        </form>
      </div>
    </div>
  );
};

export default function Team() {
  const [searchQuery, setSearchQuery] = useState("");
  const [members, setMembers] = useState([]);
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  const fetchMembers = async () => {
    try {
      const response = await api.get("/employee/list", {
        headers: {
          Authorization: `Bearer ${JSON.parse(localStorage.getItem("auth"))?.token}`,
        },
      });
      setMembers(response.data);
    } catch (err) {
      toast.error("Failed to fetch team members");
    } finally {
      setLoading(false);
    }
  };

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (query) => {
      if (!query.trim()) {
        fetchMembers();
        return;
      }

      try {
        const response = await api.get("/employee/search", {
          params: { query },
          headers: {
            Authorization: `Bearer ${JSON.parse(localStorage.getItem("auth"))?.token}`,
          },
        });
        setMembers(response.data);
      } catch (err) {
        console.error("Search error:", err);
      }
    }, 300),
    [],
  );

  useEffect(() => {
    fetchMembers();
  }, []);

  useEffect(() => {
    debouncedSearch(searchQuery);
    return () => debouncedSearch.cancel();
  }, [searchQuery, debouncedSearch]);

  const handleEdit = (member) => {
    // Implement edit functionality
    console.log("Edit member:", member);
  };

  const handleDelete = async (member) => {
    try {
      const token = JSON.parse(localStorage.getItem("auth"))?.token;

      await api.delete(`/employee/${member._id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      toast.success("Member deleted successfully");

      // Refetch updated list
      fetchMembers();
    } catch (err) {
      console.error("Failed to delete member:", err);
      toast.error(err.response?.data?.message || "Failed to delete member");
    }
  };

  return (
    <div className="container mx-auto px-6 py-8 max-w-6xl">
      <div className="mb-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Team
          </h1>
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search members..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 h-10 w-64 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm text-gray-900 dark:text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              />
            </div>
            <button
              onClick={() => setIsInviteModalOpen(true)}
              className="flex items-center gap-2 bg-indigo-600 text-white px-4 h-10 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors text-sm font-medium"
            >
              <UserPlus className="h-4 w-4" />
              Invite Member
            </button>
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-900 rounded-xl border border-gray-200 dark:border-gray-800 overflow-hidden">
        {/* Column Headers */}
        <div className="flex items-center px-6 py-3 border-b border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-900">
          <div className="w-1/4 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            Name
          </div>
          <div className="w-1/4 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            E-mail Address
          </div>
          <div className="w-1/6 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            Role
          </div>
          <div className="w-1/6 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            Status
          </div>
          <div className="w-1/6 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider text-right">
            Actions
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin h-5 w-5 text-indigo-500">
              <svg className="h-5 w-5" viewBox="0 0 24 24">
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                  fill="none"
                />
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-800">
            {members.length === 0 ? (
              <div className="text-center py-12">
                <User className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                  No team members found
                </h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {searchQuery
                    ? "Try adjusting your search"
                    : "Get started by adding a new team member"}
                </p>
              </div>
            ) : (
              members.map((member) => (
                <TeamMemberCard
                  key={member._id}
                  member={member}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                />
              ))
            )}
          </div>
        )}
      </div>

      <InviteMemberModal
        isOpen={isInviteModalOpen}
        onClose={() => setIsInviteModalOpen(false)}
      />
    </div>
  );
}
