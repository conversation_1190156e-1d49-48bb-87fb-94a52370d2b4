# 🤝 Contributing to ModelSuite.ai

Thank you for helping improve ModelSuite.ai!  
We welcome contributions — but to keep things clean, fast, and high-quality, please follow the guidelines below.

---

## 📚 Table of Contents

1. [Branch Naming Convention](#branch-naming-convention)
2. [Pull Request Guidelines](#pull-request-guidelines)
3. [Code Quality Standards](#code-quality-standards)
4. [Testing Requirements](#testing-requirements)
5. [Review Process](#review-process)

---

## 🌿 Branch Naming Convention

Please name your branches using the following format:

<type>/<short-description>

**Types:**

- `feature/` – new feature or enhancement
- `bugfix/` – bug fixes during development
- `hotfix/` – urgent production fixes
- `refactor/` – code structure improvements
- `chore/` – maintenance tasks (e.g., config, linting)

**Examples:**

- `feature/login-page`
- `bugfix/api-timeout`
- `hotfix/payment-crash`
- `refactor/auth-service`

---

## ✅ Pull Request Guidelines

Before submitting any PRs, please go through the checklist below.

> 🚨 **Important:**  
> Please submit PRs only after **8:00 PM IST**  
> so the team can focus on their work during the day without interruptions.

### 🔁 General

- [ ] Correct branch used (not `main`)
- [ ] Clear and descriptive PR title
- [ ] Short explanation of what and why
- [ ] PR is small and focused (ideally < 300 lines of diff)
- [ ] Linked to relevant issue(s) (`Closes #123`)
- [ ] Pulled the latest from `main` before opening PR (to avoid merge conflicts)
- [ ] PR raised after 8:00 PM IST

### 🔧 Code Quality

- [ ] No `console.log`, `TODO`, or debug leftovers
- [ ] Code is clean, modular, and follows structure
- [ ] Linting passed (e.g., ESLint, Prettier)
- [ ] Unused code and files removed

### 🧪 Testing

- [ ] Local tests passed
- [ ] Feature manually or automatically tested
- [ ] No known regressions introduced
- [ ] Screenshots or videos added for UI changes (if applicable)

### 🔍 Review-Ready

- [ ] PR is not a WIP
- [ ] I'm available to respond to review feedback quickly

---

## 🧠 Tips for a Smooth Contribution

- Keep PRs **focused and readable**
- Communicate early if you're unsure about your approach
- Use [draft PRs](https://github.blog/2019-02-14-introducing-draft-pull-requests/) if your work is not yet ready for review

---

## 🙏 Thank You!

Your contributions make ModelSuite.ai better for everyone.  
If you have any questions, feel free to tag a maintainer in the PR or open an issue.
