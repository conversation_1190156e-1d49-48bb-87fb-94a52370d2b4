import mongoose from "mongoose";

const groupConversationSchema = new mongoose.Schema({
  title: { type: String, required: true },
  type: { type: String, default: "group" },
  members: [
    {
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        ref: "User",
      },
      role: { type: String, enum: ["admin", "member"], default: "member" },
      pinned: { type: Boolean, default: false },
      joinedAt: { type: Date, default: Date.now },
    },
  ],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: "User",
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

export default mongoose.model("GroupConversation", groupConversationSchema);
