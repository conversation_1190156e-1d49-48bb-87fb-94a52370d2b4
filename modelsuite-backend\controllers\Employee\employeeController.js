import bcrypt from "bcryptjs";
import crypto from "crypto";
import Employee from "../../models/Employee/Employee.js";
import EmployeeInvite from "../../models/Employee/EmployeeInvite.js";
import { generateRandomPassword } from "../../utils/randomPass.js";
import {
  sendEmployeeWelcomeEmail,
  sendEmployeeInviteEmail,
} from "../../utils/sendOtpToEmail.js";

export const inviteEmployee = async (req, res) => {
  try {
    const { email, userType } = req.body;

    // Validate input
    if (!email || !userType) {
      return res
        .status(400)
        .json({ message: "Email and userType are required" });
    }

    if (!["creator", "manager", "viewer"].includes(userType)) {
      return res.status(400).json({ message: "Invalid userType" });
    }

    // Get agency ID based on the authenticated user
    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;

    // Check for existing invite
    const existingInvite = await EmployeeInvite.findOne({
      email: email.toLowerCase(),
      agencyId,
      status: "pending",
    });

    if (existingInvite) {
      return res.status(400).json({
        message: "An invite has already been sent to this email",
      });
    }

    // Generate secure token and expiry
    const token = crypto.randomBytes(32).toString("hex");
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now

    // Create new invite
    const invite = await EmployeeInvite.create({
      email: email.toLowerCase(),
      userType,
      token,
      expiresAt,
      status: "pending",
      agencyId,
    });

    // Send invite email
    const activationLink = `${process.env.FRONTEND_HOSTING_BASEURL}/employee/activate?token=${token}`;
    await sendEmployeeInviteEmail(email, activationLink);

    res.status(201).json({
      message: "Invite sent successfully",
      email: invite.email,
    });
  } catch (err) {
    console.error("inviteEmployee error:", err);
    res.status(500).json({ message: "Failed to send invite" });
  }
};

export const activateEmployee = async (req, res) => {
  try {
    const { token, name, password } = req.body;

    if (!token || !name || !password) {
      return res.status(400).json({ message: "All fields are required" });
    }

    // Find the invite
    const invite = await EmployeeInvite.findOne({
      token,
      status: "pending",
      expiresAt: { $gt: new Date() },
    });

    if (!invite) {
      return res.status(400).json({
        message: "Invalid or expired invitation link",
      });
    }

    // Check if email is already registered
    const existingEmployee = await Employee.findOne({ email: invite.email });
    if (existingEmployee) {
      return res.status(400).json({
        message: "An account with this email already exists",
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create employee account
    const newEmployee = await Employee.create({
      name,
      email: invite.email,
      password: hashedPassword,
      userType: invite.userType,
      agencyId: invite.agencyId,
      status: "active",
    });

    // Mark invite as used
    invite.status = "used";
    await invite.save();

    // Generate tokens for immediate login
    const accessToken = newEmployee.generateAccessToken();
    const refreshToken = newEmployee.generateRefreshToken();

    // Update refresh token in DB
    newEmployee.loginRefreshToken = refreshToken;
    await newEmployee.save();

    res.status(201).json({
      message: "Account activated successfully",
      accessToken,
      refreshToken,
      user: {
        _id: newEmployee._id,
        name: newEmployee.name,
        email: newEmployee.email,
        userType: newEmployee.userType,
      },
    });
  } catch (err) {
    console.error("activateEmployee error:", err);
    res.status(500).json({ message: "Failed to activate account" });
  }
};

// Get all employees for an agency
export const getEmployees = async (req, res) => {
  try {
    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;

    const employees = await Employee.find({ agencyId })
      .select("name email userType status createdAt")
      .sort({ createdAt: -1 });

    res.status(200).json(employees);
  } catch (err) {
    console.error("getEmployees error:", err);
    res.status(500).json({ message: "Failed to fetch employees" });
  }
};

// Search employees
export const searchEmployees = async (req, res) => {
  try {
    const { query } = req.query;
    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;

    if (!query) {
      return res.status(400).json({ message: "Search query is required" });
    }

    const searchRegex = new RegExp(query, "i");

    const employees = await Employee.find({
      agencyId,
      $or: [
        { name: searchRegex },
        { email: searchRegex },
        { userType: searchRegex },
      ],
    })
      .select("name email userType status createdAt")
      .sort({ createdAt: -1 })
      .limit(10);

    res.status(200).json(employees);
  } catch (err) {
    console.error("searchEmployees error:", err);
    res.status(500).json({ message: "Failed to search employees" });
  }
};

export const deleteEmployee = async (req, res) => {
  try {
    const { id } = req.params;

    const employee = await Employee.findById(id);
    if (!employee) {
      return res.status(404).json({ message: "Employee not found" });
    }

    // Optional: ensure agency owns the employee
    if (String(employee.agencyId) !== String(req.user._id)) {
      return res.status(403).json({ message: "Unauthorized" });
    }

    // Delete the employee
    await employee.deleteOne();

    // 💥 Also delete the invite (if any) to unblock re-invites
    await EmployeeInvite.findOneAndDelete({
      email: employee.email,
      agencyId: employee.agencyId,
    });

    res
      .status(200)
      .json({ message: "Employee and invite deleted successfully" });
  } catch (err) {
    console.error("Delete Employee Error:", err);
    res.status(500).json({ message: "Server error" });
  }
};

export const deletePendingInvite = async (req, res) => {
  try {
    const { email } = req.params;

    const invite = await EmployeeInvite.findOneAndDelete({
      email,
      agencyId: req.user._id,
      status: "pending",
    });

    if (!invite) {
      return res.status(404).json({ message: "No pending invite found" });
    }

    res.status(200).json({ message: "Invite deleted successfully" });
  } catch (err) {
    console.error("Delete Invite Error:", err);
    res.status(500).json({ message: "Server error" });
  }
};
