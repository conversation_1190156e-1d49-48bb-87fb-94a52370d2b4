import React from "react";
import SkillMatrixDashboard from "../../components/SkillMatrix/SkillMatrixDashboard";

const SkillMatrixPage = () => {
  // For now, use a default agency ID and user until auth is properly integrated
  // You can later integrate this with your auth system
  const defaultUser = {
    _id: "507f1f77bcf86cd799439011",
    display_name: "Agency User",
    role: "agency",
    email: "<EMAIL>",
  };

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <SkillMatrixDashboard currentUser={defaultUser} />
      </div>
    </div>
  );
};

export default SkillMatrixPage;
