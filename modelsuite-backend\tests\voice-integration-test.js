/**
 * Voice Feature Integration Test Script
 * Run this script to verify all voice feature endpoints and functionality
 */

const API_BASE = process.env.VITE_API_BASE_URL || 'http://localhost:3000/api/v1';

// Mock authentication token for testing
const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';

/**
 * Test endpoint availability and response format
 */
async function testEndpoint(method, endpoint, data = null, headers = {}) {
  const url = `${API_BASE}${endpoint}`;
  const config = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${mockToken}`,
      ...headers
    }
  };

  if (data && method !== 'GET') {
    config.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, config);
    const result = await response.json();
    
    return {
      success: response.ok,
      status: response.status,
      data: result,
      url
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      url
    };
  }
}

/**
 * Voice Scripts API Tests
 */
async function testVoiceScriptsAPI() {
  console.log('🎯 Testing Voice Scripts API...');
  
  const tests = [
    {
      name: 'Get All Scripts',
      method: 'GET',
      endpoint: '/voice/scripts'
    },
    {
      name: 'Create Script',
      method: 'POST',
      endpoint: '/voice/scripts',
      data: {
        title: 'Test Script',
        description: 'Test script for API validation',
        content: 'This is a test voice script content.',
        category: 'commercial',
        difficulty: 'beginner',
        estimatedDuration: 30,
        tags: ['test', 'api']
      }
    },
    {
      name: 'Get Script by ID',
      method: 'GET',
      endpoint: '/voice/scripts/test-id'
    },
    {
      name: 'Update Script',
      method: 'PUT',
      endpoint: '/voice/scripts/test-id',
      data: {
        title: 'Updated Test Script'
      }
    },
    {
      name: 'Delete Script',
      method: 'DELETE',
      endpoint: '/voice/scripts/test-id'
    }
  ];

  for (const test of tests) {
    const result = await testEndpoint(test.method, test.endpoint, test.data);
    console.log(`  ✓ ${test.name}: ${result.success ? '✅ PASS' : '❌ FAIL'} (${result.status || 'ERROR'})`);
    if (!result.success) {
      console.log(`    Error: ${result.error || result.data?.message || 'Unknown error'}`);
    }
  }
}

/**
 * Voice Assignments API Tests
 */
async function testVoiceAssignmentsAPI() {
  console.log('🎯 Testing Voice Assignments API...');
  
  const tests = [
    {
      name: 'Get Available Models',
      method: 'GET',
      endpoint: '/voice/models/available'
    },
    {
      name: 'Create Assignment',
      method: 'POST',
      endpoint: '/voice/assignments',
      data: {
        scriptId: 'test-script-id',
        modelIds: ['test-model-id'],
        deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        instructions: 'Please complete this voice recording by the deadline.'
      }
    },
    {
      name: 'Get My Assignments (Model)',
      method: 'GET',
      endpoint: '/voice/assignments/my'
    },
    {
      name: 'Get Assignment by ID',
      method: 'GET',
      endpoint: '/voice/assignments/test-assignment-id'
    },
    {
      name: 'Update Assignment Status',
      method: 'PUT',
      endpoint: '/voice/assignments/test-assignment-id/status',
      data: {
        status: 'in_progress'
      }
    }
  ];

  for (const test of tests) {
    const result = await testEndpoint(test.method, test.endpoint, test.data);
    console.log(`  ✓ ${test.name}: ${result.success ? '✅ PASS' : '❌ FAIL'} (${result.status || 'ERROR'})`);
    if (!result.success) {
      console.log(`    Error: ${result.error || result.data?.message || 'Unknown error'}`);
    }
  }
}

/**
 * Voice Recordings API Tests
 */
async function testVoiceRecordingsAPI() {
  console.log('🎯 Testing Voice Recordings API...');
  
  const tests = [
    {
      name: 'Get Recordings',
      method: 'GET',
      endpoint: '/voice/recordings'
    },
    {
      name: 'Get Recording by ID',
      method: 'GET',
      endpoint: '/voice/recordings/test-recording-id'
    },
    {
      name: 'Update Recording Status',
      method: 'PUT',
      endpoint: '/voice/recordings/test-recording-id/status',
      data: {
        status: 'approved',
        feedback: 'Great recording! Well done.'
      }
    },
    {
      name: 'Download Recording',
      method: 'GET',
      endpoint: '/voice/recordings/test-recording-id/download'
    }
  ];

  for (const test of tests) {
    const result = await testEndpoint(test.method, test.endpoint, test.data);
    console.log(`  ✓ ${test.name}: ${result.success ? '✅ PASS' : '❌ FAIL'} (${result.status || 'ERROR'})`);
    if (!result.success) {
      console.log(`    Error: ${result.error || result.data?.message || 'Unknown error'}`);
    }
  }
}

/**
 * Voice Sentences API Tests
 */
async function testVoiceSentencesAPI() {
  console.log('🎯 Testing Voice Sentences API...');
  
  const tests = [
    {
      name: 'Get Script Sentences',
      method: 'GET',
      endpoint: '/voice/scripts/test-script-id/sentences'
    },
    {
      name: 'Create Sentence',
      method: 'POST',
      endpoint: '/voice/scripts/test-script-id/sentences',
      data: {
        text: 'This is a test sentence for voice recording.',
        order: 1,
        notes: 'Please emphasize the word "test".'
      }
    },
    {
      name: 'Update Sentence',
      method: 'PUT',
      endpoint: '/voice/sentences/test-sentence-id',
      data: {
        text: 'This is an updated test sentence.'
      }
    },
    {
      name: 'Delete Sentence',
      method: 'DELETE',
      endpoint: '/voice/sentences/test-sentence-id'
    }
  ];

  for (const test of tests) {
    const result = await testEndpoint(test.method, test.endpoint, test.data);
    console.log(`  ✓ ${test.name}: ${result.success ? '✅ PASS' : '❌ FAIL'} (${result.status || 'ERROR'})`);
    if (!result.success) {
      console.log(`    Error: ${result.error || result.data?.message || 'Unknown error'}`);
    }
  }
}

/**
 * Voice Analytics API Tests
 */
async function testVoiceAnalyticsAPI() {
  console.log('🎯 Testing Voice Analytics API...');
  
  const tests = [
    {
      name: 'Get Voice Dashboard Stats',
      method: 'GET',
      endpoint: '/voice/analytics/dashboard'
    },
    {
      name: 'Get Model Performance',
      method: 'GET',
      endpoint: '/voice/analytics/models/performance'
    },
    {
      name: 'Get Script Analytics',
      method: 'GET',
      endpoint: '/voice/analytics/scripts'
    }
  ];

  for (const test of tests) {
    const result = await testEndpoint(test.method, test.endpoint, test.data);
    console.log(`  ✓ ${test.name}: ${result.success ? '✅ PASS' : '❌ FAIL'} (${result.status || 'ERROR'})`);
    if (!result.success) {
      console.log(`    Error: ${result.error || result.data?.message || 'Unknown error'}`);
    }
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting Voice Feature API Integration Tests\n');
  
  await testVoiceScriptsAPI();
  console.log('');
  
  await testVoiceAssignmentsAPI();
  console.log('');
  
  await testVoiceRecordingsAPI();
  console.log('');
  
  await testVoiceSentencesAPI();
  console.log('');
  
  await testVoiceAnalyticsAPI();
  console.log('');
  
  console.log('✅ Voice Feature API Tests Completed!');
  console.log('\n📝 Next Steps:');
  console.log('1. Fix any failing endpoints');
  console.log('2. Test file upload functionality manually');
  console.log('3. Verify real-time socket events');
  console.log('4. Test with actual authentication tokens');
  console.log('5. Validate error handling and edge cases');
}

// Run tests if this script is executed directly
if (typeof window === 'undefined') {
  runAllTests().catch(console.error);
}

export { runAllTests, testVoiceScriptsAPI, testVoiceAssignmentsAPI, testVoiceRecordingsAPI, testVoiceSentencesAPI, testVoiceAnalyticsAPI };
