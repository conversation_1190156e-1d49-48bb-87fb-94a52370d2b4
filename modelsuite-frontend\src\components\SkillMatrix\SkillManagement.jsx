import React, { useState, useEffect } from "react";
import { Plus, Edit3, Trash2, Save, X } from "lucide-react";
import { useSelector, useDispatch } from "react-redux";
import {
  fetchSkillCategories,
  createSkillCategory,
  updateSkillCategory,
  deleteSkillCategory,
  selectSkillCategories,
  selectSkillCategoriesLoading,
} from "../../globalstate/skillMatrix/skillCategoriesSlice";
import {
  fetchSkills,
  createSkill,
  updateSkill,
  deleteSkill,
  selectSkills,
  selectSkillsLoading,
} from "../../globalstate/skillMatrix/skillsSlice";

const SkillManagement = ({ onSkillsUpdate }) => {
  const dispatch = useDispatch();
  const [activeTab, setActiveTab] = useState("skills");
  const [editingItem, setEditingItem] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);

  // Redux selectors
  const categories = useSelector(selectSkillCategories);
  const skills = useSelector(selectSkills);
  const categoriesLoading = useSelector(selectSkillCategoriesLoading);
  const skillsLoading = useSelector(selectSkillsLoading);

  const loading = categoriesLoading || skillsLoading;

  useEffect(() => {
    dispatch(fetchSkillCategories());
    dispatch(fetchSkills());
  }, [dispatch]);

  const SkillCategoryForm = ({ category, onSave, onCancel }) => {
    const [formData, setFormData] = useState({
      name: category?.name || "",
      description: category?.description || "",
      order: category?.order || 0,
    });

    const handleSubmit = async (e) => {
      e.preventDefault();
      try {
        if (category) {
          await dispatch(
            updateSkillCategory({ id: category._id, ...formData }),
          ).unwrap();
        } else {
          await dispatch(createSkillCategory(formData)).unwrap();
        }
        onSave();
      } catch (error) {
        console.error("Failed to save category:", error);
      }
    };

    return (
      <form
        onSubmit={handleSubmit}
        className="bg-gray-700 p-4 rounded-lg border border-gray-600"
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              className="w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Description
            </label>
            <input
              type="text"
              value={formData.description}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
              className="w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Order
            </label>
            <input
              type="number"
              value={formData.order}
              onChange={(e) =>
                setFormData({ ...formData, order: parseInt(e.target.value) })
              }
              className="w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400"
            />
          </div>
        </div>
        <div className="flex justify-end space-x-2 mt-4">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-400 hover:text-gray-200 transition-colors duration-200"
          >
            <X className="w-4 h-4" />
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-md flex items-center space-x-2 transition-all duration-200"
          >
            <Save className="w-4 h-4" />
            <span>Save</span>
          </button>
        </div>
      </form>
    );
  };

  const SkillForm = ({ skill, onSave, onCancel }) => {
    const [formData, setFormData] = useState({
      name: skill?.name || "",
      category_id: skill?.category_id?._id || "",
      type: skill?.type || "hard",
      proficiency_level: skill?.proficiency_level || "beginner",
      description: skill?.description || "",
      weight: skill?.weight || 1,
    });

    const handleSubmit = async (e) => {
      e.preventDefault();
      try {
        if (skill) {
          await dispatch(updateSkill({ id: skill._id, ...formData })).unwrap();
        } else {
          await dispatch(createSkill(formData)).unwrap();
        }
        onSave();
        onSkillsUpdate?.();
      } catch (error) {
        console.error("Failed to save skill:", error);
      }
    };

    return (
      <form
        onSubmit={handleSubmit}
        className="bg-gray-700 p-4 rounded-lg border border-gray-600"
      >
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              className="w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Category
            </label>
            <select
              value={formData.category_id}
              onChange={(e) =>
                setFormData({ ...formData, category_id: e.target.value })
              }
              className="w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white"
              required
            >
              <option value="">Select Category</option>
              {categories.map((cat) => (
                <option key={cat._id} value={cat._id}>
                  {cat.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Type
            </label>
            <select
              value={formData.type}
              onChange={(e) =>
                setFormData({ ...formData, type: e.target.value })
              }
              className="w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white"
            >
              <option value="hard">Hard Skill</option>
              <option value="soft">Soft Skill</option>
              <option value="tool">Tool</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Proficiency Level
            </label>
            <select
              value={formData.proficiency_level}
              onChange={(e) =>
                setFormData({ ...formData, proficiency_level: e.target.value })
              }
              className="w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white"
            >
              <option value="beginner">Beginner</option>
              <option value="intermediate">Intermediate</option>
              <option value="advanced">Advanced</option>
              <option value="expert">Expert</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Weight
            </label>
            <select
              value={formData.weight || 1}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  weight: parseInt(e.target.value) || 1,
                })
              }
              className="w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white"
            >
              {[1, 2, 3, 4, 5].map((w) => (
                <option key={w} value={w}>
                  {w}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Description
            </label>
            <input
              type="text"
              value={formData.description}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
              className="w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400"
            />
          </div>
        </div>
        <div className="flex justify-end space-x-2 mt-4">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-400 hover:text-gray-200 transition-colors duration-200"
          >
            <X className="w-4 h-4" />
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-md flex items-center space-x-2 transition-all duration-200"
          >
            <Save className="w-4 h-4" />
            <span>Save</span>
          </button>
        </div>
      </form>
    );
  };

  const handleDelete = async (type, id) => {
    if (!confirm(`Are you sure you want to delete this ${type}?`)) return;

    try {
      if (type === "category") {
        await dispatch(deleteSkillCategory(id)).unwrap();
      } else {
        await dispatch(deleteSkill(id)).unwrap();
      }
      if (type === "skill") onSkillsUpdate?.();
    } catch (error) {
      console.error(`Failed to delete ${type}:`, error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-800 rounded-xl border border-gray-700">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          <p className="text-gray-400 text-sm">Loading skill management...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex space-x-4">
        <button
          onClick={() => setActiveTab("categories")}
          className={`px-4 py-2 rounded-lg transition-all duration-200 ${
            activeTab === "categories"
              ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg"
              : "bg-gray-700 text-gray-300 hover:bg-gray-600 border border-gray-600"
          }`}
        >
          Categories
        </button>
        <button
          onClick={() => setActiveTab("skills")}
          className={`px-4 py-2 rounded-lg transition-all duration-200 ${
            activeTab === "skills"
              ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg"
              : "bg-gray-700 text-gray-300 hover:bg-gray-600 border border-gray-600"
          }`}
        >
          Skills
        </button>
      </div>

      {activeTab === "categories" && (
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-white">
              Skill Categories
            </h3>
            <button
              onClick={() => setShowAddForm("category")}
              className="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-lg flex items-center space-x-2 transition-all duration-200 shadow-lg"
            >
              <Plus className="w-4 h-4" />
              <span>Add Category</span>
            </button>
          </div>

          {showAddForm === "category" && (
            <div className="mb-4">
              <SkillCategoryForm
                onSave={() => setShowAddForm(false)}
                onCancel={() => setShowAddForm(false)}
              />
            </div>
          )}

          <div className="space-y-3">
            {categories.map((category) => (
              <div
                key={category._id}
                className="bg-gray-700 border border-gray-600 rounded-lg p-4"
              >
                {editingItem === category._id ? (
                  <SkillCategoryForm
                    category={category}
                    onSave={() => setEditingItem(null)}
                    onCancel={() => setEditingItem(null)}
                  />
                ) : (
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="font-medium text-white">
                        {category.name}
                      </h4>
                      <p className="text-gray-400 text-sm">
                        {category.description}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setEditingItem(category._id)}
                        className="p-2 text-gray-400 hover:text-blue-400 transition-colors duration-200"
                      >
                        <Edit3 className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDelete("category", category._id)}
                        className="p-2 text-gray-400 hover:text-red-400 transition-colors duration-200"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === "skills" && (
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-white">Skills</h3>
            <button
              onClick={() => setShowAddForm("skill")}
              className="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-lg flex items-center space-x-2 transition-all duration-200 shadow-lg"
            >
              <Plus className="w-4 h-4" />
              <span>Add Skill</span>
            </button>
          </div>

          {showAddForm === "skill" && (
            <div className="mb-4">
              <SkillForm
                onSave={() => setShowAddForm(false)}
                onCancel={() => setShowAddForm(false)}
              />
            </div>
          )}

          <div className="space-y-3">
            {skills.map((skill) => (
              <div
                key={skill._id}
                className="bg-gray-700 border border-gray-600 rounded-lg p-4"
              >
                {editingItem === skill._id ? (
                  <SkillForm
                    skill={skill}
                    onSave={() => setEditingItem(null)}
                    onCancel={() => setEditingItem(null)}
                  />
                ) : (
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="font-medium text-white">{skill.name}</h4>
                      <div className="flex items-center space-x-4 text-sm text-gray-400">
                        <span>{skill.category_id?.name}</span>
                        <span className="capitalize">{skill.type}</span>
                        <span className="capitalize">
                          {skill.proficiency_level}
                        </span>
                        <span>Weight: {skill.weight}</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setEditingItem(skill._id)}
                        className="p-2 text-gray-400 hover:text-blue-400 transition-colors duration-200"
                      >
                        <Edit3 className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDelete("skill", skill._id)}
                        className="p-2 text-gray-400 hover:text-red-400 transition-colors duration-200"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SkillManagement;
