import express from "express";
import upload from "../middlewares/multer.js";
import {
  uploadAttachment,
  getAttachments,
  deleteAttachment,
} from "../controllers/attachmentController.js";

const router = express.Router();

router.post("/profile-photo", upload.single("image"), (req, res) => {
  const imageUrl = req.file.path; // secure Cloudinary URL
  res.status(200).json({ imageUrl });
});

// Note attachments
router.post(
  "/notes/:noteId/attachments",
  upload.single("file"),
  uploadAttachment,
);
router.get("/notes/:noteId/attachments", getAttachments);
router.delete("/notes/:noteId/attachments/:attachmentId", deleteAttachment);

export default router;
