import React, { useState, useMemo } from "react";
import {
  Edit,
  RefreshCw,
  Trash2,
  Copy,
  Download,
  ChevronLeft,
  ChevronRight,
  Clock,
  Globe,
  MessageCircle,
  User,
  Calendar,
  Activity,
} from "lucide-react";

const Supportcontactmanagement = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [viewMode, setViewMode] = useState("cards"); // 'cards' or 'table'
  // eslint-disable-next-line no-unused-vars
  const [selectedContacts, setSelectedContacts] = useState([]);
  const itemsPerPage = 5;

  // Dummy data - 20 assigned contacts
  const dummyContacts = [
    {
      id: 1,
      name: "<PERSON>",
      title: "Senior Support Specialist",
      profilePhoto:
        "https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face",
      assignedRole: "Technical Lead",
      bio: "Specialized in enterprise solutions and system integrations with 8+ years experience.",
      supportTags: ["Technical", "Enterprise", "Integration"],
      languages: ["English", "Spanish"],
      availability: "Available",
      timezone: "EST (UTC-5)",
      lastActive: "2 minutes ago",
      avgResponseTime: "12 minutes",
      assignedModels: ["Sebanti Dasgupta", "Rishav Sengupta"],
    },
    {
      id: 2,
      name: "Michael Chen",
      title: "AI Support Engineer",
      profilePhoto:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
      assignedRole: "AI Specialist",
      bio: "Expert in machine learning troubleshooting and model optimization.",
      supportTags: ["AI/ML", "Performance", "Optimization"],
      languages: ["English", "Mandarin", "Japanese"],
      availability: "Busy",
      timezone: "PST (UTC-8)",
      lastActive: "15 minutes ago",
      avgResponseTime: "8 minutes",
      assignedModels: ["Patrick Johnson", "Ureka Sizemar"],
    },
    {
      id: 3,
      name: "Emma Rodriguez",
      title: "Customer Success Manager",
      profilePhoto:
        "https://images.unsplash.com/photo-*************-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
      assignedRole: "Account Manager",
      bio: "Focused on customer onboarding and relationship management.",
      supportTags: ["Onboarding", "Account Management", "Training"],
      languages: ["English", "Spanish", "Portuguese"],
      availability: "Available",
      timezone: "CST (UTC-6)",
      lastActive: "5 minutes ago",
      avgResponseTime: "15 minutes",
      assignedModels: ["Kris Jenner", "Sebanti Dasgupta"],
    },
    {
      id: 4,
      name: "David Kim",
      title: "Technical Consultant",
      profilePhoto:
        "https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      assignedRole: "Integration Expert",
      bio: "Specializes in API integrations and custom implementations.",
      supportTags: ["API", "Integration", "Custom Solutions"],
      languages: ["English", "Korean"],
      availability: "Away",
      timezone: "KST (UTC+9)",
      lastActive: "1 hour ago",
      avgResponseTime: "25 minutes",
      assignedModels: ["Rishav Sengupta", "Sebanti Dasgupta"],
    },
    {
      id: 5,
      name: "Lisa Thompson",
      title: "Senior Support Agent",
      profilePhoto:
        "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",
      assignedRole: "General Support",
      bio: "Experienced in handling diverse customer inquiries and escalations.",
      supportTags: ["General Support", "Escalations", "Billing"],
      languages: ["English", "French"],
      availability: "Available",
      timezone: "EST (UTC-5)",
      lastActive: "Just now",
      avgResponseTime: "10 minutes",
      assignedModels: ["Kris Jenner", "Patrick Johnson"],
    },
    {
      id: 6,
      name: "Alex Petrov",
      title: "DevOps Support Engineer",
      profilePhoto:
        "https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face",
      assignedRole: "Infrastructure Specialist",
      bio: "Expert in deployment, scaling, and infrastructure optimization.",
      supportTags: ["DevOps", "Infrastructure", "Scaling"],
      languages: ["English", "Russian", "German"],
      availability: "Busy",
      timezone: "CET (UTC+1)",
      lastActive: "30 minutes ago",
      avgResponseTime: "18 minutes",
      assignedModels: ["Rishav Sengupta", "Ureka Sizemar"],
    },
    {
      id: 7,
      name: "Maria Garcia",
      title: "UX Support Specialist",
      profilePhoto:
        "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face",
      assignedRole: "UX Consultant",
      bio: "Helps customers optimize their AI integration user experience.",
      supportTags: ["UX/UI", "Design", "User Experience"],
      languages: ["Spanish", "English", "Italian"],
      availability: "Available",
      timezone: "CET (UTC+1)",
      lastActive: "8 minutes ago",
      avgResponseTime: "14 minutes",
      assignedModels: ["Sebanti Dasgupta", "Patrick Johnson"],
    },
    {
      id: 8,
      name: "James Wilson",
      title: "Security Consultant",
      profilePhoto:
        "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
      assignedRole: "Security Expert",
      bio: "Specializes in AI security, privacy compliance, and data protection.",
      supportTags: ["Security", "Privacy", "Compliance"],
      languages: ["English"],
      availability: "Away",
      timezone: "GMT (UTC+0)",
      lastActive: "2 hours ago",
      avgResponseTime: "20 minutes",
      assignedModels: ["Sebanti Dasgupta", "Rishav Sengupta"],
    },
    {
      id: 9,
      name: "Yuki Tanaka",
      title: "AI Research Support",
      profilePhoto:
        "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=150&h=150&fit=crop&crop=face",
      assignedRole: "Research Specialist",
      bio: "Supports advanced AI research projects and experimental features.",
      supportTags: ["Research", "Experimental", "Advanced Features"],
      languages: ["Japanese", "English"],
      availability: "Available",
      timezone: "JST (UTC+9)",
      lastActive: "12 minutes ago",
      avgResponseTime: "22 minutes",
      assignedModels: ["Sebanti Dasgupta", "Rishav Sengupta", "Ureka Sizemar"],
    },
    {
      id: 10,
      name: "Robert Anderson",
      title: "Enterprise Account Manager",
      profilePhoto:
        "https://images.unsplash.com/photo-*************-f4e0f30006d5?w=150&h=150&fit=crop&crop=face",
      assignedRole: "Enterprise Lead",
      bio: "Manages large enterprise accounts and complex deployment scenarios.",
      supportTags: ["Enterprise", "Large Scale", "Deployment"],
      languages: ["English"],
      availability: "Busy",
      timezone: "PST (UTC-8)",
      lastActive: "45 minutes ago",
      avgResponseTime: "16 minutes",
      assignedModels: ["Sebanti Dasgupta", "Rishav Sengupta"],
    },
    {
      id: 11,
      name: "Sophie Martin",
      title: "Mobile Integration Specialist",
      profilePhoto:
        "https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face",
      assignedRole: "Mobile Expert",
      bio: "Specializes in mobile app integrations and iOS/Android SDKs.",
      supportTags: ["Mobile", "iOS", "Android", "SDK"],
      languages: ["French", "English"],
      availability: "Available",
      timezone: "CET (UTC+1)",
      lastActive: "3 minutes ago",
      avgResponseTime: "11 minutes",
      assignedModels: ["Kris Jenner", "Ureka Sizemar"],
    },
    {
      id: 12,
      name: "Carlos Mendoza",
      title: "Data Science Support",
      profilePhoto:
        "https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      assignedRole: "Data Scientist",
      bio: "Helps with data preprocessing, model training, and analytics.",
      supportTags: ["Data Science", "Analytics", "Training"],
      languages: ["Spanish", "English", "Portuguese"],
      availability: "Available",
      timezone: "EST (UTC-5)",
      lastActive: "7 minutes ago",
      avgResponseTime: "19 minutes",
      assignedModels: ["Sebanti Dasgupta", "Rishav Sengupta"],
    },
    {
      id: 13,
      name: "Nina Kowalski",
      title: "Content Moderation Specialist",
      profilePhoto:
        "https://images.unsplash.com/photo-*************-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
      assignedRole: "Content Expert",
      bio: "Expert in content policy, moderation, and safety guidelines.",
      supportTags: ["Content Policy", "Moderation", "Safety"],
      languages: ["Polish", "English", "German"],
      availability: "Busy",
      timezone: "CET (UTC+1)",
      lastActive: "20 minutes ago",
      avgResponseTime: "13 minutes",
      assignedModels: ["Sebanti Dasgupta", "Patrick Johnson"],
    },
    {
      id: 14,
      name: "Ahmed Hassan",
      title: "Localization Support",
      profilePhoto:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
      assignedRole: "Localization Expert",
      bio: "Supports multilingual implementations and cultural adaptations.",
      supportTags: ["Localization", "Multilingual", "Cultural"],
      languages: ["Arabic", "English", "French"],
      availability: "Available",
      timezone: "EET (UTC+2)",
      lastActive: "1 minute ago",
      avgResponseTime: "17 minutes",
      assignedModels: ["Sebanti Dasgupta", "Kris Jenner"],
    },
    {
      id: 15,
      name: "Jennifer Liu",
      title: "Performance Optimization Lead",
      profilePhoto:
        "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",
      assignedRole: "Performance Expert",
      bio: "Focuses on API performance, rate limiting, and optimization strategies.",
      supportTags: ["Performance", "Optimization", "Rate Limits"],
      languages: ["English", "Mandarin"],
      availability: "Away",
      timezone: "PST (UTC-8)",
      lastActive: "1.5 hours ago",
      avgResponseTime: "9 minutes",
      assignedModels: ["Sebanti Dasgupta", "Rishav Sengupta", "Ureka Sizemar"],
    },
    {
      id: 16,
      name: "Thomas Mueller",
      title: "Web Integration Specialist",
      profilePhoto:
        "https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face",
      assignedRole: "Web Developer",
      bio: "Expert in web-based AI integrations and JavaScript frameworks.",
      supportTags: ["Web Development", "JavaScript", "Frameworks"],
      languages: ["German", "English"],
      availability: "Available",
      timezone: "CET (UTC+1)",
      lastActive: "4 minutes ago",
      avgResponseTime: "12 minutes",
      assignedModels: ["Kris Jenner", "Patrick Johnson"],
    },
    {
      id: 17,
      name: "Priya Patel",
      title: "Healthcare AI Specialist",
      profilePhoto:
        "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=150&h=150&fit=crop&crop=face",
      assignedRole: "Healthcare Expert",
      bio: "Specialized in healthcare AI applications and HIPAA compliance.",
      supportTags: ["Healthcare", "HIPAA", "Medical AI"],
      languages: ["English", "Hindi", "Gujarati"],
      availability: "Busy",
      timezone: "IST (UTC+5:30)",
      lastActive: "25 minutes ago",
      avgResponseTime: "21 minutes",
      assignedModels: ["Sebanti Dasgupta", "Rishav Sengupta"],
    },
    {
      id: 18,
      name: "Lucas Silva",
      title: "Gaming Integration Lead",
      profilePhoto:
        "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
      assignedRole: "Gaming Specialist",
      bio: "Focuses on AI integration in gaming, NPCs, and interactive experiences.",
      supportTags: ["Gaming", "NPCs", "Interactive AI"],
      languages: ["Portuguese", "English", "Spanish"],
      availability: "Available",
      timezone: "BRT (UTC-3)",
      lastActive: "6 minutes ago",
      avgResponseTime: "14 minutes",
      assignedModels: ["Sebanti Dasgupta", "Rishav Sengupta"],
    },
    {
      id: 19,
      name: "Anna Volkov",
      title: "Education Technology Specialist",
      profilePhoto:
        "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face",
      assignedRole: "EdTech Expert",
      bio: "Specializes in educational AI applications and learning platforms.",
      supportTags: ["Education", "Learning", "EdTech"],
      languages: ["Russian", "English"],
      availability: "Available",
      timezone: "MSK (UTC+3)",
      lastActive: "9 minutes ago",
      avgResponseTime: "16 minutes",
      assignedModels: ["Sebanti Dasgupta", "Kris Jenner"],
    },
    {
      id: 20,
      name: "Kevin O'Brien",
      title: "Finance AI Consultant",
      profilePhoto:
        "https://images.unsplash.com/photo-*************-f4e0f30006d5?w=150&h=150&fit=crop&crop=face",
      assignedRole: "Finance Expert",
      bio: "Expert in financial AI applications, risk assessment, and compliance.",
      supportTags: ["Finance", "Risk Assessment", "Compliance"],
      languages: ["English", "Irish Gaelic"],
      availability: "Away",
      timezone: "GMT (UTC+0)",
      lastActive: "3 hours ago",
      avgResponseTime: "23 minutes",
      assignedModels: ["Sebanti Dasgupta", "Rishav Sengupta"],
    },
  ];

  // Pagination logic
  const totalPages = Math.ceil(dummyContacts.length / itemsPerPage);
  const currentContacts = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return dummyContacts.slice(startIndex, startIndex + itemsPerPage);
  }, [currentPage]);

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const getAvailabilityColor = (availability) => {
    switch (availability) {
      case "Available":
        return "bg-green-500";
      case "Busy":
        return "bg-yellow-500";
      case "Away":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const handleAction = (action, contact) => {
    console.log(`${action} action for contact:`, contact);
    // Implementation would go here
  };

  const exportData = (format) => {
    const dataToExport = currentContacts.map((contact) => ({
      name: contact.name,
      title: contact.title,
      role: contact.assignedRole,
      tags: contact.supportTags.join(", "),
      languages: contact.languages.join(", "),
      availability: contact.availability,
      timezone: contact.timezone,
      avgResponseTime: contact.avgResponseTime,
      assignedModels: contact.assignedModels.join(", "),
    }));

    if (format === "csv") {
      const csv = [
        Object.keys(dataToExport[0]).join(","),
        ...dataToExport.map((row) => Object.values(row).join(",")),
      ].join("\n");

      const blob = new Blob([csv], { type: "text/csv" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "assigned-contacts.csv";
      a.click();
    } else {
      const json = JSON.stringify(dataToExport, null, 2);
      const blob = new Blob([json], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "assigned-contacts.json";
      a.click();
    }
  };

  const CardView = () => (
    <div className="grid gap-6">
      {currentContacts.map((contact) => (
        <div
          key={contact.id}
          className=" bg-white rounded-lg shadow-md border border-gray-200 p-6"
        >
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <img
                  src={contact.profilePhoto}
                  alt={contact.name}
                  className="w-16 h-16 rounded-full object-cover"
                />
                <div
                  className={`absolute -bottom-1 -right-1 w-5 h-5 rounded-full border-2 border-white ${getAvailabilityColor(contact.availability)}`}
                ></div>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {contact.name}
                </h3>
                <p className="text-sm text-gray-600">{contact.title}</p>
                <p className="text-sm font-medium text-blue-600">
                  {contact.assignedRole}
                </p>
              </div>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => handleAction("edit", contact)}
                className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                title="Edit Assignment"
              >
                <Edit size={16} />
              </button>
              <button
                onClick={() => handleAction("reassign", contact)}
                className="p-2 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                title="Reassign"
              >
                <RefreshCw size={16} />
              </button>
              <button
                onClick={() => handleAction("clone", contact)}
                className="p-2 text-gray-500 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-colors"
                title="Clone Assignment"
              >
                <Copy size={16} />
              </button>
              <button
                onClick={() => handleAction("remove", contact)}
                className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                title="Remove"
              >
                <Trash2 size={16} />
              </button>
            </div>
          </div>

          <p className="text-sm text-gray-700 mb-4">{contact.bio}</p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <MessageCircle size={14} className="text-gray-500" />
                <span className="text-xs font-medium text-gray-700">
                  Support Tags:
                </span>
              </div>
              <div className="flex flex-wrap gap-1">
                {contact.supportTags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>

            <div>
              <div className="flex items-center space-x-2 mb-2">
                <Globe size={14} className="text-gray-500" />
                <span className="text-xs font-medium text-gray-700">
                  Languages:
                </span>
              </div>
              <div className="flex flex-wrap gap-1">
                {contact.languages.map((lang, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"
                  >
                    {lang}
                  </span>
                ))}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4 pt-4 border-t border-gray-200">
            <div className="text-center">
              <div className="flex items-center justify-center space-x-1 mb-1">
                <Activity size={12} className="text-gray-500" />
                <span className="text-xs text-gray-500">Status</span>
              </div>
              <span
                className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${
                  contact.availability === "Available"
                    ? "bg-green-100 text-green-800"
                    : contact.availability === "Busy"
                      ? "bg-yellow-100 text-yellow-800"
                      : "bg-red-100 text-red-800"
                }`}
              >
                {contact.availability}
              </span>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center space-x-1 mb-1">
                <Globe size={12} className="text-gray-500" />
                <span className="text-xs text-gray-500">Timezone</span>
              </div>
              <p className="text-xs font-medium text-gray-900">
                {contact.timezone}
              </p>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center space-x-1 mb-1">
                <Calendar size={12} className="text-gray-500" />
                <span className="text-xs text-gray-500">Last Active</span>
              </div>
              <p className="text-xs font-medium text-gray-900">
                {contact.lastActive}
              </p>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center space-x-1 mb-1">
                <Clock size={12} className="text-gray-500" />
                <span className="text-xs text-gray-500">Avg Response</span>
              </div>
              <p className="text-xs font-medium text-gray-900">
                {contact.avgResponseTime}
              </p>
            </div>
          </div>

          <div className="mt-4 pt-4 border-t border-gray-200">
            <span className="text-xs font-medium text-gray-700">
              Assigned Models:
            </span>
            <div className="flex flex-wrap gap-1 mt-1">
              {contact.assignedModels.map((model, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full"
                >
                  {model}
                </span>
              ))}
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const TableView = () => (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Contact
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Role & Tags
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Languages
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Availability
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Performance
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {currentContacts.map((contact) => (
              <tr key={contact.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="relative flex-shrink-0 h-12 w-12">
                      <img
                        className="h-12 w-12 rounded-full object-cover"
                        src={contact.profilePhoto}
                        alt={contact.name}
                      />
                      <div
                        className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${getAvailabilityColor(contact.availability)}`}
                      ></div>
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">
                        {contact.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {contact.title}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm font-medium text-blue-600 mb-1">
                    {contact.assignedRole}
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {contact.supportTags.slice(0, 2).map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                    {contact.supportTags.length > 2 && (
                      <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                        +{contact.supportTags.length - 2}
                      </span>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="flex flex-wrap gap-1">
                    {contact.languages.map((lang, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"
                      >
                        {lang}
                      </span>
                    ))}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${
                      contact.availability === "Available"
                        ? "bg-green-100 text-green-800"
                        : contact.availability === "Busy"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-red-100 text-red-800"
                    }`}
                  >
                    {contact.availability}
                  </span>
                  <div className="text-xs text-gray-500 mt-1">
                    {contact.timezone}
                  </div>
                  <div className="text-xs text-gray-500">
                    {contact.lastActive}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div className="text-sm font-medium">
                    {contact.avgResponseTime}
                  </div>
                  <div className="text-xs text-gray-500">avg response</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleAction("edit", contact)}
                      className="text-blue-600 hover:text-blue-900 hover:bg-blue-50 p-1 rounded"
                      title="Edit"
                    >
                      <Edit size={14} />
                    </button>
                    <button
                      onClick={() => handleAction("reassign", contact)}
                      className="text-green-600 hover:text-green-900 hover:bg-green-50 p-1 rounded"
                      title="Reassign"
                    >
                      <RefreshCw size={14} />
                    </button>
                    <button
                      onClick={() => handleAction("clone", contact)}
                      className="text-purple-600 hover:text-purple-900 hover:bg-purple-50 p-1 rounded"
                      title="Clone"
                    >
                      <Copy size={14} />
                    </button>
                    <button
                      onClick={() => handleAction("remove", contact)}
                      className="text-red-600 hover:text-red-900 hover:bg-red-50 p-1 rounded"
                      title="Remove"
                    >
                      <Trash2 size={14} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const Pagination = () => (
    <div className="flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200 sm:px-6">
      <div className="flex justify-between flex-1 sm:hidden">
        <button
          onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
          disabled={currentPage === 1}
          className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Previous
        </button>
        <button
          onClick={() =>
            handlePageChange(Math.min(totalPages, currentPage + 1))
          }
          disabled={currentPage === totalPages}
          className="relative ml-3 inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Next
        </button>
      </div>
      <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
        <div>
          <p className="text-sm text-gray-700">
            Showing{" "}
            <span className="font-medium">
              {(currentPage - 1) * itemsPerPage + 1}
            </span>{" "}
            to{" "}
            <span className="font-medium">
              {Math.min(currentPage * itemsPerPage, dummyContacts.length)}
            </span>{" "}
            of <span className="font-medium">{dummyContacts.length}</span>{" "}
            results
          </p>
        </div>
        <div>
          <nav
            className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
            aria-label="Pagination"
          >
            <button
              onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft size={16} />
            </button>

            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                  page === currentPage
                    ? "z-10 bg-blue-50 border-blue-500 text-blue-600"
                    : "bg-white border-gray-300 text-gray-500 hover:bg-gray-50"
                }`}
              >
                {page}
              </button>
            ))}

            <button
              onClick={() =>
                handlePageChange(Math.min(totalPages, currentPage + 1))
              }
              disabled={currentPage === totalPages}
              className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight size={16} />
            </button>
          </nav>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Assigned Contact Overview
              </h1>
              <p className="mt-2 text-gray-600">
                Manage and monitor your assigned support contacts
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex bg-white rounded-lg border border-gray-200 p-1">
                <button
                  onClick={() => setViewMode("cards")}
                  className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    viewMode === "cards"
                      ? "bg-blue-100 text-blue-700"
                      : "text-gray-500 hover:text-gray-700"
                  }`}
                >
                  Cards
                </button>
                <button
                  onClick={() => setViewMode("table")}
                  className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    viewMode === "table"
                      ? "bg-blue-100 text-blue-700"
                      : "text-gray-500 hover:text-gray-700"
                  }`}
                >
                  Table
                </button>
              </div>

              <div className="flex space-x-2">
                <button
                  onClick={() => exportData("csv")}
                  className="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors"
                >
                  <Download size={16} className="mr-2" />
                  Export CSV
                </button>
                <button
                  onClick={() => exportData("json")}
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Download size={16} className="mr-2" />
                  Export JSON
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <User className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">
                  Total Contacts
                </p>
                <p className="text-2xl font-semibold text-gray-900">
                  {dummyContacts.length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Activity className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">
                  Available Now
                </p>
                <p className="text-2xl font-semibold text-gray-900">
                  {
                    dummyContacts.filter((c) => c.availability === "Available")
                      .length
                  }
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">
                  Avg Response Time
                </p>
                <p className="text-2xl font-semibold text-gray-900">
                  {Math.round(
                    dummyContacts.reduce(
                      (acc, contact) => acc + parseInt(contact.avgResponseTime),
                      0,
                    ) / dummyContacts.length,
                  )}{" "}
                  min
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Globe className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">
                  Languages Covered
                </p>
                <p className="text-2xl font-semibold text-gray-900">
                  {new Set(dummyContacts.flatMap((c) => c.languages)).size}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="space-y-6">
          {viewMode === "cards" ? <CardView /> : <TableView />}

          {/* Pagination */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <Pagination />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Supportcontactmanagement;
