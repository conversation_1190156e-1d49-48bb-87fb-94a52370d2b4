import mongoose from "mongoose";

const groupMessageSchema = new mongoose.Schema({
  convoId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: "GroupConversation",
  },
  senderId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: "User",
  },

  text: { type: String, default: "" },
  attachments: [
    {
      url: String,
      type: { type: String, enum: ["image", "video", "document", "audio"] },
    },
  ],
  type: {
    type: String,
    enum: ["textonly", "attachmentonly", "mixed", "system"],
    default: "textonly",
  },

  replyTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "GroupMessage",
    default: null,
  },
  status: {
    type: String,
    enum: ["sent", "delivered", "seen", "error"],
    default: "sent",
  },
  seenBy: [{ type: mongoose.Schema.Types.ObjectId, ref: "User" }],
  deliveredTo: [{ type: mongoose.Schema.Types.ObjectId, ref: "User" }],

  deletedFor: [{ type: mongoose.Schema.Types.ObjectId, ref: "User" }],
  deletedForAll: { type: Boolean, default: false },

  reactions: [
    {
      userId: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
      emoji: String,
    },
  ],

  createdAt: { type: Date, default: Date.now },
});

export default mongoose.model("GroupMessage", groupMessageSchema);
