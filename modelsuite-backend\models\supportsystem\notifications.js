import mongoose from "mongoose";

const notificationSchema = new mongoose.Schema(
  {
    sender: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "SupportSystem",
      required: true,
    },
    receiver: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: true,
    },
    type: {
      type: String,
      enum: ["info", "warning", "alert", "chat"],
      default: "info",
    },
    message: {
      type: String,
      required: true,
    },
    read: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
  },
);

// Index for faster unread notifications
notificationSchema.index({ receiver: 1, read: 1 });

const Notificationfromstafftomodel = mongoose.model(
  "Notificationfromstafftomodel",
  notificationSchema,
);
export default Notificationfromstafftomodel;
