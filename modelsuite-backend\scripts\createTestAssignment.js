import mongoose from "mongoose";
import VoiceAssignment from "../models/voice/VoiceAssignment.js";
import QuestionTemplate from "../models/voice/QuestionTemplate.js";
import QuestionSection from "../models/voice/QuestionSection.js";
import ModelUser from "../models/model.js";
import Agency from "../models/agency.js";

// Connect to MongoDB
const MONGODB_URI = process.env.MONGODB_URI || "mongodb://localhost:27017/modelsuite";

async function createTestAssignment() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log("Connected to MongoDB");

    // Find or create a test section
    let section = await QuestionSection.findOne({ title: "Friendly Sleep Messages" });
    if (!section) {
      section = await QuestionSection.create({
        title: "Friendly Sleep Messages",
        description: "A set of 20 friendly good morning and good night voice messages.",
        isDefault: true,
        createdBy: null
      });
    }

    // Create test questions
    const testQuestions = [
      "Good morning, I hope you slept well.",
      "Hey cutie, how are you?",
      "Goodnight, baby.",
      "Hello!",
      "I'm so glad to see you today!",
      "Sweet dreams, and sleep tight."
    ];

    const questions = [];
    for (const questionText of testQuestions) {
      let question = await QuestionTemplate.findOne({ text: questionText });
      if (!question) {
        question = await QuestionTemplate.create({
          text: questionText,
          sectionId: section._id,
          isDefault: true,
          createdBy: null
        });
      }
      questions.push(question._id);
    }

    // Find a model user (you'll need to have at least one model in your DB)
    const model = await ModelUser.findOne({});
    if (!model) {
      console.log("No model user found. Please create a model user first.");
      return;
    }

    // Find an agency (you'll need to have at least one agency in your DB)
    const agency = await Agency.findOne({});
    if (!agency) {
      console.log("No agency found. Please create an agency first.");
      return;
    }

    // Create test assignment
    const assignment = await VoiceAssignment.create({
      questionIds: questions,
      modelId: model._id,
      agencyId: agency._id,
      title: "Friendly Sleep Messages Script",
      description: "A set of 20 friendly good morning and good night voice messages.",
      status: "assigned",
      deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      priority: "medium",
      instructions: "Please record these messages with a friendly, warm tone. Smile while recording for the best effect.",
      assignedAt: new Date()
    });

    console.log("Test assignment created successfully!");
    console.log("Assignment ID:", assignment._id);
    console.log("Model:", model.username);
    console.log("Agency:", agency.agencyName);
    console.log("Questions:", questions.length);

  } catch (error) {
    console.error("Error creating test assignment:", error);
  } finally {
    await mongoose.disconnect();
  }
}

createTestAssignment();
