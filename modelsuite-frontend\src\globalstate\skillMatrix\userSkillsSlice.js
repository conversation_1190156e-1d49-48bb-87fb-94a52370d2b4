import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { userSkillService } from "../../utils/skillMatrixAPI";

// Async thunks for user skills
export const fetchUserSkills = createAsyncThunk(
  "userSkills/fetchUserSkills",
  async ({ userId, params = {} }, { rejectWithValue }) => {
    try {
      const response = await userSkillService.getByUser(userId, params);
      return { userId, skills: response.data.data };
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to fetch user skills",
      );
    }
  },
);

export const updateUserSkill = createAsyncThunk(
  "userSkills/updateUserSkill",
  async (skillData, { rejectWithValue }) => {
    try {
      const response = await userSkillService.update(skillData);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to update skill",
      );
    }
  },
);

export const endorseUserSkill = createAsyncThunk(
  "userSkills/endorseUserSkill",
  async (userSkillId, { rejectWithValue }) => {
    try {
      const response = await userSkillService.endorse(userSkillId);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to endorse skill",
      );
    }
  },
);

export const fetchSkillGaps = createAsyncThunk(
  "userSkills/fetchSkillGaps",
  async ({ userId, params = {} }, { rejectWithValue }) => {
    try {
      const response = await userSkillService.getGaps(userId, params);
      return { userId, gaps: response.data.data };
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to fetch skill gaps",
      );
    }
  },
);

const initialState = {
  userSkills: {}, // { userId: skills[] }
  skillGaps: {}, // { userId: gaps[] }
  loading: {
    userSkills: false,
    skillGaps: false,
    updating: false,
  },
  error: {
    userSkills: null,
    skillGaps: null,
    updating: null,
  },
};

const userSkillsSlice = createSlice({
  name: "userSkills",
  initialState,
  reducers: {
    clearErrors: (state) => {
      state.error = {
        userSkills: null,
        skillGaps: null,
        updating: null,
      };
    },
    clearUserSkills: (state, action) => {
      const userId = action.payload;
      delete state.userSkills[userId];
      delete state.skillGaps[userId];
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch user skills
      .addCase(fetchUserSkills.pending, (state) => {
        state.loading.userSkills = true;
        state.error.userSkills = null;
      })
      .addCase(fetchUserSkills.fulfilled, (state, action) => {
        state.loading.userSkills = false;
        const { userId, skills } = action.payload;
        state.userSkills[userId] = skills;
      })
      .addCase(fetchUserSkills.rejected, (state, action) => {
        state.loading.userSkills = false;
        state.error.userSkills = action.payload;
      })

      // Update user skill
      .addCase(updateUserSkill.pending, (state) => {
        state.loading.updating = true;
        state.error.updating = null;
      })
      .addCase(updateUserSkill.fulfilled, (state, action) => {
        state.loading.updating = false;
        const updatedSkill = action.payload;
        const userId = updatedSkill.user_id._id || updatedSkill.user_id;
        const skillId = updatedSkill.skill_id._id || updatedSkill.skill_id;

        // Update in userSkills
        if (!state.userSkills[userId]) {
          state.userSkills[userId] = [];
        }
        const existingIndex = state.userSkills[userId].findIndex(
          (us) => (us.skill_id._id || us.skill_id) === skillId,
        );
        if (existingIndex >= 0) {
          state.userSkills[userId][existingIndex] = updatedSkill;
        } else {
          state.userSkills[userId].push(updatedSkill);
        }
      })
      .addCase(updateUserSkill.rejected, (state, action) => {
        state.loading.updating = false;
        state.error.updating = action.payload;
      })

      // Endorse user skill
      .addCase(endorseUserSkill.pending, (state) => {
        state.loading.updating = true;
        state.error.updating = null;
      })
      .addCase(endorseUserSkill.fulfilled, (state, action) => {
        state.loading.updating = false;
        const endorsedSkill = action.payload;
        const userId = endorsedSkill.user_id._id || endorsedSkill.user_id;
        const skillId = endorsedSkill.skill_id._id || endorsedSkill.skill_id;

        // Update the endorsed skill
        if (state.userSkills[userId]) {
          const existingIndex = state.userSkills[userId].findIndex(
            (us) => (us.skill_id._id || us.skill_id) === skillId,
          );
          if (existingIndex >= 0) {
            state.userSkills[userId][existingIndex] = endorsedSkill;
          }
        }
      })
      .addCase(endorseUserSkill.rejected, (state, action) => {
        state.loading.updating = false;
        state.error.updating = action.payload;
      })

      // Fetch skill gaps
      .addCase(fetchSkillGaps.pending, (state) => {
        state.loading.skillGaps = true;
        state.error.skillGaps = null;
      })
      .addCase(fetchSkillGaps.fulfilled, (state, action) => {
        state.loading.skillGaps = false;
        const { userId, gaps } = action.payload;
        state.skillGaps[userId] = gaps;
      })
      .addCase(fetchSkillGaps.rejected, (state, action) => {
        state.loading.skillGaps = false;
        state.error.skillGaps = action.payload;
      });
  },
});

export const { clearErrors, clearUserSkills } = userSkillsSlice.actions;

// Selectors
export const selectUserSkills = (userId) => (state) =>
  state.userSkills.userSkills[userId] || [];
export const selectSkillGaps = (userId) => (state) =>
  state.userSkills.skillGaps[userId] || [];
export const selectUserSkillsLoading = (state) => state.userSkills.loading;
export const selectUserSkillsError = (state) => state.userSkills.error;

export default userSkillsSlice.reducer;
