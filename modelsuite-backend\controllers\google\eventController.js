import { google } from "googleapis";
import ModelUser from "../../models/model.js";
import { insertEventToGoogleCalendar } from "../../utils/googleCalendar.js";

export const createEventForModel = async (req, res) => {
  try {
    const { modelId, title, description, start, end, guests, timezone } =
      req.body;

    // Get the model from DB
    const modelUser = await ModelUser.findById(modelId);
    if (!modelUser || !modelUser.googleAccessToken) {
      return res
        .status(400)
        .json({ message: "Model has not connected Google Calendar" });
    }

    const eventData = {
      title,
      description,
      start,
      end,
      guests,
      timezone,
    };

    const result = await insertEventToGoogleCalendar(modelUser, eventData);

    res.status(201).json({
      message: "Event created and synced with Google Calendar",
      eventId: result.id,
      meetLink: result.hangoutLink,
      AllThings: result,
    });
  } catch (error) {
    console.error("Error creating event:", error);
    res
      .status(500)
      .json({ message: "Failed to create event", error: error.message });
  }
};

export const getModelCalendarEvents = async (req, res) => {
  try {
    const { modelId } = req.params;

    const modelUser = await ModelUser.findById(modelId);
    if (!modelUser || !modelUser.googleAccessToken) {
      return res
        .status(400)
        .json({ message: "Model has not connected Google Calendar" });
    }

    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_REDIRECT_URI,
    );

    oauth2Client.setCredentials({
      access_token: modelUser.googleAccessToken,
      refresh_token: modelUser.googleRefreshToken,
    });

    const calendar = google.calendar({ version: "v3", auth: oauth2Client });

    const { data } = await calendar.events.list({
      calendarId: "primary",
      timeMin: new Date().toISOString(),
      maxResults: 50,
      singleEvents: true,
      orderBy: "startTime",
    });

    // console.log(data);

    // Format events for frontend (like FullCalendar)
    const formatted = data.items.map((event) => ({
      id: event.id,
      title: event.summary,
      start: event.start?.dateTime || event.start?.date,
      end: event.end?.dateTime || event.end?.date,
      meetLink: event.hangoutLink || null,
      description: event.description || null,
      timezone: event.start?.timeZone || null,
      guests: event.attendees || [],
    }));

    res.status(200).json(formatted);
  } catch (error) {
    console.error("Fetch calendar error:", error);
    res
      .status(500)
      .json({ message: "Failed to fetch events", error: error.message });
  }
};
