import TaskCard from "../../models/task/Card.js";
import TaskList from "../../models/task/List.js";
import Board from "../../models/task/Board.js";
import cloudinary from "../../config/cloudinary.js";
import { Readable } from "stream";

// Create a new card
export const createCard = async (req, res) => {
  try {
    const { listId } = req.params;
    const { title, description, dueDate, members, labels, priority } = req.body;

    // Validate priority if provided
    const validPriorities = ["critical", "high", "medium", "low"];
    if (priority && !validPriorities.includes(priority)) {
      return res.status(400).json({
        error:
          "Invalid priority value. Must be one of: critical, high, medium, low",
      });
    }

    // Check list exists
    const list = await TaskList.findById(listId);
    if (!list) {
      return res.status(404).json({ error: "List not found" });
    }

    // Get highest position
    const lastCard = await TaskCard.findOne({ listId: listId }).sort(
      "-position",
    );
    const position = lastCard ? lastCard.position + 1 : 0;

    const card = await TaskCard.create({
      title,
      description,
      listId: listId,
      boardId: list.boardId,
      position,
      dueDate,
      assignedTo: members,
      labels,
      priority: priority || "medium", // Set default priority if not provided
    });

    // Add card to list
    list.cards.push(card._id);
    await list.save();

    res.status(201).json(card);
  } catch (err) {
    console.error("Failed to create card:", err);
    res.status(500).json({ error: "Failed to create card" });
  }
};

// Get a card by ID
export const getCardById = async (req, res) => {
  try {
    const { cardId } = req.params;
    const card = await TaskCard.findById(cardId)
      .populate("assignedTo", "fullName profilePhoto")
      .populate({
        path: "comments.author",
        select: "fullName profilePhoto agencyName",
      })
      .populate({
        path: "onHoldBy.user",
        select: "fullName profilePhoto agencyName",
      })
      .select(
        "title description listId boardId position dueDate assignedTo labels comments attachments isComplete priority isOnHold onHoldReason onHoldBy",
      );

    if (!card) {
      return res.status(404).json({ error: "Card not found" });
    }

    res.json(card);
  } catch (err) {
    console.error("Failed to get card:", err);
    res.status(500).json({ error: "Failed to get card" });
  }
};

// Put card on hold
export const putCardOnHold = async (req, res) => {
  try {
    const { cardId } = req.params;
    const { reason } = req.body;

    if (!reason || !reason.trim()) {
      return res.status(400).json({ error: "Hold reason is required" });
    }

    const card = await TaskCard.findById(cardId);
    if (!card) {
      return res.status(404).json({ error: "Card not found" });
    }

    // Find the "On Hold" list
    const onHoldList = await TaskList.findOne({
      boardId: card.boardId,
      title: "On Hold",
    });

    if (!onHoldList) {
      return res.status(404).json({ error: "On Hold list not found" });
    }

    // Remove card from current list
    const currentList = await TaskList.findById(card.listId);
    if (currentList) {
      currentList.cards = currentList.cards.filter(
        (id) => id.toString() !== cardId,
      );
      await currentList.save();
    }

    // Add card to On Hold list
    onHoldList.cards.push(card._id);
    await onHoldList.save();

    // Update card
    card.isOnHold = true;
    card.onHoldReason = reason.trim();
    card.onHoldBy = {
      user: req.user.id,
      userType: req.user.role === "model" ? "ModelUser" : "Agency",
      timestamp: new Date(),
    };
    card.listId = onHoldList._id;

    await card.save();

    // Return updated card with populated fields
    const updatedCard = await TaskCard.findById(cardId)
      .populate("assignedTo", "fullName profilePhoto")
      .populate({
        path: "onHoldBy.user",
        select: "fullName profilePhoto agencyName",
      });

    res.json(updatedCard);
  } catch (err) {
    console.error("Failed to put card on hold:", err);
    res.status(500).json({ error: "Failed to put card on hold" });
  }
};

// Update a card
export const updateCard = async (req, res) => {
  try {
    const { cardId } = req.params;
    const updates = req.body;

    // Validate priority if provided
    if (updates.priority) {
      const validPriorities = ["critical", "high", "medium", "low"];
      if (!validPriorities.includes(updates.priority)) {
        return res.status(400).json({
          error:
            "Invalid priority value. Must be one of: critical, high, medium, low",
        });
      }
    }

    const card = await TaskCard.findById(cardId);
    if (!card) {
      return res.status(404).json({ error: "Card not found" });
    }

    Object.assign(card, updates);

    await card.save();

    res.json(card);
  } catch (err) {
    console.error("Failed to update card:", err);
    res.status(500).json({ error: "Failed to update card" });
  }
};

// Delete a card
export const deleteCard = async (req, res) => {
  try {
    const { cardId } = req.params;

    const card = await TaskCard.findById(cardId);
    if (!card) {
      return res.status(404).json({ error: "Card not found" });
    }

    // Remove card from list
    const list = await TaskList.findById(card.listId);
    if (list) {
      list.cards = list.cards.filter((id) => id.toString() !== cardId);
      await list.save();
    }

    await card.deleteOne();

    res.json({ message: "Card deleted successfully" });
  } catch (err) {
    console.error("Failed to delete card:", err);
    res.status(500).json({ error: "Failed to delete card" });
  }
};

// Move card to different list
export const moveCard = async (req, res) => {
  try {
    const { cardId } = req.params;
    const { targetListId, position } = req.body;

    const card = await TaskCard.findById(cardId);
    if (!card) {
      return res.status(404).json({ error: "Card not found" });
    }

    const sourceList = await TaskList.findById(card.listId);
    const targetList = await TaskList.findById(targetListId);

    if (!targetList) {
      return res.status(404).json({ error: "Target list not found" });
    }

    // Remove from source list
    if (sourceList) {
      sourceList.cards = sourceList.cards.filter(
        (id) => id.toString() !== cardId,
      );
      await sourceList.save();
    }

    // Update card's list and position
    card.listId = targetListId;
    card.position = position;
    await card.save();

    // Add to target list
    targetList.cards.splice(position, 0, card._id);
    await targetList.save();

    res.json(card);
  } catch (err) {
    console.error("Failed to move card:", err);
    res.status(500).json({ error: "Failed to move card" });
  }
};

// Helper function to get resource type for Cloudinary
const getResourceType = (mimetype) => {
  if (mimetype.startsWith("image/")) return "image";
  if (mimetype.startsWith("video/")) return "video";
  if (mimetype === "application/pdf") return "raw";
  return "auto";
};

// Helper function to get file format
const getFormat = (mimetype) => {
  if (mimetype === "application/pdf") return "pdf";
  return undefined; // Let Cloudinary auto-detect for other types
};

// Helper function to upload to Cloudinary using streams
const uploadToCloudinary = (buffer, options = {}) => {
  return new Promise((resolve, reject) => {
    const stream = cloudinary.uploader.upload_stream(
      options,
      (error, result) => {
        if (error) reject(error);
        else resolve(result);
      },
    );

    // Convert buffer to stream and pipe to Cloudinary
    const readable = new Readable({
      read() {
        this.push(buffer);
        this.push(null);
      },
    });
    readable.pipe(stream);
  });
};

// Add attachment to card
export const addAttachment = async (req, res) => {
  try {
    const { cardId } = req.params;
    const file = req.file;

    if (!file) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    const card = await TaskCard.findById(cardId);
    if (!card) {
      return res.status(404).json({ error: "Card not found" });
    }

    // Configure upload based on file type
    const resourceType = getResourceType(file.mimetype);
    const format = getFormat(file.mimetype);

    // Upload to Cloudinary with specific options for PDFs
    const uploadOptions = {
      folder: "card-attachments",
      resource_type: resourceType,
      format: format,
      public_id: `${cardId}-${Date.now()}`, // Unique identifier
    };

    // For PDFs, add specific options
    if (file.mimetype === "application/pdf") {
      uploadOptions.flags = "attachment";
      uploadOptions.use_filename = true;
      uploadOptions.unique_filename = true;
    }

    const result = await uploadToCloudinary(file.buffer, uploadOptions);

    // For PDFs, use the secure_url directly as it's already configured for download
    const fileUrl = result.secure_url;

    // Add attachment to card
    card.attachments.push({
      url: fileUrl,
      publicId: result.public_id,
      type: file.mimetype,
      originalName: file.originalname,
      uploadedBy: req.user.id,
      uploadedAt: new Date(),
    });

    await card.save();

    res.status(201).json(card.attachments[card.attachments.length - 1]);
  } catch (err) {
    console.error("Failed to add attachment:", err);
    res.status(500).json({ error: "Failed to add attachment" });
  }
};

// Get all attachments for a card
export const getAttachments = async (req, res) => {
  try {
    const { cardId } = req.params;

    const card = await TaskCard.findById(cardId).populate(
      "attachments.uploadedBy",
      "fullName profilePhoto",
    );

    if (!card) {
      return res.status(404).json({ error: "Card not found" });
    }

    res.json(card.attachments);
  } catch (err) {
    console.error("Failed to get attachments:", err);
    res.status(500).json({ error: "Failed to get attachments" });
  }
};

// Delete an attachment
export const deleteAttachment = async (req, res) => {
  try {
    const { cardId, attachmentId } = req.params;

    const card = await TaskCard.findById(cardId);
    if (!card) {
      return res.status(404).json({ error: "Card not found" });
    }

    // Find attachment index
    const attachmentIndex = card.attachments.findIndex(
      (att) => att._id.toString() === attachmentId,
    );

    if (attachmentIndex === -1) {
      return res.status(404).json({ error: "Attachment not found" });
    }

    const attachment = card.attachments[attachmentIndex];

    // Delete from Cloudinary if publicId exists
    if (attachment.publicId) {
      try {
        const resourceType = getResourceType(attachment.type);
        await cloudinary.uploader.destroy(attachment.publicId, {
          resource_type: resourceType,
        });
      } catch (cloudinaryErr) {
        console.error("Failed to delete from Cloudinary:", cloudinaryErr);
        // Continue even if Cloudinary deletion fails
      }
    }

    // Remove attachment from array using splice
    card.attachments.splice(attachmentIndex, 1);
    await card.save();

    res.json({ message: "Attachment deleted successfully" });
  } catch (err) {
    console.error("Failed to delete attachment:", err);
    res.status(500).json({ error: "Failed to delete attachment" });
  }
};

// Get comments for a card
export const getCardComments = async (req, res) => {
  try {
    const { cardId } = req.params;

    const card = await TaskCard.findById(cardId).populate({
      path: "comments.author",
      select: "fullName profilePhoto agencyName",
    });

    if (!card) {
      return res.status(404).json({ error: "Card not found" });
    }

    res.json(card.comments);
  } catch (err) {
    console.error("Failed to get comments:", err);
    res.status(500).json({ error: "Failed to get comments" });
  }
};

// Add a comment to a card
export const addComment = async (req, res) => {
  try {
    const { cardId } = req.params;
    const { text } = req.body;
    const userId = req.user.id;
    const userRole = req.user.role;

    if (!text || !text.trim()) {
      return res.status(400).json({ error: "Comment text is required" });
    }

    const card = await TaskCard.findById(cardId);
    if (!card) {
      return res.status(404).json({ error: "Card not found" });
    }

    // Determine the author type based on user role
    const authorType = userRole === "model" ? "ModelUser" : "Agency";

    // Add comment to card
    const newComment = {
      text: text.trim(),
      author: userId,
      authorType: authorType,
      createdAt: new Date(),
    };

    card.comments.push(newComment);
    await card.save();

    // Populate the author information for the response
    await card.populate({
      path: "comments.author",
      select: "fullName profilePhoto agencyName",
    });

    // Return the newly added comment
    const addedComment = card.comments[card.comments.length - 1];
    res.status(201).json(addedComment);
  } catch (err) {
    console.error("Failed to add comment:", err);
    res.status(500).json({ error: "Failed to add comment" });
  }
};

// Get individual tasks
export const getIndividualTasks = async (req, res) => {
  try {
    const { modelId } = req.query;
    const userRole = req.user.role;

    if (!modelId) {
      return res.status(400).json({ error: "Model ID is required" });
    }

    // If user is a model, ensure they can only see their own tasks
    if (userRole === "model" && modelId !== req.user.id) {
      return res
        .status(403)
        .json({ error: "Not authorized to view these tasks" });
    }

    const tasks = await TaskCard.find({
      modelId,
      isIndividualTask: true,
      isArchived: false,
    })
      .populate("assignedTo", "fullName profilePhoto")
      .populate({
        path: "onHoldBy.user",
        select: "fullName profilePhoto agencyName",
      })
      .sort("-createdAt");

    res.json(tasks);
  } catch (err) {
    console.error("Failed to get individual tasks:", err);
    res.status(500).json({ error: "Failed to get individual tasks" });
  }
};

// Create individual task
export const createIndividualTask = async (req, res) => {
  try {
    const { title, description, dueDate, members, labels, priority, modelId } =
      req.body;

    if (!modelId) {
      return res.status(400).json({ error: "Model ID is required" });
    }

    // Validate priority if provided
    const validPriorities = ["critical", "high", "medium", "low"];
    if (priority && !validPriorities.includes(priority)) {
      return res.status(400).json({
        error:
          "Invalid priority value. Must be one of: critical, high, medium, low",
      });
    }

    const task = await TaskCard.create({
      title,
      description,
      dueDate,
      assignedTo: members,
      labels,
      priority: priority || "medium",
      modelId,
      isIndividualTask: true,
      createdBy: req.user.id,
      createdByRole: req.user.role,
    });

    const populatedTask = await TaskCard.findById(task._id).populate(
      "assignedTo",
      "fullName profilePhoto",
    );

    res.status(201).json(populatedTask);
  } catch (err) {
    console.error("Failed to create individual task:", err);
    res.status(500).json({ error: "Failed to create individual task" });
  }
};
