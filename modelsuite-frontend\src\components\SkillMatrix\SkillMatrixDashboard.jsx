import React, { useState, useEffect, useCallback } from "react";
import { Search, Filter, Users, Edit3, Plus } from "lucide-react";
import SkillMatrixTable from "./SkillMatrixTable";
import SkillManagement from "./SkillManagement";
import UserSkillProfile from "./UserSkillProfile";

// Custom debounce hook
const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

const SkillMatrixDashboard = ({ currentUser }) => {
  const [activeTab, setActiveTab] = useState("matrix");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedUser, setSelectedUser] = useState(null);

  // Debounced search term (500ms delay)
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  const tabs = [
    { id: "matrix", label: "Skill Matrix", icon: Users },
    { id: "management", label: "Manage Skills", icon: Edit3 },
  ];

  const fetchSkillMatrix = useCallback(async () => {
    // This function is mainly used to refresh data
    // The actual data fetching is now handled in SkillMatrixTable component
  }, []);

  if (selectedUser) {
    return (
      <UserSkillProfile
        user={selectedUser}
        onBack={() => setSelectedUser(null)}
        currentUser={currentUser}
      />
    );
  }

  return (
    <div className="bg-gray-800 rounded-xl shadow-xl border border-gray-700">
      {/* Header */}
      <div className="border-b border-gray-700 px-6 py-4 bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 rounded-t-xl">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-white">Skill Matrix</h1>
            <p className="text-gray-400 mt-1">
              Track and manage team member capabilities
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search staff names..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400"
              />
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex space-x-8 mt-6">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 pb-2 border-b-2 transition-colors duration-200 ${
                  activeTab === tab.id
                    ? "border-blue-500 text-blue-400"
                    : "border-transparent text-gray-400 hover:text-gray-300"
                }`}
              >
                <Icon className="w-4 h-4" />
                <span className="font-medium">{tab.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Content */}
      <div className="p-6 bg-gray-800">
        {activeTab === "matrix" && (
          <SkillMatrixTable searchTerm={debouncedSearchTerm} />
        )}

        {activeTab === "management" && (
          <SkillManagement onSkillsUpdate={fetchSkillMatrix} />
        )}
      </div>

      {/* Proficiency Level Legend */}
      {activeTab === "matrix" && (
        <div className="border-t border-gray-700 px-6 py-4 bg-gray-800 rounded-b-xl">
          <div className="flex items-center flex-wrap gap-6">
            <span className="text-sm font-medium text-gray-300">
              Proficiency Levels:
            </span>

            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 rounded-full bg-yellow-400 mr-2"></div>
              <span className="px-3 py-1 rounded-full text-xs font-medium uppercase tracking-wide bg-yellow-500/20 text-yellow-400 border border-yellow-500/30">
                BEGINNER
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 rounded-full bg-blue-400 mr-2"></div>
              <span className="px-3 py-1 rounded-full text-xs font-medium uppercase tracking-wide bg-blue-500/20 text-blue-400 border border-blue-500/30">
                INTERMEDIATE
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 rounded-full bg-purple-400 mr-2"></div>
              <span className="px-3 py-1 rounded-full text-xs font-medium uppercase tracking-wide bg-purple-500/20 text-purple-400 border border-purple-500/30">
                ADVANCED
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 rounded-full bg-green-400 mr-2"></div>
              <span className="px-3 py-1 rounded-full text-xs font-medium uppercase tracking-wide bg-green-500/20 text-green-400 border border-green-500/30">
                EXPERT
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 rounded-full bg-gray-500 mr-2"></div>
              <span className="px-3 py-1 rounded-full text-xs font-medium uppercase tracking-wide bg-gray-600/50 text-gray-400 border border-gray-600/30">
                N/A
              </span>
              <span className="text-xs text-gray-500 ml-2">
                Not assigned to category
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SkillMatrixDashboard;
