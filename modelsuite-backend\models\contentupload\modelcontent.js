import mongoose from "mongoose";

const Schema = new mongoose.Schema(
  {
    id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    category: {
      type: String,
      required: true,
    },
    notes: {
      type: String,
      required: true,
    },
    urls: {
      type: [String],
      required: true,
    },
  },
  { timestamps: true },
);
const contentModelSchema = mongoose.model("modelcontent", Schema);
export default contentModelSchema;
