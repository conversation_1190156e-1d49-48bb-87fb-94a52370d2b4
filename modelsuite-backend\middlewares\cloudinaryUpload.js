// middlewares/cloudinaryUpload.js
import { CloudinaryStorage } from "multer-storage-cloudinary";
import multer from "multer";
import cloudinary from "../config/cloudinary.js";

const storage = new CloudinaryStorage({
  cloudinary,
  params: async (req, file) => {
    const isImage = file.mimetype.startsWith("image/");

    return {
      folder: "modelsuite/invoices",
      allowed_formats: ["pdf", "jpg", "jpeg", "png"],
      resource_type: isImage ? "image" : "raw", // ✅ THIS FIXES THE ISSUE
      ...(isImage && {
        transformation: [{ width: 1000, crop: "limit" }],
      }),
    };
  },
});
const upload = multer({ storage });

export default upload;
