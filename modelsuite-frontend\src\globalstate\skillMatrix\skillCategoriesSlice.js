import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { skillCategoryService } from "../../utils/skillMatrixAPI";

// Async thunks for skill categories
export const fetchSkillCategories = createAsyncThunk(
  "skillCategories/fetchSkillCategories",
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await skillCategoryService.getAll(params);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to fetch skill categories",
      );
    }
  },
);

export const createSkillCategory = createAsyncThunk(
  "skillCategories/createSkillCategory",
  async (categoryData, { rejectWithValue }) => {
    try {
      const response = await skillCategoryService.create(categoryData);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to create skill category",
      );
    }
  },
);

export const updateSkillCategory = createAsyncThunk(
  "skillCategories/updateSkillCategory",
  async ({ id, ...categoryData }, { rejectWithValue }) => {
    try {
      const response = await skillCategoryService.update(id, categoryData);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to update skill category",
      );
    }
  },
);

export const deleteSkillCategory = createAsyncThunk(
  "skillCategories/deleteSkillCategory",
  async (categoryId, { rejectWithValue }) => {
    try {
      await skillCategoryService.delete(categoryId);
      return categoryId;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to delete skill category",
      );
    }
  },
);

const initialState = {
  list: [],
  loading: false,
  error: null,
};

const skillCategoriesSlice = createSlice({
  name: "skillCategories",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch skill categories
      .addCase(fetchSkillCategories.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSkillCategories.fulfilled, (state, action) => {
        state.loading = false;
        state.list = action.payload;
      })
      .addCase(fetchSkillCategories.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Create skill category
      .addCase(createSkillCategory.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createSkillCategory.fulfilled, (state, action) => {
        state.loading = false;
        state.list.push(action.payload);
      })
      .addCase(createSkillCategory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update skill category
      .addCase(updateSkillCategory.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateSkillCategory.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.list.findIndex(
          (category) => category._id === action.payload._id,
        );
        if (index !== -1) {
          state.list[index] = action.payload;
        }
      })
      .addCase(updateSkillCategory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete skill category
      .addCase(deleteSkillCategory.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteSkillCategory.fulfilled, (state, action) => {
        state.loading = false;
        state.list = state.list.filter(
          (category) => category._id !== action.payload,
        );
      })
      .addCase(deleteSkillCategory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError } = skillCategoriesSlice.actions;

// Selectors
export const selectSkillCategories = (state) => state.skillCategories.list;
export const selectSkillCategoriesLoading = (state) =>
  state.skillCategories.loading;
export const selectSkillCategoriesError = (state) =>
  state.skillCategories.error;

export default skillCategoriesSlice.reducer;
