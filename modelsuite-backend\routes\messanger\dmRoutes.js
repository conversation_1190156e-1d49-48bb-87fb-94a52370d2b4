import express from "express";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import {
  createNewDm,
  deleteAllDmsOfUser,
  deleteLatestMsg,
  fetchOlderDMMessages,
  getAllDMConversations,
  getPinnedMessagesWithUserDetails,
} from "../../controllers/messages/dmController.js";
const router = express.Router();

router.get("/getAllDMConversations", verifyToken, getAllDMConversations);
router.post("/createNewDm", verifyToken, createNewDm);
router.get("/fetchOlderDmMessages", verifyToken, fetchOlderDMMessages);
router.delete("/deleteLatestMsg/:convoId", deleteLatestMsg);
router.get(
  "/pinnedMessagesWithUserDetails",
  verifyToken,
  getPinnedMessagesWithUserDetails,
);

router.delete("/deleteAllDmsOfUser", deleteAllDmsOfUser);

export default router;
