import express from "express";
import {
  createSupportStaff,
  updateSupportStaff,
  getAllSupportStaff,
  getSupportStaffById,
  deleteSupportStaff,
  filterBySupportArea,
  getAllPrimarySupportAreas,
  //   registerSocketHandlers,
  getRecentChatWithModel,
  countUnseenMessages,
  getRecentNotificationFromStaff,
  countUnseenNotifications,
  markMessagesAsRead,
  markNotificationsAsRead,
} from "../../controllers/supportsystem/supportsystem.js";

const router = express.Router();

// ============== SUPPORT STAFF ROUTES ==============
router.post("/", createSupportStaff); // Create staff
router.get("/", getAllSupportStaff); // Get all staff
router.get("/filter", filterBySupportArea); // Filter by support area
router.get("/allroles", getAllPrimarySupportAreas); // Get first element of support_areas
router.get("/:id", getSupportStaffById); // Get single staff
router.put("/:id", updateSupportStaff); // Update staff
router.delete("/:id", deleteSupportStaff); // Delete staff

// ============== CHAT ROUTES ==============
router.get("/chat/recent/:modelId/:staffId", getRecentChatWithModel); // Recent chat between model & staff
router.get("/chat/unseen/:fromModelId/:toStaffId", countUnseenMessages); // Count unseen chats
router.put("/chat/mark-read/:fromModelId/:toStaffId", markMessagesAsRead); // Mark chats as read

// ============== NOTIFICATION ROUTES ==============
router.get(
  "/notification/recent/:staffId/:modelId",
  getRecentNotificationFromStaff,
); // Recent notification
router.get(
  "/notification/unseen/:fromStaffId/:toModelId",
  countUnseenNotifications,
); // Count unseen notifications
router.put(
  "/notification/mark-read/:fromStaffId/:toModelId",
  markNotificationsAsRead,
); // Mark notifications as read

export default router;
