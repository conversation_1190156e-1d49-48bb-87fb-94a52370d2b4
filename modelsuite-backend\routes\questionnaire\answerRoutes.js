import express from "express";
import {
  submitTemplateAnswers,
  getModelAnswers,
  getMyAnswers,
  getTemplateAnswers,
} from "../../controllers/questionnaire/answerController.js";
import { verifyToken } from "../../middlewares/authMiddleware.js";

const router = express.Router();

// Model submits answers
router.post("/", verifyToken, submitTemplateAnswers);

// Model fetches their own answers for a template
router.get("/template/:templateId", verifyToken, getMyAnswers);

// Agency gets all answers for a template (for analytics)
router.get("/analytics/:templateId", verifyToken, getTemplateAnswers);

// Agency views a model's answers for a template
router.get("/:modelId/:templateId", verifyToken, getModelAnswers);

export default router;
