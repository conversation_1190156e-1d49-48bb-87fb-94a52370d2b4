// Test script for secure voice download implementation
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5000/api/v1';

// Test credentials - replace with actual test user credentials
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'testpassword'
};

async function testSecureDownload() {
  try {
    console.log('🧪 Testing Secure Voice Download Implementation');
    console.log('================================================');

    // Step 1: Login to get auth token
    console.log('1. Logging in...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(TEST_CREDENTIALS),
    });

    if (!loginResponse.ok) {
      console.log('❌ Login failed. Please update TEST_CREDENTIALS with valid user credentials.');
      console.log('Response:', await loginResponse.text());
      return;
    }

    const loginData = await loginResponse.json();
    const authToken = loginData.data?.token;

    if (!authToken) {
      console.log('❌ No auth token received from login');
      return;
    }

    console.log('✅ Login successful');

    // Step 2: Get list of recordings to find a test recording ID
    console.log('2. Fetching recordings...');
    const recordingsResponse = await fetch(`${BASE_URL}/voice/recordings/agency`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    if (!recordingsResponse.ok) {
      console.log('❌ Failed to fetch recordings');
      console.log('Response:', await recordingsResponse.text());
      return;
    }

    const recordingsData = await recordingsResponse.json();
    const recordings = recordingsData.data?.recordings || [];

    if (recordings.length === 0) {
      console.log('❌ No recordings found for testing');
      return;
    }

    const testRecordingId = recordings[0]._id;
    console.log(`✅ Found ${recordings.length} recordings, using ${testRecordingId} for testing`);

    // Step 3: Test new secure download token generation
    console.log('3. Testing secure download token generation...');
    const tokenResponse = await fetch(`${BASE_URL}/voice/recordings/${testRecordingId}/generate-download-token`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    if (!tokenResponse.ok) {
      console.log('❌ Failed to generate download token');
      console.log('Response:', await tokenResponse.text());
      return;
    }

    const tokenData = await tokenResponse.json();
    const downloadToken = tokenData.data?.downloadToken;

    if (!downloadToken) {
      console.log('❌ No download token received');
      return;
    }

    console.log('✅ Download token generated successfully');
    console.log(`Token expires at: ${tokenData.data.expiresAt}`);

    // Step 4: Test secure download using token
    console.log('4. Testing secure download with token...');
    const downloadResponse = await fetch(`${BASE_URL}/voice/recordings/download/${downloadToken}`, {
      method: 'GET',
      // No Authorization header needed - token is in URL path
    });

    if (!downloadResponse.ok) {
      console.log('❌ Failed to download with token');
      console.log('Response:', await downloadResponse.text());
      return;
    }

    const contentType = downloadResponse.headers.get('content-type');
    const contentLength = downloadResponse.headers.get('content-length');
    
    console.log('✅ Secure download successful');
    console.log(`Content-Type: ${contentType}`);
    console.log(`Content-Length: ${contentLength} bytes`);

    // Step 5: Test that token is single-use (should fail on second attempt)
    console.log('5. Testing single-use token validation...');
    const secondDownloadResponse = await fetch(`${BASE_URL}/voice/recordings/download/${downloadToken}`, {
      method: 'GET',
    });

    if (secondDownloadResponse.ok) {
      console.log('❌ Token was not single-use (security issue!)');
    } else {
      console.log('✅ Token correctly rejected on second use (single-use validation working)');
    }

    // Step 6: Test legacy download still works
    console.log('6. Testing legacy download method...');
    const legacyResponse = await fetch(`${BASE_URL}/voice/recordings/${testRecordingId}/download`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    if (!legacyResponse.ok) {
      console.log('❌ Legacy download method failed');
      console.log('Response:', await legacyResponse.text());
    } else {
      console.log('✅ Legacy download method still works (backward compatibility maintained)');
    }

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ Secure download token generation works');
    console.log('✅ Secure download with token works');
    console.log('✅ Single-use token validation works');
    console.log('✅ Legacy download method still works');
    console.log('\n🔒 Security improvements implemented:');
    console.log('• JWT tokens no longer exposed in URL query parameters');
    console.log('• Temporary download tokens with separate cryptographic secret');
    console.log('• Single-use token validation prevents replay attacks');
    console.log('• Token expiration prevents long-term exposure');
    console.log('• Backward compatibility maintained for existing clients');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
testSecureDownload();
