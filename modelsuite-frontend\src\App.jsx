import { Routes, Route, useNavigate } from "react-router-dom";
import Register from "./pages/Register";
import ModelDashboard from "./pages/Model/Dashboard";
// import Login from "./pages/Model/Login";
import ForgotPassword from "./pages/Model/ForgotPassword";
import ForgotPasswordAgency from "./pages/Agency/ForgotPasswordAgency";
// import AgencyRegister from "./pages/Agency/Register";
import AgencyDashboard from "./pages/Agency/Dashboard";
import AgencyLogin from "./pages/Agency/Login";
import Home from "./pages/Home/Home";
import CreatorInsightsDashboard from "./pages/Agency/ModelView";
import Questionnaires from "./pages/Agency/Questionnaires";
import AgencyLayout from "./layouts/AgencyLayout"; //  Layout that wraps Sidebar + Outlet
import ModelLayout from "./layouts/ModelLayout";
import ProtectedRoute from "./utils/ProtectedRoute";
import InstagramSuccess from "./components/socialMedia/Success";
import TiktokSucess from "./components/socialMedia/TiktokSection";
import QuestionnaireForm from "./components/questionnaire/model/QuestionnaireForm";
import FAQ from "./components/Support/FAQ";
import AgencyBilling from "./pages/Agency/Billing";
import BoardView from "./components/task/board/BoardView";
import Email_support from "./components/Support/email";
import { Suspense, useEffect } from "react";
import AgencyProfile from "./pages/Agency/AgencyProfile";
import MagicVerify from "./pages/MagicLink";
import EmployeeLayout from "./layouts/EmployeeLayout";
import Team from "./pages/Agency/Team";
import ActivateAccount from "./pages/Employee/Activate";
import Settings from "./pages/Agency/Settings";
import AuthLayout from "./components/supportteam/Authlayout.jsx";
import Modelside from "./components/supportteam/Modelside.jsx";
import InvoiceDashboard from "./pages/Agency/InvoiceDashboard";
import SkillMatrixPage from "./pages/Agency/SkillMatrixPage";
import { Toaster } from "react-hot-toast";
import Post from "./pages/Agency/Post";
import axios from "axios";
import Supportcontactmanagement from "./components/supportteam/agency/Supportcontactmanagement.jsx";
import AgencySettings from "./pages/Agency/AgencySettings/AgencySettings.jsx";

function App() {
  // const location = useLocation();
  const baseURL = import.meta.env.VITE_API_BASE_URL;
  const navigate = useNavigate();

  useEffect(() => {
    const refreshAccessTokens = async () => {
      try {
        const response = await axios.post(
          `${baseURL}/refresh-token`,
          {},
          {
            withCredentials: true,
          },
        );
        const sanitizedData = String(response.data).replace(/[\r\n]+/g, "");
        console.log("Token refreshed successfully:", sanitizedData);
      } catch (error) {
        const sanitizedError = String(error).replace(/[\r\n]+/g, "");
        console.error("Token refresh failed:", sanitizedError);

        if (error.response?.status === 401) {
          navigate("/");
        }
      }
    };

    refreshAccessTokens();
  }, []);
  return (
    <>
      <Routes>
        {/* Public routes */}
        <Route path="/" element={<Home />} />
        <Route path="/register" element={<Register />} />
        {/* <Route path="/model/login" element={<Login />} /> */}
        {/* <Route path="/model/register" element={<Register />} /> */}
        <Route path="/magic-verify" element={<MagicVerify />} />
        {/* <Route path="/model/login" element={<Login />} /> */}
        {/* <Route path="/model/register" element={<Register />} /> */}
        <Route path="/model/forgot-password" element={<ForgotPassword />} />
        <Route path="/agency/login" element={<AgencyLogin />} />
        {/* <Route path="/agency/register" element={<AgencyRegister />} /> */}
        <Route
          path="/agency/forgot-password"
          element={<ForgotPasswordAgency />}
        />
        <Route path="/employee/activate" element={<ActivateAccount />} />

        <Route path="/instagram/success" element={<InstagramSuccess />} />
        <Route path="/tiktok/success" element={<TiktokSucess />} />
        <Route path="/support/faqs" element={<FAQ />} />
        <Route
          path="/board/:boardId"
          element={
            <div className="h-screen bg-gray-900">
              <BoardView />
            </div>
          }
        />
        <Route path="/support/email" element={<Email_support />} />

        {/* Protected model routes */}
        <Route element={<ProtectedRoute allowedRole="model" />}>
          <Route element={<ModelLayout />}>
            <Route path="/model/dashboard" element={<ModelDashboard />} />
            <Route path="/model/questionnaires" element={<ModelDashboard />} />
            <Route path="/model/supportsystem" element={<Modelside />} />
            {/* Route for individual questionnaire form */}
            <Route
              path="/model/questionnaire/:assignmentId"
              element={<QuestionnaireForm />}
            />
          </Route>
        </Route>

        {/* Protected agency routes */}
        <Route element={<ProtectedRoute allowedRole="agency" />}>
          <Route element={<AgencyLayout />}>
            <Route
              path="/support-contact-manager"
              element={<Supportcontactmanagement />}
            />
            <Route path="/agency/dashboard" element={<AgencyDashboard />} />
            <Route path="/agency/post" element={<Post />} />
            <Route path="/agency/team" element={<Team />} />
            <Route path="/agency/skill-matrix" element={<SkillMatrixPage />} />
            <Route path="/agency/questionnaires" element={<Questionnaires />} />
            <Route
              path="/agency/questionnaires/templates"
              element={<Questionnaires />}
            />
            <Route
              path="/agency/questionnaires/assignments"
              element={<Questionnaires />}
            />
            <Route
              path="/agency/questionnaires/responses"
              element={<Questionnaires />}
            />
            <Route
              path="/agency/questionnaires/analytics"
              element={<Questionnaires />}
            />
            <Route
              path="/agency/dashboard/profile/:agencyName"
              element={<AgencyProfile />}
            />
            <Route
              path="/agency/dashboard/profile/settings"
              element={<AgencySettings />}
            />
            <Route
              path="/agency/dashboard/customize"
              element={
                <Suspense fallback={<div>Loading Settings...</div>}>
                  <Settings />
                </Suspense>
              }
            />
            <Route
              path="/agency/model-view/:id"
              element={<CreatorInsightsDashboard />}
            />
            <Route path="/invoices/sent" element={<AgencyBilling />} />
            <Route
              path="/invoices/received"
              element={
                <Suspense fallback={<div>Loading Settings...</div>}>
                  <InvoiceDashboard />
                </Suspense>
              }
            />
          </Route>
        </Route>
        <Route element={<ProtectedRoute allowedRole="employee" />}>
          <Route element={<EmployeeLayout />}>
            <Route path="/employee/dashboard" element={<AgencyDashboard />} />
          </Route>
        </Route>

        {/* My support system (employee*/}
        <Route path="/supportsystem" element={<AuthLayout />} />
      </Routes>
      <Toaster />
    </>
  );
}

export default App;
