import mongoose from "mongoose";

const questionSectionSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, "Section title is required"],
      trim: true,
      maxlength: [100, "Section title cannot exceed 100 characters"],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, "Section description cannot exceed 500 characters"],
    },
    isDefault: {
      type: Boolean,
      default: false,
      index: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      default: null, // null for default sections
    },
    isDeleted: {
      type: Boolean,
      default: false,
      index: true,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for performance
questionSectionSchema.index({ isDefault: 1, isDeleted: 1 });
questionSectionSchema.index({ createdBy: 1, isDeleted: 1 });

// Virtual for questions count
questionSectionSchema.virtual("questionsCount", {
  ref: "QuestionTemplate",
  localField: "_id",
  foreignField: "sectionId",
  count: true,
});

// Don't return deleted sections by default
questionSectionSchema.pre(/^find/, function (next) {
  if (!this.getQuery().isDeleted) {
    this.where({ isDeleted: { $ne: true } });
  }
  next();
});

const QuestionSection = mongoose.model("QuestionSection", questionSectionSchema);

export default QuestionSection;
