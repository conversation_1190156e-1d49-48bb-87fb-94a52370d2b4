import multer from "multer";
import path from "path";
import fs from "fs";
import { ApiError } from "../utils/ApiError.js";

/**
 * Enhanced file validation middleware for voice recordings
 */
class VoiceFileValidator {
  constructor() {
    this.allowedMimeTypes = [
      'audio/mpeg',      // MP3
      'audio/mp3',       // MP3 (alternative)
      'audio/wav',       // WAV
      'audio/wave',      // WAV (alternative)
      'audio/x-wav',     // WAV (alternative)
      'audio/ogg',       // OGG
      'audio/mp4',       // M4A
      'audio/m4a',       // M4A
      'audio/webm',      // WebM
      'audio/flac',      // FLAC
    ];

    this.allowedExtensions = ['.mp3', '.wav', '.ogg', '.m4a', '.webm', '.flac'];
    this.maxFileSize = 100 * 1024 * 1024; // 100MB
    this.minFileSize = 1024; // 1KB
    this.maxDuration = 3600; // 1 hour in seconds
    this.minDuration = 1; // 1 second
  }

  /**
   * Validate file type and extension
   */
  validateFileType(file) {
    const fileExtension = path.extname(file.originalname).toLowerCase();
    
    // Check MIME type
    if (!this.allowedMimeTypes.includes(file.mimetype)) {
      throw new ApiError(400, `Invalid file type. Allowed types: ${this.allowedMimeTypes.join(', ')}`);
    }

    // Check file extension
    if (!this.allowedExtensions.includes(fileExtension)) {
      throw new ApiError(400, `Invalid file extension. Allowed extensions: ${this.allowedExtensions.join(', ')}`);
    }

    return true;
  }

  /**
   * Validate file size
   */
  validateFileSize(file) {
    if (file.size > this.maxFileSize) {
      throw new ApiError(400, `File too large. Maximum size: ${this.formatFileSize(this.maxFileSize)}`);
    }

    if (file.size < this.minFileSize) {
      throw new ApiError(400, `File too small. Minimum size: ${this.formatFileSize(this.minFileSize)}`);
    }

    return true;
  }

  /**
   * Validate audio content using basic checks
   */
  async validateAudioContent(filePath) {
    try {
      // Check if file exists and is readable
      if (!fs.existsSync(filePath)) {
        throw new ApiError(400, "Uploaded file not found");
      }

      const stats = fs.statSync(filePath);
      if (stats.size === 0) {
        throw new ApiError(400, "Uploaded file is empty");
      }

      // Basic file header validation for common audio formats
      const buffer = fs.readFileSync(filePath, { start: 0, end: 11 });
      
      // Check for common audio file signatures
      const signature = buffer.toString('hex').toUpperCase();
      const isValidAudio = this.checkAudioSignature(signature);
      
      if (!isValidAudio) {
        throw new ApiError(400, "File does not appear to be a valid audio file");
      }

      return true;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(400, "Error validating audio file: " + error.message);
    }
  }

  /**
   * Check audio file signatures
   */
  checkAudioSignature(signature) {
    const audioSignatures = [
      'ID3',           // MP3 with ID3 tag
      'FFFB',          // MP3 frame sync
      'FFF3',          // MP3 frame sync
      'FFF2',          // MP3 frame sync
      '52494646',      // RIFF (WAV, WebM)
      '4F676753',      // OggS (OGG)
      '664C6143',      // fLaC (FLAC)
      '00000020667479704D344120', // M4A
    ];

    return audioSignatures.some(sig => signature.startsWith(sig));
  }

  /**
   * Sanitize filename
   */
  sanitizeFilename(originalname) {
    // Remove special characters and spaces
    const sanitized = originalname
      .replace(/[^a-zA-Z0-9._-]/g, '_')
      .replace(/_{2,}/g, '_')
      .replace(/^_+|_+$/g, '');
    
    // Ensure filename isn't too long
    const maxLength = 100;
    if (sanitized.length > maxLength) {
      const ext = path.extname(sanitized);
      const name = path.basename(sanitized, ext);
      return name.substring(0, maxLength - ext.length) + ext;
    }

    return sanitized;
  }

  /**
   * Format file size for human reading
   */
  formatFileSize(bytes) {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  /**
   * Create multer file filter
   */
  createFileFilter() {
    return (req, file, cb) => {
      try {
        this.validateFileType(file);
        
        // Sanitize filename
        file.originalname = this.sanitizeFilename(file.originalname);
        
        cb(null, true);
      } catch (error) {
        cb(error, false);
      }
    };
  }

  /**
   * Create multer storage configuration
   */
  createStorage() {
    return multer.diskStorage({
      destination: function (req, file, cb) {
        const uploadDir = "./public/temp/voice";
        
        // Create directory if it doesn't exist
        if (!fs.existsSync(uploadDir)) {
          fs.mkdirSync(uploadDir, { recursive: true });
        }
        
        cb(null, uploadDir);
      },
      filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const sanitizedName = file.originalname.replace(/[^a-zA-Z0-9._-]/g, '_');
        cb(null, `voice-${uniqueSuffix}-${sanitizedName}`);
      }
    });
  }

  /**
   * Create complete multer configuration
   */
  createMulterConfig() {
    return multer({
      storage: this.createStorage(),
      fileFilter: this.createFileFilter(),
      limits: {
        fileSize: this.maxFileSize,
        files: 1,
        fields: 10,
        fieldSize: 1024 * 1024, // 1MB for form fields
      },
    });
  }
}

// Create singleton instance
const voiceFileValidator = new VoiceFileValidator();

export default voiceFileValidator;
export { VoiceFileValidator };
